body {
    margin: 0;
    padding: 0;
    font-family: "Arial", sans-serif;
    background-color: #050a0f;
    color: #d3d3d3;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    /* Performance optimizations */
    will-change: transform;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Smooth transitions */
    transition: opacity 0.3s ease-out;
    /* CSS containment for better performance */
    contain: layout style paint;
}

/* Dashboard loading states - removed grey overlay */
body:not(.dashboard-ready) {
    opacity: 1;
}

body.dashboard-ready {
    opacity: 1;
}

/* Hide delta metric elements during initial loading to prevent visual artifacts */
body:not(.dashboard-ready) .meter-wrapper {
    visibility: hidden;
    opacity: 0;
}

body.dashboard-ready .meter-wrapper {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.3s ease-out, visibility 0s;
}

/* Smooth transitions when enabled */
.smooth-transitions * {
    transition:
        transform 0.2s ease-out,
        opacity 0.15s ease-out;
}

/* Performance mode optimizations */
.performance-mode * {
    animation-duration: 0.1s !important;
    transition-duration: 0.1s !important;
}

/* Low memory mode */
/* Production CSS optimizations - containment for performance */
.crypto-container {
    contain: layout style paint;
    transform: translateZ(0); /* Force GPU acceleration */
}

.chart-container {
    contain: layout paint;
    transform: translateZ(0); /* Force GPU acceleration */
}

.orderbook-canvas {
    contain: strict;
    transform: translateZ(0); /* Force GPU acceleration */
}

.liquidation-message {
    will-change: transform, opacity;
    contain: layout;
}

.main-container {
    contain: layout;
}

.order-books-container {
    contain: layout;
}

.low-memory-mode .chart-container,

/* Loading indicator for overlays and popup chart */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 10;
    font-size: 1rem;
    pointer-events: none;
    text-align: center;
}
.low-memory-mode .orderbook-canvas {
    image-rendering: -webkit-optimize-contrast; /* Edge 79+ */
    image-rendering: pixelated; /* Standard, last */
    contain: strict;
}

/* Loading Progress Indicator */
.loading-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    z-index: 10001;
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

.loading-progress-bar.visible {
    opacity: 1;
}

.loading-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #26a69a, #4db6ac, #80cbc4);
    width: 0%;
    transition: width 0.2s ease-out;
    box-shadow: 0 0 10px rgba(38, 166, 154, 0.6);
    will-change: width;
    contain: layout;
}

.loading-status-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #26a69a;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 10001;
    opacity: 0;
    transform: translateY(20px);
    transition:
        opacity 0.3s ease-out,
        transform 0.3s ease-out;
    -webkit-backdrop-filter: blur(4px); /* Safari/iOS */
    backdrop-filter: blur(4px);
}

.loading-status-indicator.visible {
    opacity: 1;
    transform: translateY(0);
}

.loading-status-indicator::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    border: 2px solid #26a69a;
    border-top: 2px solid transparent;
    border-radius: 50%;
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.main-container {
    display: flex;
    flex-direction: row;
    height: 100%;
    width: 100%;
    overflow: visible !important;
}
.order-books-container {
    flex: 0 0 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
    justify-content: space-between;
}
.console-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
    box-sizing: border-box;
    padding: 10px;
}
.charts-container {
    flex: 1;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0;
    overflow: visible !important;
}
.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    overflow: visible;
    box-sizing: border-box;
    margin-bottom: 0;
    min-width: 0;
    min-height: 0;
    /* Performance optimizations */
    will-change: contents;
    contain: layout style paint;
}

/* Chart transition states */
.chart-switching {
    pointer-events: none;
    -webkit-user-select: none; /* Safari/iOS */
    user-select: none;
}

.chart-fade-out {
    opacity: 0;
    transform: translateY(20px);
    transition:
        opacity 150ms ease-in,
        transform 150ms ease-in;
}

.chart-fade-in {
    opacity: 1;
    transform: translateY(0);
    transition:
        opacity 250ms ease-out,
        transform 250ms ease-out;
}

/* Price chart container transitions */
.price-chart-container {
    transition: opacity 0.3s ease-out;
}

.price-chart-container.switching {
    opacity: 0.7;
}
.pair-selector {
    width: 100%;
    padding: 5px;
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    background-color: #0f141a;
    border-radius: 4px 4px 0 0;
    position: sticky;
    top: 0;
    z-index: 5;
    flex: 0 0 auto;
    overflow: visible !important;
}
.pair-button {
    padding: 5px 10px;
    font-size: 12px;
    color: #d3d3d3;
    background-color: #2a2a2a;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition:
        background-color 0.2s ease-out,
        transform 0.15s ease-out,
        box-shadow 0.2s ease-out;
    margin-left: 0;
    height: 32px;
    min-width: 60px;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    will-change: transform;
    position: relative;
    overflow: hidden;
}

.pair-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.3s ease-out;
}

.pair-button:hover::before {
    left: 100%;
}

.pair-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.pair-button:active {
    transform: translateY(0);
}

.pair-button.active {
    background-color: #2a2a2a;
    border: 1px solid #c0c0c0;
    box-shadow: 0 0 10px rgba(192, 192, 192, 0.3);
    transform: scale(1.05);
}

/* Net Flow floating button container, no background */
.net-flow-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 32px;
    min-width: 90px;
    padding: 0 12px;
    font-size: 15px;
    font-family: inherit;
    background: transparent !important;
    color: #fff;
    border: none;
    border-radius: 4px;
    margin-left: 12px;
    margin-right: 0;
    box-shadow: none;
    position: absolute;
    right: 0;
    top: 0;
    pointer-events: auto;
    cursor: default;
    vertical-align: middle;
    z-index: 10;
}

/* Net Flow window button, matches popup-timeframe-btn */
.net-flow-window-btn {
    padding: 2px 5px;
    font-size: 10px;
    color: #d3d3d3;
    background-color: #2a2a2a;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    transition: background-color 0.2s;
    height: 24px;
    min-width: 32px;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.net-flow-window-btn.active,
.net-flow-window-btn:active {
    background-color: #555555;
}
.net-flow-window-btn:hover {
    background-color: #444444;
}
.pair-button.active {
    background-color: #555555;
}
.pair-button:hover {
    background-color: #444444;
}
.fullscreen-toggle {
    margin-left: auto;
    font-size: 14px;
    transition: all 0.3s ease;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 2px 6px;
    min-width: 34px;
    min-height: 26px;
}
.fullscreen-toggle.active {
    background-color: #555555;
}
.main-container {
    display: flex;
}
.order-books-container {
    display: flex;
}
.price-chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #0f141a;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    box-sizing: border-box;
    padding: 8px;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    min-width: 0;
    flex: 1;
}
.price-chart {
    width: 100%;
    height: 100%;
    min-height: 0;
    min-width: 0;
    flex: 1;
    position: relative;
}
.tv-lightweight-charts {
    width: 100% !important;
    height: 100% !important;
    position: relative;
}
.tv-lightweight-charts .pane-separator {
    background-color: #2a2a2a;
    cursor: row-resize;
    height: 1px;
    position: relative;
    z-index: 2;
}
.tv-lightweight-charts .pane-separator:hover {
    background-color: rgba(255, 0, 0, 0.1);
}
.meter-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.crypto-container {
    flex: 0 0 auto;
    background-color: #0f141a;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    gap: 3px;
    overflow: hidden;
    aspect-ratio: 1 / 1;
    height: calc(23% - 8px);
    margin-bottom: 10px;
}
.orderbook-canvas {
    width: 100%;
    height: 35%;
    margin-top: 6px;
}
.meter-wrapper {
    width: 100%;
    height: 28%;
    padding: 6px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #0f141a;
    border-radius: 0px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    padding-top: 10px;
    margin-bottom: 3px;
}
.meter-wrapper canvas {
    width: 100%;
    height: calc(100% - 10px);
    background-color: transparent;
    border-radius: 0;
}
.meter-title-value {
    text-align: center;
    font-size: 8px;
    line-height: 10px;
    height: 10px;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1;
    color: #bbbbbb;
    background-color: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.bias-text {
    position: absolute;
    top: 2px;
    font-size: 10px !important;
    font-family: "Arial", sans-serif;
    text-align: center;
    line-height: 1.2;
    width: 100%;
    z-index: 1;
    white-space: nowrap;
}
.mid-price-text {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
}
.price-scale {
    width: 100%;
    height: 16%;
    position: absolute;
    top: 2px;
    display: flex;
    justify-content: space-between;
    padding: 0 1vw;
    box-sizing: border-box;
    font-size: 10px;
    font-family: "Arial", sans-serif;
    color: #bbbbbb;
    line-height: 1.2;
    z-index: 1;
}
.price-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    white-space: nowrap;
}
.metric-title {
    color: #bbbbbb;
    display: inline;
    text-transform: uppercase;
    font-size: 12px !important;
    font-family: "Arial", sans-serif;
    line-height: 1.2;
}
.metric-value {
    color: #bbbbbb;
    display: inline;
    font-size: 12px !important;
    font-family: "Arial", sans-serif;
    line-height: 1.2;
}
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(15, 20, 26, 0.3),
        rgba(10, 15, 20, 0.4)
    );
    -webkit-backdrop-filter: blur(1px); /* Safari/iOS */
    backdrop-filter: blur(1px);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #d3d3d3;
    font-size: 14px;
    z-index: 10;
    transition:
        opacity 0.3s ease-out,
        visibility 0.3s ease-out;
    opacity: 0.6;
    visibility: visible;
}

.loading-overlay.fade-out {
    opacity: 0;
    visibility: hidden;
}

.loading-overlay::before {
    content: "";
    position: absolute;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(38, 166, 154, 0.3);
    border-top: 3px solid #26a69a;
    border-radius: 50%;
    margin-right: 10px;
    animation: spin 1s linear infinite;
}

.loading-overlay::after {
    content: attr(data-loading-text);
    margin-left: 50px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Smooth loading animations */
.simple-loading-enter {
    opacity: 0;
    transform: translateY(-10px);
    animation: slideInDown 0.3s ease-out forwards;
}

.simple-loading-exit {
    opacity: 1;
    animation: slideOutUp 0.3s ease-in forwards;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Pair button hover improvements */
.pair-button {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.pair-button:not(.active):hover {
    background-color: rgba(42, 42, 42, 0.8);
    transform: translateY(-1px) translateZ(0);
}

.pair-button.active {
    background-color: #2a2a2a;
    border: 1px solid #c0c0c0;
    box-shadow: 0 2px 8px rgba(192, 192, 192, 0.3);
    transform: translateZ(0);
}
.first-meter {
    margin-top: 0;
}
.hidden-value {
    display: none;
}
.liq-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
    z-index: 100;
    padding: 5px 10px;
    background-color: rgba(15, 20, 26, 0.8);
    box-sizing: border-box;
    margin: 0;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}
.liq-controls-left {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}
.liq-controls-right {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}
.settings-dropdown {
    position: relative;
    display: inline-block;
    margin: 0;
}

.settings-btn {
    padding: 5px 10px;
    font-size: 12px;
    color: #d3d3d3;
    background-color: #2a2a2a;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition:
        background-color 0.2s ease-out,
        transform 0.15s ease-out,
        box-shadow 0.2s ease-out;
    margin-left: 0;
    height: 32px;
    min-width: 60px;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    will-change: transform;
    position: relative;
    overflow: hidden;
}
.settings-btn:hover {
    background-color: #444444;
}
.settings-btn:active {
    background-color: #2a2a2a;
}
.settings-dropdown-content {
    display: none;
    position: absolute;
    bottom: 100%;
    right: 0;
    background-color: rgba(30, 30, 30, 0.95);
    min-width: 250px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.5);
    z-index: 101;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 5px;
}
.settings-dropdown-content.show {
    display: block;
}
.settings-group {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(80, 80, 80, 0.5);
}
.settings-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}
.settings-group-title {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 5px;
}
.settings-group-content {
    display: flex;
    align-items: center;
    gap: 5px;
}
.controls-group {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: center;
    padding: 0 2px;
}
.controls-group:first-child {
    padding-left: 12px;
    justify-content: flex-start;
}
.controls-group:last-child {
    padding-right: 12px;
    justify-content: flex-end;
}
.console-capture {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    background-color: rgba(15, 20, 26, 1);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    font-family: "Courier New", monospace;
    font-size: 11px;
}
.console-capture::-webkit-scrollbar {
    width: 4px;
}
.console-capture::-webkit-scrollbar-track {
    background: #111;
}
.console-capture::-webkit-scrollbar-thumb {
    background-color: #333;
    border-radius: 2px;
}
.console-capture-title {
    text-align: center;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.9);
    padding: 4px 0;
    margin: 0;
    font-weight: normal;
    font-family: sans-serif;
    border-bottom: 2px solid rgba(100, 100, 100, 0.7);
    position: sticky;
    top: 0;
    background-color: rgba(15, 20, 26, 1);
    z-index: 10;
    height: 16px;
    line-height: 16px;
    box-sizing: content-box;
    width: 100%;
}
.liquidation-message {
    padding: 3px 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
    line-height: 1.2;
    position: relative;
    z-index: 0;
}
.liquidation-message.long,
.liquidation-message.sell,
.liquidation-message.whale-sell,
.liquidation-message.bybit-liquidation-long {
    color: #ff5555 !important;
}
.liquidation-message.short,
.liquidation-message.buy,
.liquidation-message.whale-buy,
.liquidation-message.bybit-liquidation-short {
    color: #00ffff !important;
}
.liquidation-message.test {
    color: #ffffff;
}
.liquidation-message.whale {
    color: #ffd700;
    font-weight: bold;
}
#console-title-line {
    display: block;
    width: 85px;
    height: 1px;
}
.popup-chart-wrapper {
    position: absolute;
    /* top: 10px; */
    /* left: 10px; */
    width: 400px;
    height: 300px;
    z-index: 10;
    background-color: rgba(15, 20, 26, 1);
    border: 1px solid rgba(100, 100, 100, 0.7);
    overflow: hidden;
    flex-direction: column;
    min-width: 200px;
    min-height: 120px;
    max-width: 1200px;
    max-height: 90vh;
    resize: both;
    display: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border-radius: 0;
    transition: none !important;
    will-change: width, height;
}
.popup-chart-wrapper.visible {
    display: flex;
}
.popup-chart-container {
    height: calc(100% - 30px);
    width: 100%;
    flex: 1;
    background-color: rgba(15, 20, 26, 1);
    position: relative;
    min-height: 0;
    min-width: 0;
}
.popup-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    padding: 0 10px;
    background-color: rgba(15, 20, 26, 1);
    border-bottom: 1px solid rgba(100, 100, 100, 0.7);
}
.popup-chart-timeframes {
    display: flex;
    gap: 5px;
}
.popup-timeframe-btn {
    padding: 2px 5px;
    font-size: 10px;
    color: #d3d3d3;
    background-color: #2a2a2a;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    transition: background-color 0.2s;
}
.popup-timeframe-btn.active {
    background-color: #555555;
}
.popup-timeframe-btn:hover {
    background-color: #444444;
}
.popup-chart-controls {
    display: flex;
    gap: 5px;
}
.popup-chart-toggle {
    padding: 2px 5px;
    font-size: 10px;
    color: #d3d3d3;
    background-color: #2a2a2a;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    transition: background-color 0.2s;
}
.popup-chart-toggle:hover {
    background-color: #444444;
}
.popup-chart-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    cursor: nwse-resize;
    background-color: #555555;
    border-top-left-radius: 2px;
}
.tradingview-widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 20px;
    padding: 0 5px;
    background-color: rgba(30, 34, 45, 0.9);
    border-bottom: 1px solid rgba(100, 100, 100, 0.7);
    cursor: move;
}
.tradingview-widget-timeframes {
    display: flex;
    gap: 2px;
}
.tv-timeframe-btn {
    background: none;
    border: none;
    color: #aaa;
    font-size: 10px;
    padding: 0 3px;
    cursor: pointer;
}
.tv-timeframe-btn:hover {
    color: #fff;
}
.tv-timeframe-btn.active {
    color: #fff;
    font-weight: bold;
}
.tradingview-widget-controls {
    display: flex;
}
.tradingview-widget-toggle {
    background: none;
    border: none;
    color: #aaa;
    font-size: 12px;
    cursor: pointer;
    padding: 0 3px;
}
.tradingview-widget-toggle:hover {
    color: #fff;
}
.tradingview-widget-resize-handle {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: linear-gradient(
        135deg,
        transparent 50%,
        rgba(100, 100, 100, 0.5) 50%
    );
    z-index: 20;
}
.tradingview-widget-container.collapsed {
    height: 24px !important;
    overflow: hidden;
}
.tradingview-widget-container.collapsed .tradingview-widget-toggle {
    transform: rotate(180deg);
}
#tradingview-toggle-btn.active {
    background-color: #555555;
}
.tradingview-widget-resize-handle {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: linear-gradient(
        135deg,
        transparent 50%,
        rgba(100, 100, 100, 0.5) 50%
    );
    z-index: 20;
}
.tradingview-widget-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 20px;
    padding: 0 4px;
    background-color: rgba(15, 20, 26, 1);
    border-bottom: 1px solid rgba(100, 100, 100, 0.7);
    cursor: move;
    -webkit-user-select: none;
    user-select: none;
}
.tradingview-widget-timeframes {
    display: flex;
    gap: 2px;
    margin-right: auto;
}
.tradingview-widget-controls {
    display: flex;
    gap: 4px;
}
.tv-timeframe-btn {
    padding: 2px 4px;
    font-size: 10px;
    color: #d3d3d3;
    background-color: #2a2a2a;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
}
.tv-timeframe-btn.active {
    background-color: #555555;
}
.tv-timeframe-btn:hover {
    background-color: #444444;
}
.tradingview-widget-toggle {
    background: none;
    border: none;
    color: #d3d3d3;
    font-size: 14px;
    cursor: pointer;
    padding: 0 4px;
    line-height: 1;
}
.tradingview-widget-toggle:hover {
    color: #ffffff;
}
.tradingview-widget-container__widget {
    height: calc(100% - 20px);
    width: 100%;
    flex: 1;
    background-color: rgba(15, 20, 26, 1);
}
.tradingview-widget-container__widget iframe {
    background-color: #0f141a !important;
}
.tradingview-widget-container__widget iframe body {
    background-color: #0f141a !important;
}
.tradingview-widget-container__widget iframe .chart-markup-table.pane {
    border: none !important;
    background-color: #0f141a !important;
}
.tradingview-widget-container__widget iframe .chart-markup-table.pane canvas {
    background-color: #0f141a !important;
}
.tradingview-widget-container__widget iframe .chart-container {
    background-color: #0f141a !important;
}
.tradingview-widget-container__widget iframe .chart-container canvas {
    background-color: #0f141a !important;
}
.tradingview-widget-container__widget iframe .chart-markup-grid {
    display: none !important;
}
.tradingview-widget-container__widget iframe .chart-markup-grid line {
    stroke: transparent !important;
    stroke-width: 0 !important;
}
.tradingview-widget-container__widget iframe .chart-status-line {
    display: none !important;
}
.tradingview-widget-container__widget iframe .chart-watermark {
    display: none !important;
}
.tradingview-widget-container__widget iframe .time-axis {
    display: none !important;
}
.tradingview-widget-container * {
    background-color: #0f141a !important;
}
.custom-tradingview-theme {
    background-color: #0f141a !important;
}
.custom-tradingview-theme iframe {
    background-color: #0f141a !important;
}
.custom-tradingview-theme iframe body,
.custom-tradingview-theme iframe .chart-page,
.custom-tradingview-theme iframe .chart-container-border,
.custom-tradingview-theme iframe .chart-container {
    background-color: #0f141a !important;
}
.tradingview-widget-container__widget iframe .chart-toolbar,
.tradingview-widget-container__widget iframe .chart-toolbar-container,
.tradingview-widget-container__widget iframe .drawing-toolbar,
.tradingview-widget-container__widget iframe .drawing-toolbar-content,
.tradingview-widget-container__widget iframe .group-wWM3zP_M-,
.tradingview-widget-container__widget iframe .chart-toolbar-container div,
.tradingview-widget-container__widget iframe .chart-toolbar div,
.tradingview-widget-container__widget iframe .drawing-toolbar div,
.tradingview-widget-container__widget iframe .drawing-toolbar-content div {
    background-color: #0f141a !important;
    border-color: #2a2a2a !important;
}
.tradingview-widget-container__widget iframe .chart-toolbar button,
.tradingview-widget-container__widget iframe .drawing-toolbar button,
.tradingview-widget-container__widget iframe .button-2ioYhFEY-,
.tradingview-widget-container__widget iframe .button-263WXsg--,
.tradingview-widget-container__widget iframe .button-1SoiPS-f-,
.tradingview-widget-container__widget iframe .button-1WS3BKaz- {
    background-color: #0f141a !important;
    color: #d3d3d3 !important;
    border-color: #2a2a2a !important;
}
.tradingview-widget-container__widget iframe .chart-markup-grid,
.tradingview-widget-container__widget iframe .chart-markup-grid line,
.tradingview-widget-container__widget iframe .chart-grid,
.tradingview-widget-container__widget iframe .chart-grid line,
.tradingview-widget-container__widget iframe .grid-1kUj-Nxm-,
.tradingview-widget-container__widget iframe .grid-1kUj-Nxm- line {
    display: none !important;
    stroke: transparent !important;
    stroke-width: 0 !important;
    opacity: 0 !important;
}
.tradingview-widget-container__widget iframe .price-axis,
.tradingview-widget-container__widget iframe .time-axis {
    background-color: rgba(15, 20, 26, 1) !important;
    border: none !important;
}
.tradingview-widget-container__widget iframe .tv-tooltip,
.tradingview-widget-container__widget iframe .tv-floating-toolbar {
    background-color: rgba(30, 34, 45, 0.9) !important;
    border: 1px solid rgba(100, 100, 100, 0.7) !important;
}
.tradingview-widget-container__widget iframe {
    --tv-color-platform-background: rgba(15, 20, 26, 1) !important;
    --tv-color-pane-background: rgba(15, 20, 26, 1) !important;
    --tv-color-pane-background-secondary: rgba(15, 20, 26, 1) !important;
    --tv-color-toolbar-button-background-hover: rgba(15, 20, 26, 1) !important;
}
.tradingview-widget-container__widget iframe .tv-watermark,
.tradingview-widget-container__widget iframe .tv-logo-toolbar,
.tradingview-widget-container__widget iframe [class*="tv-logo"],
.tradingview-widget-container__widget iframe [class*="trademark"],
.tradingview-widget-container__widget iframe [class*="watermark"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}
.tradingview-widget-resize-handle {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: linear-gradient(
        135deg,
        transparent 50%,
        rgba(100, 100, 100, 0.5) 50%
    );
    z-index: 20;
}
.tradingview-widget-container.collapsed {
    height: 24px !important;
    overflow: hidden;
}
.tradingview-widget-container.collapsed .tradingview-widget-toggle {
    transform: rotate(180deg);
}
.crypto-container:first-child {
    margin-top: 0;
}
.crypto-container:last-child {
    margin-bottom: 0;
}
.dropdown {
    position: relative;
    display: inline-block;
}
.dropdown-toggle {
    position: relative;
    padding-right: 18px !important;
}
.dropdown-toggle::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #d3d3d3;
}
.dropdown-content {
    display: none;
    position: absolute;
    background-color: #1a1a1a;
    min-width: 120px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    border-radius: 4px;
    top: 100%;
    left: 0;
    /* Ensure dropdown is not clipped */
    position: absolute !important;
    z-index: 9999 !important;
}
.dropdown-content.show {
    display: block !important;
}
.dropdown-content button {
    width: 100%;
    text-align: left;
    padding: 8px 10px;
    font-size: 12px;
    color: #d3d3d3;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}
.dropdown-content button:hover {
    background-color: #444444;
}
.dropdown-content button.active {
    background-color: #555555;
}
.dropdown-item {
    width: 100%;
    text-align: left;
    padding: 8px 10px;
    font-size: 12px;
    color: #d3d3d3;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    display: block;
}
.dropdown-item:hover {
    background-color: #444444;
}
.dropdown-item.active {
    background-color: #555555;
}
@media (max-width: 768px) {
    .dropdown-content {
        max-height: 200px;
        min-width: 100px;
    }
}
.clear-liq-btn {
    background-color: rgba(40, 40, 40, 0.7);
    color: #fff;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s;
    white-space: nowrap;
    min-width: 60px;
    margin: 0;
}
.clear-liq-btn:hover {
    background-color: rgba(60, 60, 60, 0.8);
}
.liq-threshold-input {
    background-color: rgba(40, 40, 40, 0.7);
    color: #fff;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 5px 8px;
    width: 70px;
    font-size: 12px;
    margin: 0 2px;
}
.liq-apply-btn {
    background-color: rgba(40, 40, 40, 0.7);
    color: #fff;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s;
    white-space: nowrap;
    min-width: 70px;
}
.liq-apply-btn:hover {
    background-color: rgba(60, 60, 60, 0.8);
}

/* Popup chart styles */

.popup-chart-wrapper-hidden {
    display: none;
}
