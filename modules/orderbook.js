// Dedicated Bitstamp WebSocket manager for BTC (orderbook.js only)
// Production optimized with connection pooling and error recovery
class BitstampBTCWsManager {
  constructor() {
    this.ws = null;
    this.connected = false;
    this.subscriptions = {};
    this.handlers = {};
    this.connecting = false;
    this.url = "wss://ws.bitstamp.net";
    this._reconnectTimeout = null;
    this._reconnectDelay = 2000;
    this._maxReconnectDelay = 30000;
    this._connectionAttempts = 0;
    this._lastMessageTime = 0;
    this._heartbeatInterval = null;
    this._messageQueue = [];
    this._isHealthy = true;
  }
  isConnected() {
    return this.connected;
  }
  connect() {
    if (this.connected || this.connecting) return;
    this.connecting = true;
    this._connectionAttempts++;
    console.log(
      `[BitstampBTCWsManager] Connecting to ${this.url} (attempt ${this._connectionAttempts})`,
    );

    // Cleanup existing connection
    if (this.ws) {
      this.ws.onopen = null;
      this.ws.onclose = null;
      this.ws.onerror = null;
      this.ws.onmessage = null;
      this.ws.close();
    }

    this.ws = new WebSocket(this.url);
    this.ws.onopen = () => {
      console.log(`[BitstampBTCWsManager] Connected successfully`);
      this.connected = true;
      this.connecting = false;
      this._reconnectDelay = 2000;
      this._connectionAttempts = 0;
      this._lastMessageTime = Date.now();
      this._isHealthy = true;

      // Start heartbeat monitoring
      this._startHeartbeat();

      // Process queued messages
      this._processMessageQueue();

      Object.keys(this.subscriptions).forEach((channel) => {
        console.log(`[BitstampBTCWsManager] Subscribing to ${channel}`);
        this._sendMessage({ event: "bts:subscribe", data: { channel } });
      });
    };
    this.ws.onclose = (event) => {
      console.log(
        `[BitstampBTCWsManager] Connection closed: ${event.code} ${event.reason}`,
      );
      this.connected = false;
      this.connecting = false;
      this._isHealthy = false;
      this._stopHeartbeat();
      this._scheduleReconnect();
    };
    this.ws.onerror = (error) => {
      console.error(`[BitstampBTCWsManager] WebSocket error:`, error);
      this.connected = false;
      this.connecting = false;
      this._isHealthy = false;
      this._stopHeartbeat();
      this._scheduleReconnect();
    };
    this.ws.onmessage = (msg) => {
      this._lastMessageTime = Date.now();
      this._isHealthy = true;

      let data;
      try {
        data = JSON.parse(msg.data);
      } catch (error) {
        console.warn(`[BitstampBTCWsManager] Failed to parse message:`, error);
        return;
      }

      const channel = data.channel;
      if (channel && this.handlers[channel]) {
        // Use requestIdleCallback for better performance if available
        const processHandlers = () => {
          try {
            this.handlers[channel].forEach((fn) => fn(data));
          } catch (error) {
            console.error(
              `[BitstampBTCWsManager] Handler error for ${channel}:`,
              error,
            );
          }
        };

        if (window.requestIdleCallback) {
          window.requestIdleCallback(processHandlers, { timeout: 16 });
        } else {
          processHandlers();
        }
      }
    };
  }

  _startHeartbeat() {
    this._stopHeartbeat();
    this._heartbeatInterval = setInterval(() => {
      const timeSinceLastMessage = Date.now() - this._lastMessageTime;
      if (timeSinceLastMessage > 60000) {
        // 60 seconds without messages
        console.warn(
          `[BitstampBTCWsManager] No messages received for ${timeSinceLastMessage}ms, reconnecting`,
        );
        this._isHealthy = false;
        this.reconnect();
      }
    }, 30000); // Check every 30 seconds
  }

  _stopHeartbeat() {
    if (this._heartbeatInterval) {
      clearInterval(this._heartbeatInterval);
      this._heartbeatInterval = null;
    }
  }

  _sendMessage(message) {
    if (this.connected && this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error(`[BitstampBTCWsManager] Failed to send message:`, error);
        this._messageQueue.push(message);
        return false;
      }
    } else {
      this._messageQueue.push(message);
      return false;
    }
  }

  _processMessageQueue() {
    while (this._messageQueue.length > 0 && this.connected) {
      const message = this._messageQueue.shift();
      if (!this._sendMessage(message)) {
        // Put it back at the front if send failed
        this._messageQueue.unshift(message);
        break;
      }
    }
  }

  _scheduleReconnect() {
    if (this._reconnectTimeout) clearTimeout(this._reconnectTimeout);

    // Exponential backoff with jitter
    const jitter = Math.random() * 1000;
    const delay = Math.min(
      this._reconnectDelay + jitter,
      this._maxReconnectDelay,
    );

    console.log(`[BitstampBTCWsManager] Scheduling reconnect in ${delay}ms`);

    this._reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);

    this._reconnectDelay = Math.min(
      this._reconnectDelay * 1.5,
      this._maxReconnectDelay,
    );
  }

  reconnect() {
    console.log(`[BitstampBTCWsManager] Manual reconnect triggered`);
    if (this._reconnectTimeout) {
      clearTimeout(this._reconnectTimeout);
      this._reconnectTimeout = null;
    }
    this.close();
    setTimeout(() => this.connect(), 100);
  }
  subscribe(channel, handler) {
    if (!this.subscriptions[channel]) {
      this.subscriptions[channel] = true;
      this.handlers[channel] = [];
      console.log(`[BitstampBTCWsManager] Adding subscription for ${channel}`);
      if (this.connected && this.ws) {
        console.log(
          `[BitstampBTCWsManager] Sending subscription for ${channel}`,
        );
        this._sendMessage({ event: "bts:subscribe", data: { channel } });
      }
    }
    if (handler && typeof handler === "function") {
      if (this.handlers[channel] && this.handlers[channel].includes(handler))
        return;
      this.handlers[channel].push(handler);
    }
  }
  unsubscribe(channel) {
    if (this.subscriptions[channel]) {
      delete this.subscriptions[channel];
      delete this.handlers[channel];
      if (this.connected && this.ws) {
        this._sendMessage({ event: "bts:unsubscribe", data: { channel } });
      }
    }
  }

  close() {
    console.log(`[BitstampBTCWsManager] Closing connection`);
    this._stopHeartbeat();
    if (this._reconnectTimeout) {
      clearTimeout(this._reconnectTimeout);
      this._reconnectTimeout = null;
    }
    if (this.ws) {
      this.ws.onopen = null;
      this.ws.onclose = null;
      this.ws.onerror = null;
      this.ws.onmessage = null;
      this.ws.close();
      this.ws = null;
    }
    this.connected = false;
    this.connecting = false;
    this._isHealthy = false;
    this.subscriptions = {};
    this.handlers = {};
    this._messageQueue = [];
  }

  isHealthy() {
    return this._isHealthy && this.connected;
  }

  getConnectionStats() {
    return {
      connected: this.connected,
      healthy: this._isHealthy,
      attempts: this._connectionAttempts,
      lastMessage: this._lastMessageTime,
      queuedMessages: this._messageQueue.length,
      subscriptions: Object.keys(this.subscriptions).length,
    };
  }
}

// Utility functions - using centralized utilities with performance optimizations
const utils = {
  debounce:
    window.commonUtils?.debounce ||
    ((fn, delay) => {
      let timeout;
      return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => fn.apply(this, args), delay);
      };
    }),
  throttle:
    window.commonUtils?.throttle ||
    ((fn, delay) => {
      let lastCall = 0;
      return (...args) => {
        const now = Date.now();
        if (now - lastCall >= delay) {
          lastCall = now;
          return fn.apply(this, args);
        }
      };
    }),
  isElementInViewport:
    window.commonUtils?.isElementVisible ||
    ((el) => {
      if (!el) return false;
      const rect = el.getBoundingClientRect();
      return (
        rect.top < window.innerHeight &&
        rect.bottom > 0 &&
        rect.left < window.innerWidth &&
        rect.right > 0
      );
    }),
  setTextIfChanged: (element, text) => {
    if (element && element.textContent !== text) element.textContent = text;
  },
  // Performance optimized canvas clearing
  clearCanvas: (canvas, ctx) => {
    const dpr = window.devicePixelRatio || 1;
    ctx.clearRect(0, 0, canvas.width / dpr, canvas.height / dpr);
  },
  // Batch DOM updates
  batchDOMUpdates: (updates) => {
    requestAnimationFrame(() => {
      updates.forEach((update) => update());
    });
  },
};

// Order book utility functions
const getLargestBlock = (orders) =>
  orders.length
    ? orders.reduce((max, [price, volume]) => {
        const dollarValue = price * volume;
        return dollarValue > (max?.dollarValue || -Infinity)
          ? { price, volume, dollarValue }
          : max;
      }, null)
    : null;

const hashOrderBook = (orderBookData, lastPrice) =>
  `${orderBookData.bids.slice(0, 5).join("|")}-${orderBookData.asks.slice(0, 5).join("|")}-${lastPrice || "unloaded"}`;

// Local chart creation function
const createLightweightChart = (container, options = {}) => {
  if (!window.LightweightCharts) {
    console.error("LightweightCharts library not loaded");
    return null;
  }
  const chartColors = window.CONFIG?.chart?.defaultColors || {
    background: "#131722",
    text: "#D9D9D9",
    grid: "rgba(42, 46, 57, 0.5)",
  };
  const candleStickColors = window.CONFIG?.chart?.candlestick || {
    upColor: "#AAAAAA",
    downColor: "#AAAAAA",
    borderColor: "#AAAAAA",
    wickUpColor: "#AAAAAA",
    wickDownColor: "#AAAAAA",
  };

  const defaultOptions = {
    autoSize: true,
    layout: {
      background: { type: "solid", color: chartColors.background },
      textColor: chartColors.text,
      fontSize: 10,
      fontFamily: "Trebuchet MS, Roboto, Ubuntu, sans-serif",
      attributionLogo: false,
    },
    grid: {
      vertLines: { color: chartColors.grid },
      horzLines: { color: chartColors.grid },
    },
    timeScale: { visible: false },
    rightPriceScale: {
      visible: true,
      borderColor: chartColors.grid,
      scaleMargins: { top: 0.1, bottom: 0.1 },
      entireTextOnly: false,
      ticksVisible: true,
      formatPrice: (price) => Math.floor(price).toString(),
      minMove: 1,
      precision: 0,
    },
    crosshair: {
      mode: 1, // Assuming CrosshairMode.Normal or similar if LightweightCharts object is available
      vertLine: {
        visible: true,
        color: chartColors.text, // Or a specific crosshair color from config
        width: 1,
        style: 3, // LineStyle.Dotted
        labelBackgroundColor: chartColors.background,
      },
      horzLine: {
        visible: true,
        color: chartColors.text, // Or a specific crosshair color from config
        width: 1,
        style: 3, // LineStyle.Dotted
        labelBackgroundColor: chartColors.background,
      },
    },
    handleScroll: { vertTouchDrag: false },
  };
  const chartOptions = { ...defaultOptions, ...options };
  try {
    const chart = window.LightweightCharts.createChart(container, chartOptions);
    const series = chart.addSeries(window.LightweightCharts.CandlestickSeries, {
      ...candleStickColors,
      lastValueVisible: true,
      priceLineVisible: true,
      priceLineSource: window.LightweightCharts.PriceLineSource.LastBar,
      priceFormat: {
        type: "price",
        precision: 0,
        minMove: 1,
        formatter: (price) =>
          window.commonUtils &&
          typeof window.commonUtils.formatLargeNumber === "function"
            ? window.commonUtils.formatLargeNumber(price)
            : Math.floor(price).toString(),
      },
    });
    return { chart, series };
  } catch (error) {
    console.error("Error creating lightweight chart:", error);
    return null;
  }
};

// Crypto module creation function
const createCryptoModule = (symbol, config, elements) => {
  if (!elements.container || !elements.orderbookCanvas) {
    console.error(`Missing elements for ${symbol}`);
    return null;
  }
  // Each module gets its own WebSocket manager
  const wsManager = new BitstampBTCWsManager();
  const safeElements = {
    container: elements.container,
    orderbookCanvas: elements.orderbookCanvas,
    balancePercent: elements.balancePercent,
    tickerName: elements.tickerName,
    minPrice: elements.minPrice,
    midPrice: elements.midPrice,
    maxPrice: elements.maxPrice,
    lowestPrice: elements.lowestPrice,
    highestPrice: elements.highestPrice,
    loadingOverlay: elements.loadingOverlay,
  };
  const state = {
    wsManager, // Store the manager in state
    orderBookData: { bids: [], asks: [] },
    lastPrice: null,
    lastPriceUpdateTime: Date.now(),
    persistentBlocks: { lowestBid: null, highestAsk: null },
    lastOrderBookHash: 0,
    cachedDisplay: { bids: null, asks: null },
    lastFilterPrice: null,
    isBitstampReady: false,
    needsUpdate: false,
    lastFullDrawTime: 0,
    consecutiveHighLoadFrames: 0,
    alternateUpdates: true,
    enabled: true,
    chartData: { candles: [] },
    charts: {},
    resizing: false,
    currentBarUpdateToken: 0,
  };
  const contexts = { orderbook: safeElements.orderbookCanvas.getContext("2d") };

  // Performance optimization: cache context properties
  const ctx = contexts.orderbook;
  ctx.imageSmoothingEnabled = false; // Better performance for financial charts

  // IMPROVED FIX: Use visibility instead of opacity for better performance
  safeElements.orderbookCanvas.style.visibility = 'hidden';
  safeElements.orderbookCanvas.style.transition = 'visibility 0s, opacity 0.3s ease-out';
  safeElements.orderbookCanvas.style.opacity = '0';

  const updateTickerName = () =>
    (safeElements.tickerName.textContent = `${symbol} Δ: `);
  updateTickerName();
  const updateOrderBookExtremes = () => {
    if (
      !state.orderBookData?.bids?.length ||
      !state.orderBookData?.asks?.length
    )
      return;
    const lowestBidPrice = state.orderBookData.bids.at(-1)[0],
      highestAskPrice = state.orderBookData.asks.at(-1)[0];
    state.persistentBlocks.lowestBid = Math.min(
      state.persistentBlocks.lowestBid ?? Infinity,
      lowestBidPrice,
    );
    state.persistentBlocks.highestAsk = Math.max(
      state.persistentBlocks.highestAsk ?? -Infinity,
      highestAskPrice,
    );
    const resetThreshold =
      window.CONFIG?.orderbook?.extremesResetThreshold || 0.05;
    if (state.persistentBlocks.lastResetPrice) {
      const priceChange =
        Math.abs(state.lastPrice - state.persistentBlocks.lastResetPrice) /
        state.persistentBlocks.lastResetPrice;
      if (priceChange > resetThreshold) {
        state.persistentBlocks.lowestBid = lowestBidPrice;
        state.persistentBlocks.highestAsk = highestAskPrice;
        state.persistentBlocks.lastResetPrice = state.lastPrice;
      }
    } else state.persistentBlocks.lastResetPrice = state.lastPrice;
  };
  const drawOrderBookBars = () => {
    // Early exit optimizations
    if (!state.enabled || !safeElements.orderbookCanvas) return;

    const currentHash = hashOrderBook(state.orderBookData, state.lastPrice);
    if (currentHash === state.lastOrderBookHash && !window._isResizing) return;

    if (!utils.isElementInViewport(safeElements.orderbookCanvas)) return;

    state.lastOrderBookHash = currentHash;

    const ctx = contexts.orderbook;
    const dpr = window.devicePixelRatio || 1;

    // Performance optimized clearing
    utils.clearCanvas(safeElements.orderbookCanvas, ctx);

    // IMPROVED FIX: Only show loading state if canvas is visible
    if (!state.lastPrice) {
      if (safeElements.orderbookCanvas.style.visibility === 'visible') {
        ["minPrice", "maxPrice", "midPrice"].forEach(
          (id) =>
            safeElements[id] &&
            utils.setTextIfChanged(safeElements[id], "Loading..."),
        );
        ["lowestPrice", "highestPrice", "balancePercent"].forEach(
          (id) =>
            safeElements[id] && utils.setTextIfChanged(safeElements[id], ""),
        );
      }
      return;
    }
    const rangePercentage = window.CONFIG?.orderbook?.rangePercentage || 0.01;
    const rangeSize = state.lastPrice * rangePercentage,
      minPrice = state.lastPrice - rangeSize,
      maxPrice = state.lastPrice + rangeSize;
    const biasBids = state.orderBookData.bids.filter(
      ([price]) => price >= state.lastPrice * 0.95 && price <= state.lastPrice,
    );
    const biasAsks = state.orderBookData.asks.filter(
      ([price]) => price >= state.lastPrice && price <= state.lastPrice * 1.05,
    );
    const bidValue = biasBids.reduce((sum, [p, v]) => sum + p * v, 0),
      askValue = biasAsks.reduce((sum, [p, v]) => sum + p * v, 0);
    const totalValue = bidValue + askValue,
      balancePercent = totalValue
        ? ((bidValue - askValue) / totalValue) * 100
        : 0;
    const largestBid = getLargestBlock(state.orderBookData.bids),
      largestAsk = getLargestBlock(state.orderBookData.asks);
    const obColors = window.CONFIG?.orderbook?.colors || {
      strokeStyle: "#1c2526",
      positiveDynamic: "rgba(0, 255, 255, 0.75)",
      negativeDynamic: "rgba(255, 85, 85, 0.75)",
      midPriceText: "#BBBBBB",
      barFill: "rgba(170, 170, 170, 0.6)",
    };
    ctx.strokeStyle = obColors.strokeStyle;
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(safeElements.orderbookCanvas.width / 2, 0);
    ctx.lineTo(
      safeElements.orderbookCanvas.width / 2,
      safeElements.orderbookCanvas.height,
    );
    ctx.stroke();
    const dynamicColor =
      balancePercent >= 0 ? obColors.positiveDynamic : obColors.negativeDynamic;
    safeElements.tickerName.style.color = dynamicColor;
    utils.setTextIfChanged(
      safeElements.balancePercent,
      `${Math.round(balancePercent)}%`,
    );
    safeElements.balancePercent.style.color = dynamicColor;
    safeElements.balancePercent.style.fontSize = "24px";
    safeElements.midPrice.parentElement.style.color = obColors.midPriceText;
    if (state.lastFilterPrice !== state.lastPrice) {
      state.cachedDisplay.bids = state.orderBookData.bids
        .filter(([price]) => price >= minPrice && price <= state.lastPrice)
        .sort((a, b) => b[0] - a[0]);
      state.cachedDisplay.asks = state.orderBookData.asks
        .filter(([price]) => price >= state.lastPrice && price <= maxPrice)
        .sort((a, b) => a[0] - b[0]);
      state.lastFilterPrice = state.lastPrice;
    }
    const baseline = safeElements.orderbookCanvas.height / dpr;
    const viewFieldDollarSum =
      [...state.cachedDisplay.bids, ...state.cachedDisplay.asks].reduce(
        (sum, [p, v]) => sum + p * v,
        0,
      ) || 1;
    let cumulativeBidHeight = 0,
      cumulativeAskHeight = 0;
    state.cachedDisplay.bids.forEach(([price, volume]) => {
      const x =
        ((price - minPrice) / (maxPrice - minPrice)) *
        (safeElements.orderbookCanvas.width / dpr);
      const normalizedHeight =
        ((price * volume) / viewFieldDollarSum) *
        (safeElements.orderbookCanvas.height / dpr);
      cumulativeBidHeight += normalizedHeight;
      ctx.fillStyle = obColors.barFill;
      const barWidth = window.CONFIG?.orderbook?.barWidth || 1;
      ctx.fillRect(
        x - barWidth / 2,
        baseline - cumulativeBidHeight,
        barWidth,
        cumulativeBidHeight,
      );
    });
    state.cachedDisplay.asks.forEach(([price, volume]) => {
      const x =
        ((price - minPrice) / (maxPrice - minPrice)) *
        (safeElements.orderbookCanvas.width / dpr);
      const normalizedHeight =
        ((price * volume) / viewFieldDollarSum) *
        (safeElements.orderbookCanvas.height / dpr);
      cumulativeAskHeight += normalizedHeight;
      ctx.fillStyle = obColors.barFill;
      const barWidth = window.CONFIG?.orderbook?.barWidth || 1;
      ctx.fillRect(
        x - barWidth / 2,
        baseline - cumulativeAskHeight,
        barWidth,
        cumulativeAskHeight,
      );
    });
    utils.setTextIfChanged(
      safeElements.minPrice,
      `$${largestBid ? Math.floor(largestBid.price) : Math.floor(state.lastPrice)}`,
    );
    utils.setTextIfChanged(
      safeElements.lowestPrice,
      state.persistentBlocks.lowestBid
        ? `($${Math.floor(state.persistentBlocks.lowestBid)})`
        : "",
    );
    utils.setTextIfChanged(
      safeElements.midPrice,
      `$${Math.floor(state.lastPrice)}`,
    );
    utils.setTextIfChanged(
      safeElements.maxPrice,
      `$${largestAsk ? Math.floor(largestAsk.price) : Math.floor(state.lastPrice)}`,
    );
    utils.setTextIfChanged(
      safeElements.highestPrice,
      state.persistentBlocks.highestAsk
        ? `($${Math.floor(state.persistentBlocks.highestAsk)})`
        : "",
    );
  };
  const fetchHistoricalData = async (token) => {
    const pair = symbol.toLowerCase();
    const interval = window.CONFIG?.orderbook?.miniChartIntervalSeconds || 300;
    const totalLimit = window.CONFIG?.orderbook?.miniChartBarLimit || 288;
    try {
      if (safeElements.loadingOverlay) {
        safeElements.loadingOverlay.style.display = "block";
        safeElements.loadingOverlay.textContent = `Loading ${symbol} historical data...`;
      }
      const bitstampPair = `${pair}usd`;
      const url = `https://www.bitstamp.net/api/v2/ohlc/${bitstampPair}/?step=${interval}&limit=1000`;
      let response, data;
      try {
        response = await fetch(url);
        if (response.ok) data = await response.json();
      } catch (directError) {}
      if (!data) {
        try {
          const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(url)}`;
          response = await fetch(proxyUrl);
          if (response.ok) data = await response.json();
        } catch (proxyError) {
          console.error(`Proxy fetch failed: ${proxyError.message}`);
        }
      }
      if (!data) {
        try {
          const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;
          response = await fetch(proxyUrl);
          if (response.ok) data = await response.json();
        } catch (proxyError2) {
          console.error(`Second proxy fetch failed: ${proxyError2.message}`);
        }
      }
      if (!data?.data?.ohlc) {
        console.error("Invalid or missing Bitstamp data");
        return [];
      }
      if (token !== state.currentBarUpdateToken) return [];
      const allBars = data.data.ohlc
        .map((bar) => ({
          time: parseInt(bar.timestamp, 10),
          open: parseFloat(bar.open),
          high: parseFloat(bar.high),
          low: parseFloat(bar.low),
          close: parseFloat(bar.close),
          volume: parseFloat(bar.volume),
        }))
        .sort((a, b) => a.time - b.time);
      return allBars.slice(-totalLimit);
    } catch (error) {
      console.error(`Error fetching historical data: ${error.message}`);
      return [];
    } finally {
      if (safeElements.loadingOverlay)
        safeElements.loadingOverlay.style.display = "none";
    }
  };
  const initializeCharts = async () => {
    if (!window.LightweightCharts) {
      console.error("LightweightCharts library not loaded");
      return;
    }
    const meterWrappers =
      safeElements.container.querySelectorAll(".meter-wrapper");
    meterWrappers.forEach((wrapper) => wrapper.remove());
    const chartContainer = document.createElement("div");
    chartContainer.className = "combined-chart-container";
    chartContainer.style.width = "100%";
    chartContainer.style.flex = "1";
    chartContainer.style.marginTop = "10px";
    chartContainer.style.backgroundColor = "#0f141a";
    chartContainer.style.borderRadius = "4px";
    chartContainer.style.boxShadow = "0 1px 3px rgba(0,0,0,0.3)";
    chartContainer.style.overflow = "hidden";
    chartContainer.style.position = "relative";
    safeElements.container.style.display = "flex";
    safeElements.container.style.flexDirection = "column";
    safeElements.container.appendChild(chartContainer);
    const chart = createLightweightChart(chartContainer, {
      layout: {
        background: { color: "#0f141a", type: "solid" },
        textColor: "#D3D3D3",
        fontSize: 10,
        attributionLogo: false,
      },
      grid: { vertLines: { visible: false }, horzLines: { visible: false } },
      timeScale: { visible: false },
      rightPriceScale: {
        visible: true,
        borderColor: "#2A2A2A",
        scaleMargins: { top: 0.1, bottom: 0.1 },
      },
      crosshair: {
        mode: 1,
        vertLine: { visible: true, labelBackgroundColor: "#2A2A2A" },
        horzLine: { visible: true, labelBackgroundColor: "#2A2A2A" },
      },
    });
    if (chart) {
      state.charts.combined = chart;
      const get5MinTimestamp = (timestamp) => {
        const date = new Date(timestamp * 1000);
        date.setMinutes(Math.floor(date.getMinutes() / 5) * 5, 0, 0);
        return Math.floor(date.getTime() / 1000);
      };
      const historicalData = await fetchHistoricalData(
        state.currentBarUpdateToken,
      );
      if (historicalData && historicalData.length > 0) {
        state.chartData.candles = historicalData;
        chart.series.setData(historicalData);
        chart.chart.timeScale().fitContent();
        const lastCandle = historicalData[historicalData.length - 1];
        state.currentCandleTime = lastCandle.time;
      } else {
        state.chartData.candles = [];
        if (state.lastPrice) {
          const currentTime = get5MinTimestamp(Math.floor(Date.now() / 1000));
          const initialCandle = {
            time: currentTime,
            open: state.lastPrice,
            high: state.lastPrice,
            low: state.lastPrice,
            close: state.lastPrice,
          };
          state.chartData.candles = [initialCandle];
          chart.series.setData([initialCandle]);
          chart.chart.timeScale().fitContent();
          state.currentCandleTime = currentTime;
        }
      }
      const candleInterval = setInterval(() => {
        if (!state.enabled || !state.lastPrice) return;
        const newCandleTime = get5MinTimestamp(Math.floor(Date.now() / 1000));
        if (!state.chartData.candles || state.chartData.candles.length === 0) {
          const newCandle = {
            time: newCandleTime,
            open: state.lastPrice,
            high: state.lastPrice,
            low: state.lastPrice,
            close: state.lastPrice,
          };
          state.chartData.candles = [newCandle];
          chart.series.update(newCandle);
          state.currentCandleTime = newCandleTime;
          return;
        }
        const lastCandle =
          state.chartData.candles[state.chartData.candles.length - 1];
        if (newCandleTime > state.currentCandleTime) {
          const newCandle = {
            time: newCandleTime,
            open: state.lastPrice,
            high: state.lastPrice,
            low: state.lastPrice,
            close: state.lastPrice,
          };
          state.chartData.candles.push(newCandle);
          state.currentCandleTime = newCandleTime;
          if (state.chartData.candles.length > 288)
            state.chartData.candles = state.chartData.candles.slice(-288);
          chart.series.update(newCandle);
        } else if (lastCandle) {
          lastCandle.close = state.lastPrice;
          lastCandle.high = Math.max(lastCandle.high, state.lastPrice);
          lastCandle.low = Math.min(lastCandle.low, state.lastPrice);
          chart.series.update(lastCandle);
        }
      }, 1000);
      state.chartIntervals = [candleInterval];
    }
  };
  let updateScheduled = false;
  const updateAllVisuals = () => {
    if (!state.needsUpdate || !state.enabled || updateScheduled) return;

    // Skip updates during resize or transitions
    if (window._isResizing || state.resizing) return;
    if (window.TransitionStateManager?.isInTransition?.()) return;

    state.needsUpdate = false;
    updateScheduled = true;

    // Use optimized performance scheduler if available
    if (window.chartOptimizations?.PerformanceOptimizer) {
      window.chartOptimizations.PerformanceOptimizer.scheduleFrame(
        () => {
          try {
            updateScheduled = false;
            if (
              !state.enabled ||
              !utils.isElementInViewport(safeElements.orderbookCanvas)
            )
              return;

            const startTime = performance.now();
            const now = Date.now();
            const timeSinceLastFullDraw = now - (state.lastFullDrawTime || 0);
            const isHighLoad = state.consecutiveHighLoadFrames > 3;
            const isTabActive = !document.hidden && document.hasFocus();

            // Adaptive rendering based on performance
            if (!isTabActive && timeSinceLastFullDraw < 2000) return;
            if (isHighLoad && timeSinceLastFullDraw < 100) return;

            if (isHighLoad) {
              if (state.alternateUpdates) drawOrderBookBars();
              state.alternateUpdates = !state.alternateUpdates;
            } else {
              drawOrderBookBars();
              state.lastFullDrawTime = now;
            }

            const frameTime = performance.now() - startTime;
            state.consecutiveHighLoadFrames =
              frameTime > 16
                ? state.consecutiveHighLoadFrames + 1
                : Math.max(0, state.consecutiveHighLoadFrames - 1);
          } catch (error) {
            console.error(`Error updating visuals for ${symbol}:`, error);
            updateScheduled = false;
          }
        },
        isHighLoad ? "low" : "normal",
      );
    } else {
      // Fallback to original implementation
      setTimeout(() => {
        const now = Date.now();
        const minFrameInterval = 33;
        if (!state.lastVisualUpdateTime) state.lastVisualUpdateTime = 0;

        if (now - state.lastVisualUpdateTime >= minFrameInterval) {
          state.lastVisualUpdateTime = now;
          requestAnimationFrame(() => {
            try {
              updateScheduled = false;
              if (window._isResizing || state.resizing || !state.enabled)
                return;
              if (!utils.isElementInViewport(safeElements.orderbookCanvas))
                return;

              drawOrderBookBars();
            } catch (error) {
              console.error(`Error updating visuals for ${symbol}:`, error);
              updateScheduled = false;
            }
          });
        } else {
          updateScheduled = false;
          setTimeout(
            updateAllVisuals,
            minFrameInterval - (now - state.lastVisualUpdateTime),
          );
        }
      }, 0);
    }
  };
  const throttledUpdateAllVisuals = utils.throttle(updateAllVisuals, 100);
  const updateCanvasSize = () => {
    if (!safeElements.container.offsetParent) return;

    // Prevent multiple simultaneous resize operations
    if (state.resizing) return;
    state.resizing = true;

    const dpr = window.devicePixelRatio || 1,
      width = safeElements.container.clientWidth,
      height = safeElements.container.clientHeight;
    const textElementsHeight = 60;
    const availableHeight = height - textElementsHeight;
    const bottomMargin = 5;
    const adjustedAvailableHeight = availableHeight - bottomMargin;
    const orderbookHeight = Math.floor(adjustedAvailableHeight * 0.6);

    if (safeElements.orderbookCanvas) {
      const newWidth = Math.floor(width * dpr),
        newHeight = Math.floor(orderbookHeight * dpr);
      const prevWidth = safeElements.orderbookCanvas.width || 0,
        prevHeight = safeElements.orderbookCanvas.height || 0;

      if (
        Math.abs(newWidth - prevWidth) > 2 ||
        Math.abs(newHeight - prevHeight) > 2
      ) {
        // Set global resize flag
        window._isResizing = true;

        // Schedule canvas update
        requestAnimationFrame(() => {
          try {
            safeElements.container.style.paddingBottom = `${bottomMargin}px`;
            safeElements.orderbookCanvas.width = newWidth;
            safeElements.orderbookCanvas.height = newHeight;
            safeElements.orderbookCanvas.style.width = `${width}px`;
            safeElements.orderbookCanvas.style.height = `${orderbookHeight}px`;
            safeElements.orderbookCanvas.style.margin = `0 0 0 0`;

            // Re-get context and apply scaling
            const ctx = safeElements.orderbookCanvas.getContext("2d");
            if (dpr !== 1) ctx.scale(dpr, dpr);

            // Update parent element styles
            if (safeElements.tickerName?.parentElement)
              safeElements.tickerName.parentElement.style.cssText = `width: ${width}px; padding: 2px 0;`;
            if (safeElements.midPrice?.parentElement)
              safeElements.midPrice.parentElement.style.cssText = `width: ${width}px; padding: 2px 0;`;

            // Handle chart container resize
            const combinedChartContainer = safeElements.container.querySelector(
              ".combined-chart-container",
            );
            if (combinedChartContainer) {
              combinedChartContainer.style.width = `${width}px`;
              combinedChartContainer.style.marginTop = "10px";
              const containerHeight = safeElements.container.clientHeight;
              const orderbookCanvasHeight =
                safeElements.orderbookCanvas.clientHeight;
              const topElementsHeight = textElementsHeight;
              const chartContainerHeight =
                containerHeight -
                orderbookCanvasHeight -
                topElementsHeight -
                15;

              if (state.charts.combined && state.charts.combined.chart) {
                state.charts.combined.chart.resize(width, chartContainerHeight);
              }
            }

            // Reset cache to force redraw
            state.lastOrderBookHash = null;
            state.cachedDisplay = { bids: null, asks: null };
            state.lastFilterPrice = null;

            // Schedule visual update after resize completes
            setTimeout(() => {
              window._isResizing = false;
              state.resizing = false;
              state.needsUpdate = true;
              updateAllVisuals();
            }, 100);
          } catch (error) {
            console.error("Error during canvas resize:", error);
            window._isResizing = false;
            state.resizing = false;
          }
        });
      } else {
        state.resizing = false;
      }
    } else {
      state.resizing = false;
    }
  };
  const checkReadyState = () => {
    if (state.isBitstampReady) {
      // Always update canvas size and show canvas when connection is ready
      updateCanvasSize();
      safeElements.orderbookCanvas.style.visibility = 'visible';
      safeElements.orderbookCanvas.style.opacity = '1';
      safeElements.loadingOverlay.style.cssText =
        "opacity: 0; pointer-events: none;";
      updateOrderBookExtremes();
      updateAllVisuals();
    } else {
      // Keep canvas hidden and show loading overlay
      safeElements.orderbookCanvas.style.visibility = 'hidden';
      safeElements.orderbookCanvas.style.opacity = '0';
      safeElements.loadingOverlay.style.cssText =
        "opacity: 1; pointer-events: auto;";
      setTimeout(() => {
        if (!state.isBitstampReady) wsManager.connect();
      }, 10000);
    }
  };
  // IMPROVED FIX: Add initialization flag
  let isFirstDataReceived = false;

  wsManager.subscribe(config.ticker.bitstampOrderBook, (data) => {
    try {
      if (
        data.event === "data" &&
        data.channel === config.ticker.bitstampOrderBook
      ) {
        if (
          !data.data ||
          !Array.isArray(data.data.bids) ||
          !Array.isArray(data.data.asks)
        ) {
          console.warn(
            `[orderbook.js][${symbol}] Received invalid orderbook data`,
            data,
          );
          return;
        }

        // IMPROVED FIX: Handle first data reception
        if (!isFirstDataReceived) {
          isFirstDataReceived = true;
          // Ensure canvas is ready for first data
          setTimeout(() => {
            if (safeElements.orderbookCanvas.style.visibility === 'hidden') {
              updateCanvasSize();
              safeElements.orderbookCanvas.style.visibility = 'visible';
            }
          }, 50);
        }
        const processedBids = data.data.bids
          .filter((bid) => Array.isArray(bid) && bid.length >= 2)
          .map(([p, v]) => [parseFloat(p), parseFloat(v)])
          .filter(([p, v]) => isFinite(p) && isFinite(v));
        const processedAsks = data.data.asks
          .filter((ask) => Array.isArray(ask) && ask.length >= 2)
          .map(([p, v]) => [parseFloat(p), parseFloat(v)])
          .filter(([p, v]) => isFinite(p) && isFinite(v));
        if (processedBids.length && processedAsks.length) {
          state.orderBookData = { bids: processedBids, asks: processedAsks };

          // Optimized price update with throttling
          const now = Date.now();
          if (now - state.lastPriceUpdateTime > 1000) {
            // Reduced from 2000ms to 1000ms
            const newPrice = (processedBids[0][0] + processedAsks[0][0]) / 2;
            if (
              Math.abs(newPrice - state.lastPrice) >
              state.lastPrice * 0.0001
            ) {
              // Only update if significant change
              state.lastPrice = newPrice;
              state.lastPriceUpdateTime = now;
              utils.setTextIfChanged(
                safeElements.midPrice,
                `$${Math.floor(state.lastPrice)}`,
              );
            }
          }

          state.needsUpdate = true;

          // IMPROVED FIX: Ensure canvas is visible when we have data
          if (state.lastPrice && safeElements.orderbookCanvas.style.visibility === 'hidden') {
            safeElements.orderbookCanvas.style.visibility = 'visible';
            safeElements.orderbookCanvas.style.opacity = '1';
          }

          // Batch visual updates for better performance
          if (window.chartOptimizations?.DataProcessor) {
            window.chartOptimizations.DataProcessor.addData("orderbook", {
              symbol,
              bids: processedBids.length,
              asks: processedAsks.length,
              price: state.lastPrice,
            });
          } else {
            throttledUpdateAllVisuals();
          }
        } else {
          console.warn(
            `[orderbook.js][${symbol}] No valid bids/asks in orderbook update`,
          );
        }
      }
    } catch (error) {
      console.error(`Bitstamp orderbook WS error (${symbol}):`, error);
      setTimeout(() => wsManager.connect(), 5000);
    }
  });
  wsManager.subscribe(config.ticker.bitstampTrades, (data) => {
    try {
      if (
        data.event === "trade" &&
        data.channel === config.ticker.bitstampTrades &&
        data.data
      ) {
        const price = parseFloat(data.data.price),
          volume = parseFloat(data.data.amount),
          type = data.data.type;
        if (
          Number.isFinite(price) &&
          Number.isFinite(volume) &&
          type !== undefined
        ) {
          state.lastPrice = price;
          state.lastPriceUpdateTime = Date.now();
          updateOrderBookExtremes();
        }
      }
    } catch (error) {
      console.error(`Bitstamp trades WS error (${symbol}):`, error);
      setTimeout(() => wsManager.connect(), 5000);
    }
  });
  // Connect the WebSocket manager for this module
  wsManager.connect();

  // IMPROVED FIX: Initialize canvas size after a brief delay to prevent artifacts
  setTimeout(() => {
    updateCanvasSize();
  }, 100);

  // IMPROVED FIX: Start WebSocket connection immediately
  setTimeout(() => {
    if (!state.isBitstampReady) {
      wsManager.connect();
    }
  }, 200);

  initializeCharts();
  safeElements.container.module = {
    updateCanvasSize,
    handleConnectionEvent: (exchange) => {
      if (exchange === "bitstamp") {
        state.isBitstampReady = true;
        checkReadyState();
      }
    },
    cleanup: () => {
      if (state.chartIntervals) state.chartIntervals.forEach(clearInterval);
      throttledUpdateAllVisuals.cancel();
      wsManager.unsubscribe(config.ticker.bitstampOrderBook);
      wsManager.unsubscribe(config.ticker.bitstampTrades);
      wsManager.close(); // Only close this module's connection
    },
    getOrderBookData: () => ({ ...state.orderBookData }),
    getLastPrice: () => state.lastPrice,
    setEnabled: () => {
      state.enabled = true;
    },
    isEnabled: () => state.enabled,
    fetchHistoricalData: (token) => fetchHistoricalData(token),
  };
  return safeElements.container.module;
};

// Helper function to get DOM elements for a symbol
const getElementsForSymbol = (symbol) => {
  const prefix = symbol.toLowerCase();
  return {
    container: document.getElementById(`${prefix}-container`),
    orderbookCanvas: document.getElementById(`${prefix}-orderbook-canvas`),
    balancePercent: document.getElementById(`${prefix}-balance-percent`),
    tickerName: document.getElementById(`${prefix}-ticker-name`),
    minPrice: document.getElementById(`${prefix}-min-price`),
    midPrice: document.getElementById(`${prefix}-mid-price`),
    maxPrice: document.getElementById(`${prefix}-max-price`),
    lowestPrice: document.getElementById(`${prefix}-lowest-price`),
    highestPrice: document.getElementById(`${prefix}-highest-price`),
    loadingOverlay: document.getElementById(`${prefix}-loading-overlay`),
  };
};

// Configuration for cryptocurrencies
const cryptoConfigs = [
  { symbol: "BTC" },
  { symbol: "ETH" },
  { symbol: "LTC" },
  { symbol: "SOL" },
];

// Initialize all modules
cryptoConfigs.forEach(({ symbol }) => {
  const elements = getElementsForSymbol(symbol);

  if (elements.container && elements.orderbookCanvas) {
    const module = createCryptoModule(
      symbol,
      {
        ticker: {
          symbol,
          bitstampOrderBook: `order_book_${symbol.toLowerCase()}usd`,
          bitstampTrades: `live_trades_${symbol.toLowerCase()}usd`,
        },
        // config.orderbook.barWidth is now read directly from window.CONFIG in drawOrderBookBars
      },
      elements,
    );

    // Signal orderbook module ready after initialization
    if (module && window.signalModuleReady) {
      setTimeout(() => {
        window.signalModuleReady("orderbook", symbol);
      }, 100); // Small delay to ensure module is fully initialized
    }
  } else {
    console.warn(`[orderbook.js] Missing elements for ${symbol} module`);
  }
});

// Production optimized resize handler with memory management
if (
  !window.orderBookResizeHandlerInitialized &&
  typeof ResizeObserver !== "undefined"
) {
  let resizeTimeout;
  let resizeFrameId;

  const resizeObserver = new ResizeObserver(
    utils.debounce((entries) => {
      // Cancel any pending operations
      if (resizeTimeout) clearTimeout(resizeTimeout);
      if (resizeFrameId) cancelAnimationFrame(resizeFrameId);

      // Set global resize flag
      window._isResizing = true;

      // Batch process resize entries for better performance
      const validEntries = entries.filter(
        (entry) =>
          entry.target.module?.updateCanvasSize &&
          entry.contentRect.width > 0 &&
          entry.contentRect.height > 0,
      );

      if (validEntries.length === 0) {
        window._isResizing = false;
        return;
      }

      // Process resize entries with frame scheduling
      resizeFrameId = requestAnimationFrame(() => {
        validEntries.forEach((entry) => {
          try {
            entry.target.module.updateCanvasSize();
          } catch (error) {
            console.error("[OrderBook] Resize error:", error);
          }
        });

        // Reset resize flag with stability delay
        resizeTimeout = setTimeout(() => {
          window._isResizing = false;

          // Trigger completion events
          validEntries.forEach((entry) => {
            if (entry.target.module?.isEnabled?.()) {
              setTimeout(() => {
                document.dispatchEvent(
                  new CustomEvent("orderbook-resize-complete", {
                    detail: { symbol: entry.target.id },
                  }),
                );
              }, 25);
            }
          });

          // Force garbage collection after resize
          if (window.chartOptimizations?.MemoryManager) {
            window.chartOptimizations.MemoryManager.checkAndCleanup();
          }
        }, 150); // Reduced from 200ms for better responsiveness
      });
    }, 100), // Reduced debounce delay
  );

  // Observe all crypto containers with error handling
  try {
    document.querySelectorAll(".crypto-container").forEach((container) => {
      if (container && container.offsetParent !== null) {
        resizeObserver.observe(container);
      }
    });
  } catch (error) {
    console.error("[OrderBook] Error setting up resize observer:", error);
  }

  window.orderBookResizeHandlerInitialized = true;

  // Cleanup function for memory management
  window.cleanupOrderBookResizeHandler = () => {
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
    if (resizeTimeout) clearTimeout(resizeTimeout);
    if (resizeFrameId) cancelAnimationFrame(resizeFrameId);
    window.orderBookResizeHandlerInitialized = false;
  };
}

// WebSocket connection handler
window.handleWebSocketConnection = (exchange) =>
  document
    .querySelectorAll(".crypto-container")
    .forEach((container) => container.module?.handleConnectionEvent(exchange));
window.addEventListener("websocket-connected-bitstamp", () => {
  document.querySelectorAll(".crypto-container").forEach(async (container) => {
    if (
      container.module &&
      typeof container.module.fetchHistoricalData === "function" &&
      container.module.charts &&
      container.module.charts.combined
    ) {
      // Token-based race condition prevention
      if (!container.module.currentBarUpdateToken)
        container.module.currentBarUpdateToken = 0;
      container.module.currentBarUpdateToken++;
      const thisToken = container.module.currentBarUpdateToken;
      const bars = await container.module.fetchHistoricalData(thisToken);
      if (thisToken !== container.module.currentBarUpdateToken) return; // Outdated, ignore
      if (bars && bars.length > 0) {
        const chart = container.module.charts.combined;
        chart.series.setData(bars);
        chart.chart.timeScale().fitContent();
      }
    }
  });
});

// Enhanced WebSocket message handling with transition state awareness
function handleOrderbookMessage(message, source) {
  // CRITICAL FIX: Check transition state before processing
  if (
    window.chartTransitionState &&
    window.chartTransitionState.isTransitioning
  ) {
    return; // Skip processing during chart transitions
  }

  try {
    const data = JSON.parse(message);
    const pair = window.currentPair || "BTC";

    // Enhanced message processing with better error handling
    if (data.event === "orderbook") {
      updateOrderbookLines(data, source, pair);
    }
  } catch (error) {
    console.warn("[orderbook.js] Error processing WebSocket message:", error);
  }
}

// Debug function for ETH orderbook issues
window.debugETHOrderbook = () => {
  console.log("=== ETH Orderbook Debug ===");

  // Check DOM elements
  const ethContainer = document.getElementById("eth-container");
  const ethCanvas = document.getElementById("eth-orderbook-canvas");

  console.log("DOM Elements:", {
    container: !!ethContainer,
    canvas: !!ethCanvas,
    containerModule: !!ethContainer?.module,
    canvasContext: !!ethCanvas?.getContext("2d"),
  });

  if (ethContainer?.module) {
    const module = ethContainer.module;
    console.log("ETH Module State:", {
      enabled: module.isEnabled(),
      lastPrice: module.getLastPrice(),
      orderBookData: module.getOrderBookData(),
      hasCharts: !!module.charts,
      hasCombinedChart: !!module.charts?.combined,
    });
  }

  // Check WebSocket connection
  const wsManagers = document
    .querySelectorAll(".crypto-container")
    .forEach((container) => {
      if (container.id === "eth-container" && container.module) {
        console.log("ETH WebSocket Manager:", {
          hasWsManager: !!container.module.wsManager,
          connected: container.module.wsManager?.isConnected(),
          subscriptions: container.module.wsManager?.subscriptions,
        });
      }
    });

  // Check canvas visibility and dimensions
  if (ethCanvas) {
    console.log("ETH Canvas State:", {
      width: ethCanvas.width,
      height: ethCanvas.height,
      clientWidth: ethCanvas.clientWidth,
      clientHeight: ethCanvas.clientHeight,
      style: {
        display: ethCanvas.style.display,
        visibility: ethCanvas.style.visibility,
        opacity: ethCanvas.style.opacity,
      },
      inViewport: window.commonUtils?.isElementVisible(ethCanvas),
    });
  }

  console.log("========================");
};

// Force trigger ETH orderbook update for testing
window.forceETHOrderbookUpdate = () => {
  console.log("=== Force ETH Orderbook Update ===");

  const ethContainer = document.getElementById("eth-container");
  if (ethContainer?.module) {
    const module = ethContainer.module;

    // Force enable the module
    module.setEnabled();

    // Force canvas resize
    if (module.updateCanvasSize) {
      module.updateCanvasSize();
    }

    // Simulate orderbook data for testing
    const testData = {
      bids: [
        [2000, 1.5],
        [1999, 2.0],
        [1998, 1.0],
      ],
      asks: [
        [2001, 1.2],
        [2002, 1.8],
        [2003, 0.8],
      ],
    };

    // Access the module's internal state (if possible)
    if (module.wsManager && module.wsManager.handlers) {
      const orderbookHandler = module.wsManager.handlers["order_book_ethusd"];
      if (orderbookHandler && orderbookHandler.length > 0) {
        // Trigger the handler with test data
        const testMessage = {
          event: "data",
          channel: "order_book_ethusd",
          data: testData,
        };
        orderbookHandler[0](testMessage);
        console.log("Triggered ETH orderbook handler with test data");
      }
    }

    console.log("ETH orderbook update forced");
  } else {
    console.error("ETH module not found");
  }
};

// Simple ETH orderbook status check
window.checkETHOrderbookStatus = () => {
  console.log("=== ETH Orderbook Status Check ===");

  const ethContainer = document.getElementById("eth-container");
  const ethCanvas = document.getElementById("eth-orderbook-canvas");

  if (!ethContainer) {
    console.error("❌ ETH container not found");
    return false;
  }

  if (!ethCanvas) {
    console.error("❌ ETH canvas not found");
    return false;
  }

  if (!ethContainer.module) {
    console.error("❌ ETH module not created");
    return false;
  }

  const module = ethContainer.module;
  const orderBookData = module.getOrderBookData();
  const lastPrice = module.getLastPrice();

  console.log("✅ ETH Orderbook Status:", {
    moduleEnabled: module.isEnabled(),
    hasOrderBookData:
      orderBookData.bids.length > 0 && orderBookData.asks.length > 0,
    bidsCount: orderBookData.bids.length,
    asksCount: orderBookData.asks.length,
    lastPrice: lastPrice,
    canvasVisible: ethCanvas.offsetWidth > 0 && ethCanvas.offsetHeight > 0,
    canvasDimensions: {
      width: ethCanvas.width,
      height: ethCanvas.height,
      clientWidth: ethCanvas.clientWidth,
      clientHeight: ethCanvas.clientHeight,
    },
  });

  if (
    orderBookData.bids.length > 0 &&
    orderBookData.asks.length > 0 &&
    lastPrice
  ) {
    console.log("✅ ETH Orderbook is working correctly!");
    return true;
  } else {
    console.log("⚠️ ETH Orderbook data incomplete - may still be loading");
    return false;
  }
};
