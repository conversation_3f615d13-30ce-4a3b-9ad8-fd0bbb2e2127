// modules/charts/chartutilities/settingsManager.js
// Handles settings UI and threshold management

(function() {
  let lastTitleUpdateTime = 0;

  /**
   * Initializes the settings button and dropdown.
   */
  function initSettingsButton() {
    const existingControls = document.querySelector(".liq-controls");
    if (existingControls) existingControls.remove();
    const container = document.querySelector(".price-chart-container");
    if (!container) return;
    const controlsContainer = document.createElement("div");
    controlsContainer.className = "liq-controls";
    const settingsDropdown = document.createElement("div");
    settingsDropdown.className = "settings-dropdown";
    const settingsButton = document.createElement("button");
    settingsButton.id = "settings-btn";
    settingsButton.className = "settings-btn";
    settingsButton.textContent = "Settings";
    const dropdownContent = document.createElement("div");
    dropdownContent.className = "settings-dropdown-content";
    const savedLiqThreshold = localStorage.getItem("liquidationThreshold");
    const defaultLiqThreshold = savedLiqThreshold
      ? parseFloat(savedLiqThreshold)
      : 100000;
    const savedWhaleThreshold = localStorage.getItem("whaleAlertThreshold");
    const defaultWhaleThreshold = savedWhaleThreshold
      ? parseFloat(savedWhaleThreshold)
      : 100000;
    window.chartStates?.forEach((state) => {
      if (state.liquidationManager)
        state.liquidationManager.dollarThreshold = defaultLiqThreshold;
      if (state.whaleAlertManager)
        state.whaleAlertManager.dollarThreshold = defaultWhaleThreshold;
      ["deltaOiProfile"].forEach((profile) => {
        if (state[profile] && state[profile].config) {
          state[profile].config.normalizationWindow = 1440;
          state[profile].update?.();
        }
      });
    });
    const liqGroup = createSettingsGroup(
      "Liquidation Threshold",
      "liq-threshold-input",
      defaultLiqThreshold,
      "Min $ value",
      applyLiquidationThreshold,
    );
    const whaleGroup = createSettingsGroup(
      "Whale Alert Threshold",
      "whale-threshold-input",
      defaultWhaleThreshold,
      "Min $ value",
      applyWhaleAlertThreshold,
    );
    dropdownContent.appendChild(liqGroup);
    dropdownContent.appendChild(whaleGroup);
    settingsDropdown.appendChild(settingsButton);
    settingsDropdown.appendChild(dropdownContent);
    controlsContainer.appendChild(settingsDropdown);
    container.appendChild(controlsContainer);
    settingsButton.addEventListener("click", () =>
      dropdownContent.classList.toggle("show"),
    );
    document.addEventListener("click", (event) => {
      if (
        !settingsButton.contains(event.target) &&
        !dropdownContent.contains(event.target)
      ) {
        dropdownContent.classList.remove("show");
      }
    });
  }

  /**
   * Creates a settings group for the settings dropdown.
   * @param {string} title - Group title.
   * @param {string} inputId - Input element ID.
   * @param {number} defaultValue - Default input value.
   * @param {string} placeholder - Input placeholder text.
   * @param {Function} applyFunction - Function to apply settings.
   * @param {string} type - Input type.
   * @param {number} min - Minimum input value.
   * @param {number} max - Maximum input value.
   * @param {number} step - Input step value.
   * @returns {HTMLElement} The settings group element.
   */
  function createSettingsGroup(
    title,
    inputId,
    defaultValue,
    placeholder,
    applyFunction,
    type = "number",
    min = 0,
    max = Infinity,
    step = 10000,
  ) {
    const group = document.createElement("div");
    group.className = "settings-group";
    const groupTitle = document.createElement("div");
    groupTitle.className = "settings-group-title";
    groupTitle.textContent = title;
    const content = document.createElement("div");
    content.className = "settings-group-content";
    const input = document.createElement("input");
    input.type = type;
    input.min = min;
    input.max = max;
    input.step = step;
    input.value = defaultValue.toString();
    input.id = inputId;
    input.className = "liq-threshold-input";
    input.placeholder = placeholder;
    const applyButton = document.createElement("button");
    applyButton.textContent = "Apply";
    applyButton.className = "liq-apply-btn";
    content.appendChild(input);
    content.appendChild(applyButton);
    group.appendChild(groupTitle);
    group.appendChild(content);
    applyButton.addEventListener("click", applyFunction);
    input.addEventListener("keyup", (event) => {
      if (event.key === "Enter") applyFunction();
    });
    return group;
  }

  /**
   * Applies the liquidation threshold setting.
   */
  function applyLiquidationThreshold() {
    const thresholdInput = document.getElementById("liq-threshold-input");
    if (!thresholdInput) return;
    const thresholdValue = parseFloat(thresholdInput.value) || 100000;
    try {
      localStorage.setItem("liquidationThreshold", thresholdValue.toString());
      window.currentLiquidationThreshold = thresholdValue;
      window.directConsole?.setThreshold?.(thresholdValue);
      if (typeof window.setConsoleMessageThreshold === "function") {
        window.setConsoleMessageThreshold(thresholdValue);
      } else {
        window.consoleMessageThreshold = thresholdValue;
      }
      window.chartStates.forEach((state) => {
        if (state.liquidationManager)
          state.liquidationManager.dollarThreshold = thresholdValue;
      });
      
      // Refresh threshold caches in WebSocket managers
      if (typeof window.refreshThresholdCaches === "function") {
        window.refreshThresholdCaches();
      }
      
      console.log(
        `[Settings] Liquidation threshold updated: $${thresholdValue.toLocaleString()}`,
      );
      thresholdInput.style.backgroundColor = "rgba(0, 100, 0, 0.3)";
      setTimeout(() => (thresholdInput.style.backgroundColor = ""), 500);
    } catch (e) {
      thresholdInput.style.backgroundColor = "rgba(100, 0, 0, 0.3)";
      setTimeout(() => (thresholdInput.style.backgroundColor = ""), 500);
    }
  }

  /**
   * Applies the whale alert threshold setting.
   */
  function applyWhaleAlertThreshold() {
    const thresholdInput = document.getElementById("whale-threshold-input");
    if (!thresholdInput) return;
    const thresholdValue = parseFloat(thresholdInput.value) || 100000;
    try {
      localStorage.setItem("whaleAlertThreshold", thresholdValue.toString());
      if (typeof window.setConsoleMessageThreshold === "function") {
        window.setConsoleMessageThreshold(thresholdValue);
      } else {
        window.consoleMessageThreshold = thresholdValue;
      }
      window.chartStates.forEach((state) => {
        if (state.whaleAlertManager)
          state.whaleAlertManager.dollarThreshold = thresholdValue;
      });
      
      // Refresh threshold caches in WebSocket managers
      if (typeof window.refreshThresholdCaches === "function") {
        window.refreshThresholdCaches();
      }
      
      console.log(
        `[Settings] Whale alert threshold updated: $${thresholdValue.toLocaleString()}`,
      );
      thresholdInput.style.backgroundColor = "rgba(0, 100, 0, 0.3)";
      setTimeout(() => (thresholdInput.style.backgroundColor = ""), 500);
    } catch (e) {
      thresholdInput.style.backgroundColor = "rgba(100, 0, 0, 0.3)";
      setTimeout(() => (thresholdInput.style.backgroundColor = ""), 500);
    }
  }

  /**
   * Ensures the liquidation console is visible.
   */
  function ensureLiquidationConsoleTitle() {
    const now = Date.now();
    if (now - lastTitleUpdateTime < 500) return;
    lastTitleUpdateTime = now;
    const consoleElement = document.getElementById("liquidation-console");
    if (!consoleElement) return;
    const existingTitles = consoleElement.querySelectorAll(
      "#liquidation-console-title",
    );
    existingTitles.forEach((title) => title.remove());
    const style = window.getComputedStyle(consoleElement);
    if (
      style.display === "none" ||
      style.visibility === "hidden" ||
      parseFloat(style.opacity) === 0
    ) {
      consoleElement.style.display = "block";
      consoleElement.style.visibility = "visible";
      consoleElement.style.opacity = "1";
    }
  }

  // Export the functions
  window.settingsManager = {
    initSettingsButton,
    createSettingsGroup,
    applyLiquidationThreshold,
    applyWhaleAlertThreshold,
    ensureLiquidationConsoleTitle
  };
})(); 