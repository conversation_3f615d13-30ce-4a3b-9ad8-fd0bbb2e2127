// modules/charts/chartutilities/dataFetcher.js
// Handles historical data fetching for Bitstamp and Bybit

/**
 * Data Fetcher
 * Handles data fetching, caching, and transformation for charts
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("DataFetcher")
    : console;
  // Use global getHistDb, HIST_STORE, configManager
  if (!window.getHistDb || !window.HIST_STORE || !window.configManager) {
    throw new Error(
      "getHistDb, HIST_STORE, and configManager must be defined globally",
    );
  }

  const CONFIG = window.configManager.CONFIG;
  const utils = window.configManager.utils;

  async function fetchBitstampHistoricalData(
    pair,
    interval,
    limit = 6000,
    abortSignal,
  ) {
    const cacheKey = `${pair}_historical_${interval}_${limit}_${Math.floor(Date.now() / CONFIG.cacheTTL)}`;
    let db;
    try {
      db = await window.getHistDb();
      const cached = await db.get(window.HIST_STORE, cacheKey);
      if (cached && cached.data.length >= limit * 0.9) {
        return cached.data.map((b) => ({
          time: b.t,
          open: b.o,
          high: b.h,
          low: b.l,
          close: b.c,
          volume: b.v,
        }));
      }
    } catch (e) {
      logger.warn("Cache access failed, proceeding without cache:", e);
    }

    // Bitstamp API step mapping - these are the valid step values for Bitstamp API
    // Bitstamp only accepts: 60, 300, 900, 1800, 3600, 14400, 86400
    const bitstampStepMap = {
      60: 60, // 1 minute
      300: 300, // 5 minutes
      900: 900, // 15 minutes
      1800: 1800, // 30 minutes
      3600: 3600, // 1 hour
      14400: 14400, // 4 hours
      86400: 86400, // 1 day
      // Note: Bitstamp doesn't support weekly or monthly intervals
      // For weekly/monthly, we'll use daily data and aggregate
      604800: 86400, // 1 week -> use daily data
      2592000: 86400, // 1 month -> use daily data
    };

    // Use the mapped step value or default to 300 (5 minutes)
    const step = bitstampStepMap[interval] || 300;

    const maxApiLimit = 1000;
    let allBars = [],
      timeMap = new Map();
    const fetchBars = async (url) => {
      if (abortSignal?.aborted) throw new Error("Fetch aborted");
      const res = await fetch(url, { signal: abortSignal });
      if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
      const data = await res.json();
      if (!data?.data?.ohlc) throw new Error("Invalid Bitstamp data");
      data.data.ohlc.forEach((b) => {
        const t = parseInt(b.timestamp, 10);
        if (!timeMap.has(t)) {
          timeMap.set(t, true);
          allBars.push({
            time: t,
            open: parseFloat(b.open),
            high: parseFloat(b.high),
            low: parseFloat(b.low),
            close: parseFloat(b.close),
            volume: parseFloat(b.volume),
          });
        }
      });
    };
    await fetchBars(
      `https://www.bitstamp.net/api/v2/ohlc/${pair}/?step=${step}&limit=${maxApiLimit}`,
    );
    allBars.sort((a, b) => a.time - b.time);
    if (allBars.length > 0) {
      for (let i = 0; i < 3 && allBars.length < limit; i++) {
        allBars.sort((a, b) => a.time - b.time);
        const earliest = allBars[0].time;
        const batchPromises = Array.from({ length: 2 }, (_, j) => {
          const offset = j === 0 ? 1 : j * maxApiLimit * step;
          return fetchBars(
            `https://www.bitstamp.net/api/v2/ohlc/${pair}/?step=${step}&limit=${maxApiLimit}&end=${earliest - offset}`,
          )
            .then(() => allBars.length)
            .catch(() => 0);
        });
        const newBars = await Promise.all(batchPromises);
        if (newBars.reduce((s, c) => s + c, 0) === 0) break;
        if (i < 2) await new Promise((r) => setTimeout(r, 100));
      }
    }
    allBars.sort((a, b) => a.time - b.time);

    // Aggregate daily data to weekly/monthly if needed
    if (interval === 604800) {
      allBars = aggregateDailyToWeekly(allBars);
    } else if (interval === 2592000) {
      allBars = aggregateDailyToMonthly(allBars);
    }

    if (allBars.length > limit) allBars = allBars.slice(-limit);
    try {
      if (db) {
        await db.set(window.HIST_STORE, {
          key: cacheKey,
          timestamp: Date.now(),
          data: allBars.map((b) => ({
            t: b.time,
            o: b.open,
            h: b.high,
            l: b.low,
            c: b.close,
            v: b.volume,
          })),
        });
      }
    } catch (e) {
      logger.warn("Failed to cache Bitstamp data:", e);
    }
    return allBars;
  }

  // Helper functions for aggregation
  function getStartOfWeek(timestampSeconds) {
    const date = new Date(timestampSeconds * 1000);
    const day = date.getUTCDay();
    const diff = (day + 6) % 7;
    date.setUTCDate(date.getUTCDate() - diff);
    date.setUTCHours(0, 0, 0, 0);
    return Math.floor(date.getTime() / 1000);
  }

  function getStartOfMonth(timestampSeconds) {
    const date = new Date(timestampSeconds * 1000);
    date.setUTCDate(1);
    date.setUTCHours(0, 0, 0, 0);
    return Math.floor(date.getTime() / 1000);
  }

  function aggregateDailyToWeekly(dailyData) {
    if (!dailyData || dailyData.length === 0) return [];

    const weeklyData = [];
    let currentWeekStart = -1;
    let weeklyCandle = null;

    for (const dailyCandle of dailyData) {
      const weekStartForCandle = getStartOfWeek(dailyCandle.time);

      if (weekStartForCandle !== currentWeekStart) {
        if (weeklyCandle) {
          weeklyData.push(weeklyCandle);
        }
        currentWeekStart = weekStartForCandle;
        weeklyCandle = {
          time: currentWeekStart,
          open: dailyCandle.open,
          high: dailyCandle.high,
          low: dailyCandle.low,
          close: dailyCandle.close,
          volume: dailyCandle.volume,
        };
      } else {
        weeklyCandle.high = Math.max(weeklyCandle.high, dailyCandle.high);
        weeklyCandle.low = Math.min(weeklyCandle.low, dailyCandle.low);
        weeklyCandle.close = dailyCandle.close;
        weeklyCandle.volume += dailyCandle.volume;
      }
    }
    if (weeklyCandle) {
      weeklyData.push(weeklyCandle);
    }
    return weeklyData;
  }

  function aggregateDailyToMonthly(dailyData) {
    if (!dailyData || dailyData.length === 0) return [];

    const monthlyData = [];
    let currentMonthStart = -1;
    let monthlyCandle = null;

    for (const dailyCandle of dailyData) {
      const monthStartForCandle = getStartOfMonth(dailyCandle.time);

      if (monthStartForCandle !== currentMonthStart) {
        if (monthlyCandle) {
          monthlyData.push(monthlyCandle);
        }
        currentMonthStart = monthStartForCandle;
        monthlyCandle = {
          time: currentMonthStart,
          open: dailyCandle.open,
          high: dailyCandle.high,
          low: dailyCandle.low,
          close: dailyCandle.close,
          volume: dailyCandle.volume,
        };
      } else {
        monthlyCandle.high = Math.max(monthlyCandle.high, dailyCandle.high);
        monthlyCandle.low = Math.min(monthlyCandle.low, dailyCandle.low);
        monthlyCandle.close = dailyCandle.close;
        monthlyCandle.volume += dailyCandle.volume;
      }
    }
    if (monthlyCandle) {
      monthlyData.push(monthlyCandle);
    }
    return monthlyData;
  }

  async function fetchBybitHistoricalData(
    pair,
    interval = CONFIG.barInterval,
    limit = 6000,
    onProgress,
    abortSignal,
  ) {
    const cacheKey = `bybit_${pair}_historical_${interval}_${limit}_${Math.floor(Date.now() / CONFIG.cacheTTL)}`;
    let db;
    try {
      db = await window.getHistDb();
      const cached = await db.get(window.HIST_STORE, cacheKey);
      if (cached && cached.data.length >= limit * 0.9) {
        return cached.data.map((b) => ({
          time: b.t,
          open: b.o,
          high: b.h,
          low: b.l,
          close: b.c,
          volume: b.v,
        }));
      }
    } catch (e) {
      logger.warn("Cache access failed, proceeding without cache:", e);
    }
    const symbol = `${pair}USDT`;
    let allBars = [],
      timeMap = new Map();
    const maxApiLimit = 1000;
    const intervalMap = {
      60: "1",
      300: "5",
      900: "15",
      1800: "30",
      3600: "60",
      14400: "240",
      86400: "D",
      D: "D",
      "1D": "D",
    };
    const bybitInterval = intervalMap[interval] || "5";
    const reverseIntervalMap = {
      1: 60,
      5: 300,
      15: 900,
      30: 1800,
      60: 3600,
      240: 14400,
      D: 86400,
    };
    const intervalSeconds = reverseIntervalMap[bybitInterval] || 300;
    const throttledProgress = onProgress
      ? utils.throttle(onProgress, 200)
      : null;
    const fetchBars = async (url) => {
      if (abortSignal?.aborted) throw new Error("Fetch aborted");
      const res = await fetch(url, { signal: abortSignal });
      if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
      const data = await res.json();
      if (data.retCode !== 0) {
        logger.error("Bybit API error:", data.retMsg);
        return 0;
      }
      let added = 0;
      if (data?.result?.list) {
        data.result.list.forEach((item) => {
          const t = Math.floor(parseInt(item[0]) / 1000);
          const nt = Math.floor(t / interval) * interval;
          if (!timeMap.has(nt)) {
            timeMap.set(nt, true);
            allBars.push({
              time: nt,
              open: parseFloat(item[1]),
              high: parseFloat(item[2]),
              low: parseFloat(item[3]),
              close: parseFloat(item[4]),
              volume: parseFloat(item[5]),
            });
            added++;
          }
        });
      }
      if (throttledProgress) throttledProgress(allBars.slice());
      return added;
    };
    try {
      let keepFetching = true;
      let fetchCount = 0;
      let lastEarliest = null;
      await fetchBars(
        `https://api.bybit.com/v5/market/kline?category=linear&symbol=${symbol}&interval=${bybitInterval}&limit=${maxApiLimit}`,
      );
      allBars.sort((a, b) => a.time - b.time);
      while (allBars.length < limit && keepFetching && allBars.length > 0) {
        allBars.sort((a, b) => a.time - b.time);
        const earliest = allBars[0].time;
        if (lastEarliest !== null && earliest >= lastEarliest) break;
        lastEarliest = earliest;
        const endTime = (earliest - 1) * 1000;
        const url = `https://api.bybit.com/v5/market/kline?category=linear&symbol=${symbol}&interval=${bybitInterval}&limit=${maxApiLimit}&end=${endTime}`;
        const added = await fetchBars(url).catch((error) => {
          logger.warn(`Historical batch failed:`, error.message);
          return 0;
        });
        fetchCount++;
        if (added === 0) keepFetching = false;
        if (keepFetching && allBars.length < limit)
          await new Promise((resolve) => setTimeout(resolve, 100));
      }
      allBars.sort((a, b) => a.time - b.time);
      const result = allBars.length > limit ? allBars.slice(-limit) : allBars;
      if (result.length > 0) {
        const timeSpan = result[result.length - 1].time - result[0].time;
        let gaps = 0;
        for (let i = 1; i < result.length; i++) {
          const timeDiff = result[i].time - result[i - 1].time;
          if (timeDiff > intervalSeconds * 1.5) gaps++;
        }
        if (gaps > 0) logger.warn(`Found ${gaps} data gaps in historical data`);
      }
      try {
        if (db) {
          await db.set(window.HIST_STORE, {
            key: cacheKey,
            timestamp: Date.now(),
            data: result.map((b) => ({
              t: b.time,
              o: b.open,
              h: b.high,
              l: b.low,
              c: b.close,
              v: b.volume,
            })),
          });
        }
      } catch (e) {
        logger.warn("Failed to cache Bybit data:", e);
      }
      return result;
    } catch (e) {
      logger.error("Bybit historical data fetch failed:", e);
      return [];
    }
  }

  // Export for compatibility
  window.fetchBybitHistoricalData = fetchBybitHistoricalData;
  window.fetchBitstampHistoricalData = fetchBitstampHistoricalData;

  // Create a getHistoricalData function that matches the expected interface
  async function getHistoricalData(exchange, symbol, interval, signal) {
    try {
      if (exchange.toLowerCase() === "bitstamp") {
        return await fetchBitstampHistoricalData(
          symbol,
          interval,
          6000,
          null,
          signal,
        );
      } else if (exchange.toLowerCase() === "bybit") {
        return await fetchBybitHistoricalData(
          symbol,
          interval,
          6000,
          null,
          signal,
        );
      } else {
        throw new Error(`Unsupported exchange: ${exchange}`);
      }
    } catch (error) {
      logger.error(
        `Error fetching historical data for ${exchange} ${symbol}:`,
        error,
      );
      return [];
    }
  }

  // Export as a module with all required functions
  window.dataFetcher = {
    fetchBitstampHistoricalData,
    fetchBybitHistoricalData,
    getHistoricalData,
  };
})();
