// modules/charts/chartutilities/websocketMessageHandler.js
// Handles WebSocket message queueing and processing for charts

/**
 * WebSocket Message Handler
 * Handles WebSocket message queueing and processing for charts
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("WebSocketMessageHandler")
    : console;
  // Use global chartStates, currentPair, directUpdatePriceData, OrderbookManager, handleError
  if (!window.chartStates || typeof window.currentPair === "undefined") {
    throw new Error("chartStates and currentPair must be defined globally");
  }

  let lastMessageProcessTime = 0;
  const minMessageInterval = 1; // 1ms for near real-time updates

  function handleWebSocketMessage(message, source, chartState) {
    // CRITICAL FIX: Check transition state before processing
    if (
      window.TransitionStateManager &&
      window.TransitionStateManager.isInTransition()
    ) {
      logger.debug(
        "[websocketMessageHandler] Skipping message processing during chart transition",
      );
      return;
    }

    if (!chartState?.config?.ticker) return;
    const pair = chartState.config.ticker.symbol;
    try {
      if (source === "bitstamp") {
        if (
          message.channel === `live_trades_${pair.toLowerCase()}usd` &&
          message.data
        ) {
          const price = parseFloat(message.data.price);
          const volume = parseFloat(message.data.amount);
          if (Number.isFinite(price) && Number.isFinite(volume) && price > 0) {
            const type = message.data.type;
            const timestamp =
              parseInt(message.data.timestamp) || Math.floor(Date.now() / 1000);
            directUpdatePriceData(chartState, price, volume, type, timestamp);
          }
        }
        if (
          message.channel === `order_book_${pair.toLowerCase()}usd` &&
          message.data?.bids &&
          message.data?.asks &&
          chartState.data?.orderBook
        ) {
          // CRITICAL FIX: Add transition state check for orderbook updates
          if (
            window.TransitionStateManager &&
            window.TransitionStateManager.isInTransition()
          ) {
            logger.debug(
              "[websocketMessageHandler] Skipping orderbook update during chart transition",
            );
            return;
          }

          requestAnimationFrame(() => {
            // Double-check transition state inside rAF
            if (
              window.TransitionStateManager &&
              window.TransitionStateManager.isInTransition()
            ) {
              return;
            }

            chartState.data.orderBook.bids = message.data.bids.map(([p, v]) => [
              parseFloat(p),
              parseFloat(v),
            ]);
            chartState.data.orderBook.asks = message.data.asks.map(([p, v]) => [
              parseFloat(p),
              parseFloat(v),
            ]);
            if (window.OrderbookManager)
              window.OrderbookManager.updateOrderBookLines(chartState);
          });
        }
      }
      if (source === "bybit") {
        if (message.topic?.startsWith("liquidation.") && message.data) {
          if (!window.bybitWsManager?.isConnected()) {
            logger.warn("Received Bybit message but connection is not healthy");
            return;
          }
          const liquidations = Array.isArray(message.data)
            ? message.data
            : [message.data];
          const batch = liquidations
            .map((liq) => ({
              price: parseFloat(liq.price),
              amount: parseFloat(liq.size || liq.qty),
              side: liq.side?.toLowerCase(),
              value: parseFloat(liq.price) * parseFloat(liq.size || liq.qty),
            }))
            .filter(
              (l) =>
                Number.isFinite(l.price) && Number.isFinite(l.amount) && l.side,
            );
          batch.forEach((liq) => {
            logger.info(
              `${liq.side === "buy" ? "LONG LIQUIDATION:" : "SHORT LIQUIDATION:"} $${liq.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} @ ${liq.price.toLocaleString(undefined, { minimumFractionDigits: 1, maximumFractionDigits: 2 })} | ${pair}`,
            );
            if (window.liqEventCallback) {
              window.liqEventCallback({
                pair,
                price: liq.price,
                size: liq.amount,
                side: liq.side,
                value: liq.value,
                timestamp: new Date().toISOString(),
              });
            }
          });
        }
      }
    } catch (e) {
      if (window.configManager?.handleError) {
        window.configManager.handleError(
          `WebSocket handler error (${source})`,
          e,
        );
      } else {
        logger.error(`WebSocket handler error (${source}):`, e);
      }
    }
  }

  // CRITICAL FIX: Optimize message processing to reduce requestAnimationFrame violations
  let pendingMessages = [];
  let processingScheduled = false;

  const processPendingMessages = () => {
    if (pendingMessages.length === 0) {
      processingScheduled = false;
      return;
    }

    const messagesToProcess = pendingMessages.splice(0, 10); // Process in batches
    messagesToProcess.forEach(({ data, source, state }) => {
      handleWebSocketMessage(data, source, state);
    });

    if (pendingMessages.length > 0) {
      // Use setTimeout instead of requestAnimationFrame for non-visual updates
      setTimeout(processPendingMessages, 1);
    } else {
      processingScheduled = false;
    }
  };

  const messageQueue = {
    add: (source, data) => {
      const state = window.chartStates.get(window.currentPair);
      if (!state) return;

      // CRITICAL FIX: Skip processing during transitions
      if (window.TransitionStateManager?.isInTransition()) {
        return;
      }

      if (source === "bitstamp" && data.channel?.includes("live_trades")) {
        // Process live trades immediately for real-time price updates
        handleWebSocketMessage(data, source, state);
      } else {
        const now = Date.now();
        if (now - lastMessageProcessTime >= minMessageInterval) {
          lastMessageProcessTime = now;
          handleWebSocketMessage(data, source, state);
        } else {
          // Queue message for batch processing
          pendingMessages.push({ data, source, state });
          if (!processingScheduled) {
            processingScheduled = true;
            setTimeout(processPendingMessages, 5); // Small delay for batching
          }
        }
      }
    },
  };

  // Export to global for charts.js to use
  window.websocketMessageQueue = messageQueue;
  window.handleWebSocketMessage = handleWebSocketMessage;

  // Export module for simpleLoader to detect
  window.websocketMessageHandler = {
    messageQueue,
    handleWebSocketMessage,
  };
})();
