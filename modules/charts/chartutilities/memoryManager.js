// modules/charts/chartutilities/memoryManager.js
// Handles memory management and cleanup operations

(function() {
  // Use global dependencies
  if (!window.CONFIG) {
    throw new Error('CONFIG must be defined globally');
  }

  const CONFIG = window.CONFIG;

  const memoryManagement = {
    cleanupInterval: 60000,
    lastCleanup: Date.now(),
    /**
     * Cleans up historical data to manage memory usage.
     * @param {Object} state - Chart state containing data arrays.
     */
    cleanupHistoricalData: (state) => {
      if (
        !state?.data ||
        Date.now() - memoryManagement.lastCleanup <
          memoryManagement.cleanupInterval
      )
        return;
      memoryManagement.lastCleanup = Date.now();
      const maxBars = CONFIG.maxBars;
      [
        state.data.priceData,
        state.data.alignedBybit,
        state.data.alignedBitstamp,
      ].forEach((a) => {
        if (a?.length > maxBars) a.splice(0, a.length - maxBars);
      });
      state.oldData = null;
      if (window.gc) window.gc();
    },
  };

  // Export the memory management object
  window.memoryManager = memoryManagement;
})(); 