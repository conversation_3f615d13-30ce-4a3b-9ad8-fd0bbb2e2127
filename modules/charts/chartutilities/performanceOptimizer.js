// modules/charts/chartutilities/performanceOptimizer.js
// Handles performance optimization and scheduling operations

/**
 * Performance Optimizer
 * <PERSON><PERSON> chart and DOM update performance optimizations
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("PerformanceOptimizer")
    : console;
  // Batch chart updates to reduce requestAnimationFrame violations
  let pendingChartUpdates = new Map();
  let chartUpdateScheduled = false;

  function scheduleChartUpdate(pair, updateFn) {
    if (!pendingChartUpdates.has(pair)) {
      pendingChartUpdates.set(pair, []);
    }
    pendingChartUpdates.get(pair).push(updateFn);

    if (!chartUpdateScheduled) {
      chartUpdateScheduled = true;

      // Use requestIdleCallback if available, otherwise use requestAnimationFrame
      const scheduleFn = window.requestIdleCallback || requestAnimationFrame;
      scheduleFn(() => {
        // Process all pending updates
        for (const [pairKey, updates] of pendingChartUpdates) {
          const state = window.chartStates?.get(pairKey);
          if (state && state.isActive) {
            updates.forEach((updateFn) => {
              try {
                updateFn();
              } catch (error) {
                window.errorHandler.handleError(
                  error,
                  `[performanceOptimizer.js] Error in chart update for ${pairKey}:`,
                );
              }
            });
          }
        }

        // Clear pending updates
        pendingChartUpdates.clear();
        chartUpdateScheduled = false;
      });
    }
  }

  // Batch DOM updates to reduce forced reflow violations
  let pendingDOMUpdates = [];
  let domUpdateScheduled = false;

  function scheduleDOMUpdate(updateFn) {
    pendingDOMUpdates.push(updateFn);

    if (!domUpdateScheduled) {
      domUpdateScheduled = true;

      // Use requestAnimationFrame to batch DOM updates
      requestAnimationFrame(() => {
        // Batch all DOM reads first
        const reads = pendingDOMUpdates.filter(
          (update) => update.type === "read",
        );
        reads.forEach((update) => {
          try {
            update.fn();
          } catch (error) {
            window.errorHandler.handleError(
              error,
              "[performanceOptimizer.js] Error in DOM read:",
            );
          }
        });

        // Then batch all DOM writes
        const writes = pendingDOMUpdates.filter(
          (update) => update.type === "write",
        );
        writes.forEach((update) => {
          try {
            update.fn();
          } catch (error) {
            window.errorHandler.handleError(
              error,
              "[performanceOptimizer.js] Error in DOM write:",
            );
          }
        });

        // Clear pending updates
        pendingDOMUpdates = [];
        domUpdateScheduled = false;
      });
    }
  }

  // Helper functions for DOM operations
  function scheduleDOMRead(fn) {
    scheduleDOMUpdate({ type: "read", fn });
  }

  function scheduleDOMWrite(fn) {
    scheduleDOMUpdate({ type: "write", fn });
  }

  // Optimize indicator updates to reduce requestIdleCallback violations
  let pendingIndicatorUpdates = new Map();
  let indicatorUpdateScheduled = false;

  function scheduleIndicatorUpdate(indicatorName, updateFn) {
    if (!pendingIndicatorUpdates.has(indicatorName)) {
      pendingIndicatorUpdates.set(indicatorName, []);
    }
    pendingIndicatorUpdates.get(indicatorName).push(updateFn);

    if (!indicatorUpdateScheduled) {
      indicatorUpdateScheduled = true;

      // Use a more efficient scheduling strategy
      const scheduleFn =
        window.requestIdleCallback || ((callback) => setTimeout(callback, 0));

      scheduleFn(
        () => {
          // Process all pending indicator updates
          for (const [indicator, updates] of pendingIndicatorUpdates) {
            updates.forEach((updateFn) => {
              try {
                updateFn();
              } catch (error) {
                window.errorHandler.handleError(
                  error,
                  `[performanceOptimizer.js] Error in indicator update for ${indicator}:`,
                );
              }
            });
          }

          // Clear pending updates
          pendingIndicatorUpdates.clear();
          indicatorUpdateScheduled = false;
        },
        { timeout: 50 },
      ); // Shorter timeout to prevent violations
    }
  }

  // Throttle initialization to reduce startup performance violations
  let initializationInProgress = false;
  let pendingInitializations = [];

  function throttleInitialization(initFn) {
    if (initializationInProgress) {
      pendingInitializations.push(initFn);
      return;
    }

    initializationInProgress = true;

    // Use requestIdleCallback for initialization to avoid blocking
    const scheduleFn =
      window.requestIdleCallback || ((callback) => setTimeout(callback, 0));

    scheduleFn(
      () => {
        try {
          initFn();
        } catch (error) {
          window.errorHandler.handleError(
            error,
            "[performanceOptimizer.js] Error during throttled initialization:",
          );
        }

        // Process any pending initializations
        if (pendingInitializations.length > 0) {
          const nextInit = pendingInitializations.shift();
          setTimeout(() => throttleInitialization(nextInit), 100);
        } else {
          initializationInProgress = false;
        }
      },
      { timeout: 100 },
    );
  }

  // Optimize chart initialization
  function optimizedChartInit(container, data, pair, callback) {
    return throttleInitialization(() => {
      try {
        const state = window.chartInitializer?.initializeChartAndMeter(
          container,
          data,
          pair,
          callback,
        );
        if (state) {
          window.chartStates?.set(pair, state);
        }
      } catch (error) {
        window.errorHandler.handleError(
          error,
          "[performanceOptimizer.js] Error in optimized chart init:",
        );
      }
    });
  }

  // Optimize WebSocket reconnection for faster recovery
  function optimizeWebSocketReconnection() {
    logger.log(
      "[performanceOptimizer.js] Optimizing WebSocket reconnection...",
    );

    // Optimize Bitstamp WebSocket reconnection
    if (window.bitstampWsManager) {
      try {
        if (
          typeof window.bitstampWsManager.setReconnectionStrategy === "function"
        ) {
          window.bitstampWsManager.setReconnectionStrategy({
            initialDelay: 100,
            maxDelay: 5000,
            backoffMultiplier: 1.5,
            maxRetries: 10,
          });
        } else {
          logger.log(
            "[performanceOptimizer.js] Bitstamp WebSocket manager does not support setReconnectionStrategy",
          );
        }
      } catch (error) {
        window.errorHandler.handleError(
          error,
          "[performanceOptimizer.js] Error setting Bitstamp reconnection strategy:",
        );
      }
    }

    // Optimize Bybit WebSocket reconnection
    if (window.bybitWsManager) {
      try {
        if (
          typeof window.bybitWsManager.setReconnectionStrategy === "function"
        ) {
          window.bybitWsManager.setReconnectionStrategy({
            initialDelay: 100,
            maxDelay: 5000,
            backoffMultiplier: 1.5,
            maxRetries: 10,
          });
        } else {
          logger.log(
            "[performanceOptimizer.js] Bybit WebSocket manager does not support setReconnectionStrategy",
          );
        }
      } catch (error) {
        window.errorHandler.handleError(
          error,
          "[performanceOptimizer.js] Error setting Bybit reconnection strategy:",
        );
      }
    }
  }

  // Export the performance optimization functions
  window.performanceOptimizer = {
    scheduleChartUpdate,
    scheduleDOMUpdate,
    scheduleDOMRead,
    scheduleDOMWrite,
    scheduleIndicatorUpdate,
    throttleInitialization,
    optimizedChartInit,
    optimizeWebSocketReconnection,
  };

  // Export the function globally for direct access
  window.optimizeWebSocketReconnection = optimizeWebSocketReconnection;
})();
