// modules/charts/chartutilities/pollingManager.js
// Handles chart polling and data fetching operations

/**
 * Polling Manager
 * Handles polling for new chart data and indicator updates
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("PollingManager")
    : console;
  // Use global dependencies
  if (!window.configManager) {
    throw new Error("configManager must be defined globally");
  }

  const CONFIG = window.configManager.CONFIG;

  // Add polling mechanism variables
  let chartPollingInterval = null;
  let lastBarTime = 0;
  const BAR_INTERVAL = 300; // 5 minutes in seconds
  const POLL_INTERVAL = 5 * 60 * 1000; // 5 minutes in milliseconds

  // Performance optimization: Reduce update frequency when tab is not visible
  let lastChartUpdate = 0;

  /**
   * Starts 5-minute polling for chart data instead of WebSocket updates
   */
  function startChartPolling(pair) {
    if (chartPollingInterval) {
      clearInterval(chartPollingInterval);
    }

    chartPollingInterval = setInterval(async () => {
      // CRITICAL FIX: Check transition state before polling
      if (
        window.TransitionStateManager &&
        window.TransitionStateManager.isInTransition()
      ) {
        logger.debug(
          "[pollingManager.js] Skipping chart polling during chart transition",
        );
        return;
      }

      try {
        const state = window.chartStates?.get(pair);
        if (!state || !state.isActive) return;

        const pollStart = performance.now();

        // Fetch latest bar data from both APIs
        const latestData = await fetchLatestBarData(pair);
        if (!latestData.bitstampBar && !latestData.bybitBar) {
          return;
        }

        // Update chart with Bitstamp bar (primary price data)
        if (latestData.bitstampBar) {
          updateChartWithNewBar(state, latestData.bitstampBar);
        }

        // Update Bybit data for indicators
        if (latestData.bybitBar && state.data.alignedBybit) {
          // Add new Bybit bar to aligned data
          state.data.alignedBybit.push(latestData.bybitBar);
          if (state.data.alignedBybit.length > state.config.maxBars) {
            state.data.alignedBybit.shift();
          }
        }

        // Update aligned Bitstamp data for indicators
        if (latestData.bitstampBar && state.data.alignedBitstamp) {
          // Add new Bitstamp bar to aligned data
          state.data.alignedBitstamp.push(latestData.bitstampBar);
          if (state.data.alignedBitstamp.length > state.config.maxBars) {
            state.data.alignedBitstamp.shift();
          }
        }

        // Update all indicators with fresh data
        updateAllIndicators(state, latestData);
      } catch (error) {
        window.errorHandler.handleError(
          error,
          "Error in chart polling for ${pair}",
        );
      }
    }, POLL_INTERVAL);

    // Initial poll - removed problematic _callback call
    // The interval will start automatically after POLL_INTERVAL
  }

  /**
   * Fetches the latest bar data from API
   */
  async function fetchLatestBarData(pair) {
    try {
      // Fetch both Bitstamp and Bybit data for complete indicator updates
      const [bitstampResponse, bybitResponse] = await Promise.all([
        fetch(
          `https://www.bitstamp.net/api/v2/ohlc/${pair.toLowerCase()}usd/?step=300&limit=1`,
        ),
        fetch(
          `https://api.bybit.com/v5/market/kline?category=linear&symbol=${pair.toUpperCase()}USDT&interval=5&limit=1`,
        ),
      ]);

      const [bitstampData, bybitData] = await Promise.all([
        bitstampResponse.json(),
        bybitResponse.json(),
      ]);

      let bitstampBar = null;
      let bybitBar = null;

      // Process Bitstamp data
      if (
        bitstampData &&
        bitstampData.data &&
        bitstampData.data.ohlc &&
        bitstampData.data.ohlc.length > 0
      ) {
        const bar = bitstampData.data.ohlc[0];
        bitstampBar = {
          time: parseInt(bar.timestamp),
          open: parseFloat(bar.open),
          high: parseFloat(bar.high),
          low: parseFloat(bar.low),
          close: parseFloat(bar.close),
          volume: parseFloat(bar.volume),
        };
      }

      // Process Bybit data
      if (
        bybitData &&
        bybitData.retCode === 0 &&
        bybitData.result &&
        bybitData.result.list &&
        bybitData.result.list.length > 0
      ) {
        const item = bybitData.result.list[0];
        const t = Math.floor(parseInt(item[0]) / 1000);
        const nt = Math.floor(t / 300) * 300; // Align to 5-minute intervals
        bybitBar = {
          time: nt,
          open: parseFloat(item[1]),
          high: parseFloat(item[2]),
          low: parseFloat(item[3]),
          close: parseFloat(item[4]),
          volume: parseFloat(item[5]),
        };
      }

      return { bitstampBar, bybitBar };
    } catch (error) {
      window.errorHandler.handleError(error, "Error fetching latest bar data");
      return { bitstampBar: null, bybitBar: null };
    }
  }

  /**
   * Updates chart with new bar data from polling
   */
  function updateChartWithNewBar(state, bar) {
    if (!state || !state.chart || !state.chart.priceSeries) return;

    // Defensive: ensure bar.time is a number
    bar.time =
      typeof bar.time === "object" &&
      bar.time !== null &&
      typeof bar.time.getTime === "function"
        ? Math.floor(bar.time.getTime() / 1000)
        : Number(bar.time);
    if (!Number.isFinite(bar.time)) {
      logger.warn(
        "[pollingManager.js] Invalid bar.time in updateChartWithNewBar:",
        bar,
      );
      return;
    }

    // Check if this bar is newer than the current bar
    const currentBar = state.currentBars.currentBarBitstamp;
    if (currentBar && bar.time <= currentBar.time) {
      logger.log(
        `[pollingManager.js] Skipping older bar from polling: ${new Date(bar.time * 1000).toISOString()}`,
      );
      return;
    }

    // Add new bar to price data
    state.data.priceData.push(bar);
    if (state.data.priceData.length > state.config.maxBars) {
      state.data.priceData.shift();
    }

    // Update chart
    state.chart.priceSeries.update(bar);

    // Update current bar reference
    state.currentBars.currentBarBitstamp = bar;

    logger.log(
      `[pollingManager.js] Chart updated with new bar from polling: ${new Date(bar.time * 1000).toISOString()}`,
    );
  }

  /**
   * Updates all indicators with new bar data (heavy work, but only every 5 minutes)
   */
  function updateAllIndicators(state, latestData) {
    const indicatorStart = performance.now();

    // Update data stores with fresh data
    if (window.PS && typeof window.PS.setCVDPriceData === "function") {
      window.PS.setCVDPriceData(state.data.priceData);
    }
    if (
      window.PS &&
      typeof window.PS.setPerpCVDPriceData === "function" &&
      state.data.alignedBybit
    ) {
      window.PS.setPerpCVDPriceData(state.data.alignedBybit);
    }
    if (
      window.PS &&
      typeof window.PS.setPerpImbalancePriceData === "function" &&
      state.data.alignedBybit &&
      state.data.alignedBitstamp
    ) {
      window.PS.setPerpImbalancePriceData({
        bybitBars: state.data.alignedBybit,
        bitstampBars: state.data.alignedBitstamp,
      });
    }

    // Update chart indicators with fresh data
    if (state.chart?.cvdComponents && window.cvdModule?.initializeCVDData) {
      window.cvdModule.initializeCVDData(
        state.chart.cvdComponents,
        state.data.priceData,
      );
    }
    if (
      state.chart?.perpCvdComponents &&
      window.perpCvdModule?.initializeCVDData
    ) {
      window.perpCvdModule.initializeCVDData(
        state.chart.perpCvdComponents,
        state.data.alignedBybit,
      );
    }
    if (
      state.chart?.perpImbalanceComponents &&
      window.perpImbalanceModule?.initializeImbalanceData
    ) {
      window.perpImbalanceModule.initializeImbalanceData(
        state.chart.perpImbalanceComponents,
        state.data.alignedBybit,
        state.data.alignedBitstamp,
      );
    }

    const indicatorDuration = performance.now() - indicatorStart;
    logger.log(
      `[pollingManager.js] All indicators updated in ${indicatorDuration.toFixed(1)}ms`,
    );
  }

  /**
   * Stops chart polling
   */
  function stopChartPolling() {
    if (chartPollingInterval) {
      clearInterval(chartPollingInterval);
      chartPollingInterval = null;
      logger.log(`[pollingManager.js] Chart polling stopped`);
    }
  }

  // Export the polling management functions
  window.pollingManager = {
    startChartPolling,
    stopChartPolling,
    fetchLatestBarData,
    updateChartWithNewBar,
    updateAllIndicators,
  };
})();
