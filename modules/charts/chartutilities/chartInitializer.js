// modules/charts/chartutilities/chartInitializer.js
// Handles chart initialization and setup

(function () {
  const logger = window.logger
    ? window.logger.createLogger("ChartInitializer")
    : console;
  // Use global configManager, domCache
  if (!window.configManager || !window.domCache) {
    throw new Error("configManager and domCache must be defined globally");
  }

  const CONFIG = window.configManager.CONFIG;
  const utils = window.configManager.utils;
  const domCache = window.domCache;

  /**
   * Initializes the chart and meter for a trading pair.
   * @param {HTMLElement} container - Chart container element.
   * @param {Object} data - Pre-calculated data.
   * @param {string} pair - Trading pair.
   * @param {Function} progressiveLoader - Progressive loading callback.
   * @returns {Object|null} Chart state or null on error.
   */
  function initializeChartAndMeter(container, data, pair, progressiveLoader) {
    logger.log("[chartInitializer] Starting chart initialization for", pair);
    logger.log("[chartInitializer] Container:", !!container);
    logger.log("[chartInitializer] Data available:", !!data);

    const overlay = domCache.get("overlay");
    let userHasScrolled = false;
    const filteredPriceData = utils.filterValidBars(data?.priceData);
    logger.log(
      "[chartInitializer] Filtered price data length:",
      filteredPriceData?.length,
    );

    if (!data || !filteredPriceData.length) {
      overlay.textContent = `Failed to load ${pair} data (no price data)`;
      overlay.style.display = "block";
      logger.error(
        `[initializeChartAndMeter] No price data for ${pair}:`,
        data,
      );
      return null;
    }

    // Hide overlay immediately when we have data
    if (overlay && overlay.style) {
      overlay.style.display = "none";
      overlay.style.visibility = "hidden";
      overlay.style.opacity = "0";
    }

    // Also hide the specific pair loading overlay
    const pairLoadingOverlay = document.getElementById("pair-loading-overlay");
    if (pairLoadingOverlay) {
      pairLoadingOverlay.style.display = "none";
      pairLoadingOverlay.style.visibility = "hidden";
      pairLoadingOverlay.style.opacity = "0";
      logger.log("[chartInitializer] Pair loading overlay hidden");
    }
    const priceChartContainer = domCache.get("priceChartContainer");
    if (priceChartContainer) priceChartContainer.style.height = "100%";
    const chartConfig = {
      ...CONFIG,
      ticker: {
        symbol: pair,
        bitstampOrderBook: `order_book_${pair.toLowerCase()}usd`,
        bitstampTrades: `live_trades_${pair.toLowerCase()}usd`,
        bybitTrades: `publicTrade.${pair.toUpperCase()}USDT`,
      },
    };
    let priceChartElement = domCache.get("priceChartElement");
    if (!priceChartElement) {
      priceChartElement = container.querySelector(".price-chart");
      domCache.set("priceChartElement", priceChartElement);
    }
    logger.log(
      "[chartInitializer] Price chart element found:",
      !!priceChartElement,
    );

    if (!priceChartElement) {
      overlay.textContent = `Chart container missing for ${pair}`;
      overlay.style.display = "block";
      logger.error(
        `[initializeChartAndMeter] .price-chart element not found for ${pair}`,
      );
      return null;
    }
    let priceChart;
    try {
      const chartColors = window.configManager?.CONFIG?.chart
        ?.defaultColors || {
        background: "#0f141a",
        text: "#D3D3D3",
        grid: "#2A2A2A",
      };
      priceChart = LightweightCharts.createChart(priceChartElement, {
        autoSize: true,
        layout: {
          background: { color: chartColors.background, type: "solid" },
          textColor: chartColors.text,
          fontSize: 10,
          attributionLogo: false,
        },
        panes: [{}, { height: 150, visible: true }],
        grid: { vertLines: { visible: false }, horzLines: { visible: false } },
        crosshair: {
          mode: LightweightCharts.CrosshairMode.Normal,
          vertLine: { labelBackgroundColor: chartColors.grid },
          horzLine: { labelBackgroundColor: chartColors.grid },
        },
        timeScale: {
          timeVisible: true,
          secondsVisible: false,
          borderColor: chartColors.grid,
          lockVisibleTimeRangeOnResize: true,
          fixLeftEdge: false,
          fixRightEdge: false,
          kineticScroll: { touch: true, mouse: false },
          tickMarkFormatter: (t) =>
            new Date(t * 1000).toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            }),
        },
        rightPriceScale: {
          borderColor: chartColors.grid,
          autoScale: true,
          entireTextOnly: false,
        },
        trackingMode: {
          exitMode: LightweightCharts.TrackingModeExitMode.OnNextTap,
        },
        handleScale: { axisPressedMouseMove: { time: true, price: true } },
        handleScroll: { vertTouchDrag: true, horzTouchDrag: true },
      });
    } catch (err) {
      overlay.textContent = `Chart initialization failed for ${pair}`;
      overlay.style.display = "block";
      logger.error(
        `[initializeChartAndMeter] Chart creation failed for ${pair}:`,
        err,
        data,
      );
      return null;
    }

    logger.log("[chartInitializer] Chart created successfully");
    priceChart.subscribeCrosshairMove((param) => {
      priceChart.applyOptions({
        crosshair: {
          mode: param.point
            ? LightweightCharts.CrosshairMode.Normal
            : LightweightCharts.CrosshairMode.Hidden,
        },
      });
    });
    const chartExtras = { watermark: null, upDownMarkers: null };
    if (window.LightweightCharts?.createTextWatermark) {
      const panes = priceChart.panes();
      if (panes?.length > 0)
        chartExtras.watermark = window.LightweightCharts.createTextWatermark(
          panes[0],
          {
            horzAlign: "right",
            vertAlign: "bottom",
            lines: [
              {
                text: `${pair.toUpperCase()}USD`,
                color: "rgba(255, 255, 255, 0.3)",
                fontSize: 28,
                fontStyle: "bold",
                fontFamily: "Arial",
              },
            ],
            padding: { right: 28 },
          },
        );
    }
    const candleColors = window.configManager?.CONFIG?.chart?.candlestick || {
      upColor: "#AAAAAA",
      downColor: "#AAAAAA",
      borderColor: "#AAAAAA",
      wickUpColor: "#AAAAAA",
      wickDownColor: "#AAAAAA",
    };
    const priceSeries = priceChart.addSeries(
      LightweightCharts.CandlestickSeries,
      {
        ...candleColors,
        lastValueVisible: true,
        priceLineVisible: true,
        priceLineSource: LightweightCharts.PriceLineSource.LastBar,
        priceFormat: { type: "price", precision: 2, minMove: 0.01 },
      },
    );
    const effectiveData = data || {
      priceData: [],
      bands: { t1: 0, t2: 0, b1: 0, b2: 0, time: 0 },
      vwap: {
        vwapValue: 0,
        upperBand: 0,
        lowerBand: 0,
        upperMidline: 0,
        lowerMidline: 0,
      },
      emaBands: { ema: 0, upper: 0, lower: 0, time: 0 },
      liqsData: [{ time: Math.floor(Date.now() / 1000), value: 0 }],
      liqsRawWindow: [],
      sums: { perpSum: 0, spotSum: 0 },
      alignedBybit: [],
      alignedBitstamp: [],
      timing: {
        firstTime: Math.floor(Date.now() / 1000),
        lastTime: Math.floor(Date.now() / 1000),
      },
    };
    const createPriceLine = (price, title) => {
      if (!Number.isFinite(price)) {
        logger.warn(
          `[initializeChartAndMeter] Skipping price line for ${title} due to invalid price:`,
          price,
        );
        return null;
      }
      const lineColors = window.configManager?.CONFIG?.chart?.defaultColors || {
        grid: "#2A2A2A",
      };
      return priceSeries.createPriceLine({
        price,
        color: lineColors.grid,
        lineWidth: 1,
        title,
      });
    };
    const priceLines = {
      b2Upper: createPriceLine(effectiveData.bands.t2, "2σMR"),
      b1Upper: createPriceLine(effectiveData.bands.t1, "1σMR"),
      b1Lower: createPriceLine(effectiveData.bands.b1, "1σMR"),
      b2Lower: createPriceLine(effectiveData.bands.b2, "2σMR"),
      stdPlus2: createPriceLine(effectiveData.vwap.upperBand, "std+2"),
      stdPlus1: createPriceLine(effectiveData.vwap.upperMidline, "std+1"),
      vwap: createPriceLine(effectiveData.vwap.vwapValue, "vwap"),
      stdMinus1: createPriceLine(effectiveData.vwap.lowerMidline, "std-1"),
      stdMinus2: createPriceLine(effectiveData.vwap.lowerBand, "std-2"),
    };
    // Throttled chart data update to avoid rAF violations
    let lastChartUpdate = 0;
    const minChartUpdateInterval = 33; // ~30fps
    requestAnimationFrame(() => {
      const now = Date.now();
      if (now - lastChartUpdate >= minChartUpdateInterval) {
        lastChartUpdate = now;
        try {
          priceSeries.setData(utils.filterValidBars(effectiveData.priceData));
        } catch (err) {
          overlay.textContent = `Failed to set chart data for ${pair}`;
          overlay.style.display = "block";
          logger.error(
            `[initializeChartAndMeter] setData failed for ${pair}:`,
            err,
            effectiveData.priceData,
          );
        }
      } else {
        setTimeout(
          () => {
            requestAnimationFrame(() => {
              try {
                priceSeries.setData(
                  utils.filterValidBars(effectiveData.priceData),
                );
              } catch (err) {
                overlay.textContent = `Failed to set chart data for ${pair}`;
                overlay.style.display = "block";
                logger.error(
                  `[initializeChartAndMeter] setData failed for ${pair}:`,
                  err,
                  effectiveData.priceData,
                );
              }
            });
          },
          minChartUpdateInterval - (now - lastChartUpdate),
        );
      }
    });
    if (window.LightweightCharts?.createUpDownMarkers) {
      const markerSeries = priceChart.addSeries(LightweightCharts.LineSeries, {
        lineWidth: 0,
        lastValueVisible: false,
        priceLineVisible: false,
        crosshairMarkerVisible: false,
        visible: false,
        title: "",
      });
      markerSeries.setData(
        utils
          .filterValidBars(effectiveData.priceData)
          .map((b) => ({ time: b.time, value: b.close })),
      );
      const markerConfig = window.configManager?.CONFIG?.chart
        ?.upDownMarkers || {
        threshold: 0.005,
        upColor: "rgba(0, 255, 255, 0.7)",
        downColor: "rgba(255, 85, 85, 0.7)",
        size: 0.5,
      };
      chartExtras.upDownMarkers = window.LightweightCharts.createUpDownMarkers(
        markerSeries,
        markerConfig,
      );
      chartExtras.markerSeries = markerSeries;
    }
    if (typeof progressiveLoader === "function") {
      const guardedLoader = (series) => {
        progressiveLoader({
          update: (bar) => {
            if (
              typeof versionToken !== "undefined" &&
              state &&
              state._versionToken !== versionToken
            )
              return;
            const validBar = utils.filterValidBars([bar])[0];
            if (validBar) {
              series.update(validBar);
              if (chartExtras.markerSeries)
                chartExtras.markerSeries.update({
                  time: validBar.time,
                  value: validBar.close,
                });
            }
          },
          setData: (bars) => series.setData(utils.filterValidBars(bars)),
        });
      };
      guardedLoader(priceSeries);
    }
    const fullRange =
      effectiveData.timing.lastTime +
      CONFIG.barInterval * CONFIG.futureBars -
      effectiveData.timing.firstTime;
    const halfRange = fullRange / 2;
    const midPoint = effectiveData.timing.lastTime - halfRange / 2;
    let cvdComponents = null;
    if (window.cvdModule?.createCVDChart) {
      cvdComponents = window.cvdModule.createCVDChart(container, priceChart);
      priceChart.applyOptions({
        layout: {
          background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        },
      });
      const syncResources = window.cvdModule.synchronizeCharts(
        cvdComponents,
        priceChart,
      );
      cvdComponents.syncResources = syncResources;
      window.cvdModule.initializeCVDData(
        cvdComponents,
        utils.filterValidBars(effectiveData.priceData),
      );
    } else {
      logger.warn("CVD module not loaded, skipping CVD chart creation");
    }
    let perpCvdComponents = null;
    if (window.perpCvdModule?.createCVDChart) {
      perpCvdComponents = window.perpCvdModule.createCVDChart(
        container,
        priceChart,
      );
      const perpSyncResources = window.perpCvdModule.synchronizeCharts(
        perpCvdComponents,
        priceChart,
      );
      perpCvdComponents.syncResources = perpSyncResources;
      const bybitBars = utils.filterValidBars(effectiveData.alignedBybit);
      window.perpCvdModule.initializeCVDData(perpCvdComponents, bybitBars);
    } else {
      logger.warn(
        "PerpCVD module not loaded, skipping Perp CVD chart creation",
      );
    }

    // --- Perp Imbalance Integration ---
    let perpImbalanceComponents = null;
    if (window.perpImbalanceModule?.createImbalanceChart) {
      perpImbalanceComponents = window.perpImbalanceModule.createImbalanceChart(
        container,
        priceChart,
      );
      const perpImbSyncResources = window.perpImbalanceModule.synchronizeCharts(
        perpImbalanceComponents,
        priceChart,
      );
      perpImbalanceComponents.syncResources = perpImbSyncResources;
      const bybitBars = utils.filterValidBars(effectiveData.alignedBybit);
      const bitstampBars = utils.filterValidBars(effectiveData.alignedBitstamp);
      window.perpImbalanceModule.initializeImbalanceData(
        perpImbalanceComponents,
        bybitBars,
        bitstampBars,
      );
      if (
        window.PS &&
        typeof window.PS.setPerpImbalancePriceData === "function"
      ) {
        window.PS.setPerpImbalancePriceData({
          bybitBars: bybitBars,
          bitstampBars: bitstampBars,
        });
      }
    } else {
      logger.warn(
        "PerpImbalance module not loaded, skipping Perp Imbalance chart creation",
      );
    }

    requestAnimationFrame(() => {
      priceChart.timeScale().fitContent();
      if (Number.isFinite(midPoint) && Number.isFinite(halfRange)) {
        priceChart.timeScale().setVisibleRange({
          from: midPoint - halfRange / 2,
          to: midPoint + halfRange / 2,
        });
      }

      // Apply savedVisibleLogicalRange if it exists
      try {
        const currentChartState = window.chartStates.get(pair);
        if (
          currentChartState &&
          currentChartState.savedVisibleLogicalRange &&
          typeof currentChartState.savedVisibleLogicalRange.from === "number" &&
          typeof currentChartState.savedVisibleLogicalRange.to === "number"
        ) {
          logger.log(
            `[Charts-${pair}] Applying saved visibleLogicalRange:`,
            currentChartState.savedVisibleLogicalRange,
          );
          priceChart
            .timeScale()
            .setVisibleLogicalRange(currentChartState.savedVisibleLogicalRange);
          currentChartState.savedVisibleLogicalRange = null;
        }
      } catch (e) {
        logger.warn(
          `[Charts-${pair}] Error applying saved visibleLogicalRange:`,
          e.message,
        );
      }
    });
    priceChart.timeScale().subscribeVisibleLogicalRangeChange((range) => {
      const logicalRange = priceChart.timeScale().getVisibleLogicalRange();
      const bars = state?.data?.priceData || [];
      if (logicalRange && bars.length > 0) {
        const lastBarTime = bars[bars.length - 1].time;
        userHasScrolled = logicalRange.to < lastBarTime - 0.5;
      }
    });
    const state = {
      chart: {
        priceChart,
        priceSeries,
        cvdComponents,
        perpCvdComponents,
        perpImbalanceComponents,
        priceLines,
        extras: chartExtras,
      },
      config: chartConfig,
      data: {
        priceData: filteredPriceData,
        alignedBybit: utils.filterValidBars(effectiveData.alignedBybit),
        alignedBitstamp: utils.filterValidBars(effectiveData.alignedBitstamp),
        openInterestData: effectiveData.openInterestData || [],
        orderBook: { bids: [], asks: [] },
        liquidationsData: [],
      },
      caches: effectiveData.caches || {
        twapCache: { priceVolume: 0, totalVolume: 0, value: 0 },
        vwapCache: { priceVolume: 0, totalVolume: 0, anchor: null },
        stdDevCache: {},
        vwapActive: false,
      },
      sums: effectiveData.sums || { perpSum: 0, spotSum: 0 },
      liqs: { liqsRawWindow: effectiveData.liqsRawWindow || [] },
      timing: {
        firstTime: effectiveData.timing.firstTime,
        lastTime: effectiveData.timing.lastTime,
        lastPriceUpdateTime: effectiveData.timing.lastTime,
      },
      currentBars: {
        currentBarBitstamp: filteredPriceData[filteredPriceData.length - 1]
          ? { ...filteredPriceData[filteredPriceData.length - 1] }
          : {
              time:
                Math.floor(Date.now() / 1000 / CONFIG.barInterval) *
                CONFIG.barInterval,
              open: null,
              high: null,
              low: null,
              close: null,
              volume: 0,
            },
        currentBarBybit: utils.filterValidBars(effectiveData.alignedBybit)[
          utils.filterValidBars(effectiveData.alignedBybit).length - 1
        ]
          ? {
              ...utils.filterValidBars(effectiveData.alignedBybit)[
                utils.filterValidBars(effectiveData.alignedBybit).length - 1
              ],
            }
          : {
              time:
                Math.floor(Date.now() / 1000 / CONFIG.barInterval) *
                CONFIG.barInterval,
              open: null,
              high: null,
              low: null,
              close: null,
              volume: 0,
            },
      },
      chartExtras,
      readiness: { isBitstampReady: false, isBybitReady: false },
      isActive: true,
      throttledFunctions: {
        throttledPriceUpdate: (bar) => {
          if (
            !state.isActive ||
            !bar ||
            !Number.isFinite(bar.time) ||
            !Number.isFinite(bar.close)
          )
            return;
          const validBar = utils.filterValidBars([bar])[0];
          if (!validBar) return;
          const lastBar = state.data.priceData[state.data.priceData.length - 1];
          if (!lastBar || validBar.time >= lastBar.time) {
            if (!lastBar || validBar.time > lastBar.time) {
              state.data.priceData.push(validBar);
              if (state.data.priceData.length > CONFIG.maxBars)
                state.data.priceData.shift();
              if (
                window.PS &&
                typeof window.PS.setCVDPriceData === "function"
              ) {
                window.PS.setCVDPriceData(state.data.priceData);
              }
              const now = Date.now();
              const minUpdateInterval = 32;

              if (
                !state.lastChartUpdate ||
                now - state.lastChartUpdate >= minUpdateInterval
              ) {
                state.lastChartUpdate = now;
                requestAnimationFrame(() => {
                  priceSeries.update(validBar);
                  if (state.chart.extras?.markerSeries)
                    state.chart.extras.markerSeries.update({
                      time: validBar.time,
                      value: validBar.close,
                    });
                  if (state.liquidationManager?.checkForCandleClose) {
                    state.liquidationManager.checkForCandleClose(
                      bar.time * 1000,
                    );

                    if (
                      state.chart.cvdComponents &&
                      window.cvdModule?.renderPendingCVDUpdates
                    ) {
                      window.cvdModule.renderPendingCVDUpdates(
                        state.chart.cvdComponents,
                      );
                    }
                    if (
                      state.chart.perpCvdComponents &&
                      window.perpCvdModule?.renderPendingCVDUpdates
                    ) {
                      window.perpCvdModule.renderPendingCVDUpdates(
                        state.chart.perpCvdComponents,
                      );
                    }
                  }
                  window.deltaOiProfileManager?.updateProfile?.(
                    state.config?.ticker?.symbol,
                  ) ||
                    state.deltaOiProfile?.update?.() ||
                    (() => {});
                  if (!userHasScrolled) {
                  }
                });
              }
              if (Math.random() < 0.1)
                window.memoryManagement?.cleanupHistoricalData?.(state);
            }
          }
        },
        throttledCloseUpdate: (bar) => {
          if (
            !state.isActive ||
            !bar ||
            !Number.isFinite(bar.time) ||
            !Number.isFinite(bar.close)
          )
            return;
          const validBar = utils.filterValidBars([bar])[0];
          if (!validBar) return;
          const lastBar = state.data.priceData[state.data.priceData.length - 1];
          if (lastBar && validBar.time === lastBar.time) {
            lastBar.close = validBar.close;
            requestAnimationFrame(() => {
              priceSeries.update(lastBar);
              if (state.chart.extras?.markerSeries)
                state.chart.extras.markerSeries.update({
                  time: validBar.time,
                  value: validBar.close,
                });
              if (state.liquidationManager?.checkForCandleClose) {
                state.liquidationManager.checkForCandleClose(Date.now());
              }
            });
          }
        },
        updateOrderBook: (state) =>
          window.OrderbookManager?.updateOrderBookLines?.(state),
        throttledMeterUpdate: utils.throttle(() => {}, 200),
        updateEMABands: () => {
          if (
            !state.isActive ||
            !state.data.priceData.length ||
            !window.chartIndicators?.calculateEMABands
          )
            return;
          const emaBands = window.chartIndicators.calculateEMABands(
            state.data.priceData,
          );
          state.data.emaBands = emaBands;
          if (state.chart.priceLines.ema) {
            state.chart.priceLines.ema.applyOptions({ price: emaBands.ema });
            state.chart.priceLines.emaUpper.applyOptions({
              price: emaBands.upper,
            });
            state.chart.priceLines.emaLower.applyOptions({
              price: emaBands.lower,
            });
          }
        },
      },
      largeOrderLines: [],
    };

    logger.log("[chartInitializer] Chart state created successfully");
    logger.log("[chartInitializer] State summary:", {
      hasConfig: !!state.config,
      hasData: !!state.data,
      hasChart: !!state.chart,
      hasPriceSeries: !!state.chart.priceSeries,
      priceDataLength: state.data?.priceData?.length || 0,
    });
    state.liquidationManager = {
      addLiquidation: () => {},
      isActiveFn: () => false,
      checkForCandleClose: () => {},
      cleanup: () => {},
      destroy: () => {},
      processLiquidation: () => {},
      dollarThreshold: localStorage.getItem("liquidationThreshold")
        ? parseFloat(localStorage.getItem("liquidationThreshold"))
        : 100000,
    };
    state.whaleAlertManager = {
      addWhaleAlert: () => {},
      isActiveFn: () => false,
      checkForCandleClose: () => {},
      cleanup: () => {},
      destroy: () => {},
      dollarThreshold: localStorage.getItem("whaleAlertThreshold")
        ? parseFloat(localStorage.getItem("whaleAlertThreshold"))
        : 100000,
    };
    state.chartContainer =
      domCache.get("priceChartElement") ||
      container.querySelector(".price-chart");

    function tryInitDeltaOiProfile(retries = 5, delay = 200) {
      // Enhanced validation
      const hasValidState =
        state &&
        state.config &&
        state.config.ticker &&
        state.config.ticker.symbol &&
        state.chartContainer;

      const hasManager =
        window.deltaOiProfileManager &&
        typeof window.deltaOiProfileManager.initializeProfiles === "function";

      if (hasValidState && hasManager) {
        try {
          const result = window.deltaOiProfileManager.initializeProfiles(state);
          if (result) {
            console.log(
              "DeltaOI profile initialized successfully for",
              state.config.ticker.symbol,
            );
          }

          // Staggered updates with better error handling
          [100, 500, 1000, 2000, 3000].forEach((d) =>
            setTimeout(() => {
              try {
                const lc = document.getElementById("liquidation-console");
                if (lc) {
                  lc.style.cssText =
                    "display: block; visibility: visible; opacity: 1";
                }

                // Try multiple update paths
                if (window.deltaOiProfileManager?.updateProfile) {
                  window.deltaOiProfileManager.updateProfile(
                    state.config.ticker.symbol,
                  );
                } else if (state.deltaOiProfile?.update) {
                  state.deltaOiProfile.update();
                }

                if (window.updateSize) {
                  window.updateSize();
                }
              } catch (updateError) {
                console.warn("Delta OI profile update error:", updateError);
              }
            }, d),
          );
        } catch (initError) {
          console.warn("Delta OI profile initialization error:", initError);
          if (retries > 0) {
            setTimeout(
              () => tryInitDeltaOiProfile(retries - 1, delay * 1.5),
              delay,
            );
          }
        }
      } else if (retries > 0) {
        // Wait longer between retries and provide more specific feedback
        const nextDelay = delay * 1.2; // Gradually increase delay
        setTimeout(
          () => tryInitDeltaOiProfile(retries - 1, nextDelay),
          nextDelay,
        );
      } else {
        if (retries <= 1) {
          console.warn(
            "DeltaOI Profile failed to initialize after all retries:",
            {
              hasValidState,
              hasManager,
              symbol: state?.config?.ticker?.symbol,
            },
          );
        }
      }
    }

    // Wait for deltaOI profile manager to be available
    const waitForManager = () => {
      if (window.deltaOiProfileManager) {
        tryInitDeltaOiProfile();
      } else {
        setTimeout(waitForManager, 100);
      }
    };

    waitForManager();

    requestAnimationFrame(() => {
      // Ensure overlay is hidden
      if (overlay && overlay.style) {
        overlay.style.display = "none";
        overlay.style.visibility = "hidden";
        overlay.style.opacity = "0";
        logger.log("[chartInitializer] Loading overlay hidden");
      }

      // Also hide the specific pair loading overlay
      const pairLoadingOverlay = document.getElementById(
        "pair-loading-overlay",
      );
      if (pairLoadingOverlay) {
        pairLoadingOverlay.style.display = "none";
        pairLoadingOverlay.style.visibility = "hidden";
        pairLoadingOverlay.style.opacity = "0";
        logger.log(
          "[chartInitializer] Pair loading overlay hidden in requestAnimationFrame",
        );
      }

      logger.log("[chartInitializer] Chart rendering completed");
      logger.log("[chartInitializer] Chart element dimensions:", {
        width: priceChartElement?.offsetWidth,
        height: priceChartElement?.offsetHeight,
        visible: priceChartElement?.style?.display !== "none",
      });

      // Force a resize to ensure proper rendering
      if (priceChart && typeof priceChart.resize === "function") {
        setTimeout(() => {
          try {
            priceChart.resize();
            logger.log("[chartInitializer] Chart resized");
          } catch (e) {
            logger.warn("[chartInitializer] Chart resize failed:", e);
          }
        }, 100);
      }
    });
    state.refreshToken = (state.refreshToken || 0) + 1;
    return state;
  }

  // Export the function
  window.chartInitializer = {
    initializeChartAndMeter,
  };
})();
