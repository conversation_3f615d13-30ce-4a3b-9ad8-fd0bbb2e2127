// modules/charts/chartutilities/eventHandlers.js
// Handles event listeners and event handling logic

/**
 * Event Handlers
 * Handles global and chart-specific event handling
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("EventHandlers")
    : console;
  // Use global dependencies
  if (!window.domCache || !window.chartStates || !window.currentPair) {
    throw new Error(
      "domCache, chartStates, and currentPair must be defined globally",
    );
  }

  const domCache = window.domCache;
  const chartStates = window.chartStates;
  const currentPair = window.currentPair;

  /**
   * Handles WebSocket visibility restoration events
   */
  function handleWebSocketVisibilityRestored(event) {
    logger.log(`WebSocket visibility restored for bitstamp:`, event.detail);

    // Sync chart data after page becomes visible to fix price desync
    const currentState = chartStates.get(currentPair);
    if (currentState && currentState.isActive) {
      try {
        // Fetch latest bar to sync chart with current data
        const latestData = window.fetchLatestBarData?.(currentPair);
        if (latestData && latestData.bitstampBar) {
          // Update the last bar in the chart to current data
          const lastBar =
            currentState.data.priceData[currentState.data.priceData.length - 1];
          if (
            lastBar &&
            Math.abs(lastBar.time - latestData.bitstampBar.time) <= 300
          ) {
            // Same bar period - update the existing bar
            Object.assign(lastBar, latestData.bitstampBar);
            currentState.chart.priceSeries.update(lastBar);
          } else {
            // New bar period - add the new bar
            window.updateChartWithNewBar?.(
              currentState,
              latestData.bitstampBar,
            );
          }
          logger.log(
            `Synced chart data after visibility restore for ${currentPair}`,
          );
        }
      } catch (error) {
        window.errorHandler?.handleError(
          error,
          "Failed to sync chart data after visibility restore",
        );
      }
    }
  }

  /**
   * Handles computer sleep events
   */
  function handleComputerSleep() {
    logger.log("[eventHandlers.js] Computer sleep detected");
    // Mark all chart states as potentially stale
    chartStates.forEach((state, pair) => {
      if (state) {
        state._sleepDetected = true;
        state._lastSleepTime = Date.now();
      }
    });
  }

  /**
   * Handles computer wake events
   */
  function handleComputerWake(event) {
    const sleepDuration = event.detail?.sleepDuration || 0;
    logger.log(
      `[eventHandlers.js] Computer wake detected after ${Math.round(sleepDuration / 1000)}s sleep`,
    );

    // Sync all chart data after wake
    setTimeout(async () => {
      for (const [pair, state] of chartStates.entries()) {
        if (state && state.isActive && state._sleepDetected) {
          try {
            logger.log(
              `[eventHandlers.js] Syncing chart data for ${pair} after wake`,
            );

            // Fetch latest data to sync chart
            const latestData = window.fetchLatestBarData?.(pair);
            if (latestData && latestData.bitstampBar) {
              // Update the last bar in the chart to current data
              const lastBar =
                state.data.priceData[state.data.priceData.length - 1];
              if (
                lastBar &&
                Math.abs(lastBar.time - latestData.bitstampBar.time) <= 300
              ) {
                // Same bar period - update the existing bar
                Object.assign(lastBar, latestData.bitstampBar);
                state.chart.priceSeries.update(lastBar);
              } else {
                // New bar period - add the new bar
                window.updateChartWithNewBar?.(state, latestData.bitstampBar);
              }
              logger.log(
                `[eventHandlers.js] Synced chart data for ${pair} after wake`,
              );
            }

            state._sleepDetected = false;
          } catch (error) {
            window.errorHandler?.handleError(
              error,
              `Failed to sync chart data for ${pair} after wake`,
            );
          }
        }
      }
    }, 3000); // 3 second delay to allow system to stabilize
  }

  /**
   * Handles DOM content loaded events
   */
  function handleDOMContentLoaded() {
    const style = document.createElement("style");
    style.textContent = `.liq-controls{position:absolute;bottom:10px;right:10px;display:flex;gap:10px;z-index:100;padding:5px 10px;background-color:rgba(15,20,26,0.8);border-radius:4px;box-shadow:0 2px 5px rgba(0,0,0,0.3)}.liq-apply-btn{background:#444;color:#fff;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:11px}.liq-apply-btn:hover{background:#555}.liq-threshold-input{background:#333;color:#fff;border:1px solid #555;padding:4px;border-radius:3px;width:80px}.settings-dropdown{position:relative}.settings-dropdown-content{display:none;background-color:#1a2026;border:1px solid #555;border-radius:4px;padding:10px;position:absolute;bottom:100%;right:0;min-width:200px;z-index:101}.settings-dropdown-content.show{display:block}.settings-group{margin-bottom:10px}.settings-group-title{font-size:12px;color:#fff;margin-bottom:5px}.settings-group-content{display:flex;gap:5px;align-items:center}.settings-btn{background:#444;color:#fff;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;font-size:11px}
        .tv-lightweight-charts .pane-legend-item[data-title*='1 to -1'] {opacity: 0 !important; color: transparent !important;}`;
    document.head.appendChild(style);
    setTimeout(window.settingsManager?.initSettingsButton, 1000);
    setTimeout(window.settingsManager?.ensureLiquidationConsoleTitle, 500);

    // --- ALTS Dropdown Logic ---
    // (Removed: now handled by inline script in index.html)
    // --- END ALTS Dropdown Logic ---

    const observer = new MutationObserver((m) => {
      let shouldEnsure = false;
      for (const mu of m) {
        if (
          mu.type === "childList" &&
          (mu.target.id === "liquidation-console" ||
            mu.target.closest("#liquidation-console"))
        ) {
          shouldEnsure = true;
          break;
        }
        if (
          mu.type === "attributes" &&
          mu.attributeName === "style" &&
          ["liquidation-console", "liquidation-console-title"].includes(
            mu.target.id,
          )
        ) {
          shouldEnsure = true;
          break;
        }
      }
      if (shouldEnsure) window.settingsManager?.ensureLiquidationConsoleTitle();
    });
    const lc = document.getElementById("liquidation-console");
    if (lc)
      observer.observe(lc, {
        childList: true,
        attributes: true,
        attributeFilter: ["style"],
        subtree: true,
      });
    const cc = document.querySelector(".price-chart-container");
    if (cc) observer.observe(cc, { childList: true });
    const debouncedResize = (() => {
      let cbs = new Set();
      let t;
      const add = (cb) => {
        if (typeof cb === "function") cbs.add(cb);
        return () => cbs.delete(cb);
      };
      const exec = () =>
        cbs.forEach((cb) => {
          try {
            cb();
          } catch (e) {}
        });
      const handle = () => {
        clearTimeout(t);
        t = setTimeout(exec, 100);
      };
      window.addEventListener("resize", handle, { passive: true });
      return { addCallback: add, trigger: handle };
    })();
    const ps = document.querySelector(".tv-lightweight-charts .pane-separator");
    if (ps) {
      ps.addEventListener("mouseup", debouncedResize.trigger, {
        passive: true,
      });
      new MutationObserver(debouncedResize.trigger).observe(ps, {
        attributes: true,
        attributeFilter: ["style"],
      });
    }
    debouncedResize.addCallback(window.updateSize);
    const container = domCache.get("container");
    if (container) {
      const ro = new ResizeObserver(() => window.updateSize());
      ro.observe(container);
      window.addEventListener("beforeunload", () => ro.disconnect());
    }
  }

  /**
   * Initializes all event handlers
   */
  function initEventHandlers() {
    // Handle visibility restoration to fix chart price stability
    window.addEventListener(
      "websocket-visibility-restored-bitstamp",
      handleWebSocketVisibilityRestored,
    );

    // CRITICAL FIX: Sleep/wake event handlers
    window.addEventListener("computer-sleep", handleComputerSleep);
    window.addEventListener("computer-wake", handleComputerWake);

    document.addEventListener("DOMContentLoaded", handleDOMContentLoaded);
  }

  // Export the event handlers
  window.eventHandlers = {
    handleWebSocketVisibilityRestored,
    handleComputerSleep,
    handleComputerWake,
    handleDOMContentLoaded,
    initEventHandlers,
  };
})();
