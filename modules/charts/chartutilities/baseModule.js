/**
 * Base Module
 * Provides standardized module lifecycle and resource management
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function() {
  'use strict';

  // logger is created per instance in the class, but add a top-level fallback for static/class methods if needed
  const logger = window.logger ? window.logger.createLogger('BaseModule') : console;

  /**
   * Base Module class
   * Provides common functionality for all modules
   */
  class BaseModule {
    /**
     * Create a new module
     * @param {string} name - Module name
     * @param {Object} options - Module options
     */
    constructor(name, options = {}) {
      this.name = name;
      this.options = options;
      this.initialized = false;
      this.destroying = false;
      this.error = null;

      // Create module-specific resource manager
      this.resources = window.resourceManager
        ? new window.resourceManager.constructor(`${name}ResourceManager`)
        : {
            register: () => () => {},
            registerEventListener: () => () => {},
            registerTimeout: () => 0,
            registerInterval: () => 0,
            cleanupAll: () => 0
          };

      // Create module-specific logger
      this.logger = window.logger
        ? window.logger.createLogger(name)
        : {
            debug: console.debug.bind(console, `[${name}]`),
            info: console.log.bind(console, `[${name}]`),
            warn: console.warn.bind(console, `[${name}]`),
            error: console.error.bind(console, `[${name}]`)
          };

      // Event subscriptions
      this.subscriptions = [];

      // Register with module manager if available
      if (window.moduleManager) {
        window.moduleManager.register({
          name: this.name,
          dependencies: this.options.dependencies || [],
          init: this._init.bind(this),
          destroy: this._destroy.bind(this),
          exports: this,
          api: this.getPublicApi ? this.getPublicApi() : null,
          autoInit: this.options.autoInit
        });
      }
    }

    /**
     * Initialize the module
     * @param {Object} dependencies - Initialized dependencies
     * @returns {Promise} - Promise that resolves when initialization is complete
     * @private
     */
    async _init(dependencies) {
      if (this.initialized) {
        return this;
      }

      this.logger.info('Initializing module');

      try {
        // Call the module's initialize method
        if (typeof this.initialize === 'function') {
          await Promise.resolve(this.initialize(dependencies));
        }

        this.initialized = true;
        this.logger.info('Module initialized successfully');

        // Emit initialized event
        this._emitEvent('initialized');

        return this;
      } catch (error) {
        this.error = error;
        this.logger.error('Failed to initialize module', error);

        // Emit error event
        this._emitEvent('error', { error });

        throw error;
      }
    }

    /**
     * Destroy the module
     * @returns {Promise} - Promise that resolves when destruction is complete
     * @private
     */
    async _destroy() {
      if (this.destroying) {
        return;
      }

      this.destroying = true;
      this.logger.info('Destroying module');

      try {
        // Call the module's destroy method
        if (typeof this.destroy === 'function') {
          await Promise.resolve(this.destroy());
        }

        // Unsubscribe from events
        this.unsubscribeAll();

        // Clean up all resources
        this.resources.cleanupAll();

        this.initialized = false;
        this.destroying = false;
        this.logger.info('Module destroyed successfully');

        // Emit destroyed event
        this._emitEvent('destroyed');
      } catch (error) {
        this.logger.error('Failed to destroy module', error);

        // Emit error event
        this._emitEvent('error', { error });

        throw error;
      }
    }

    /**
     * Subscribe to an event
     * @param {string} eventName - Event name
     * @param {Function} callback - Event callback
     * @returns {Function} - Unsubscribe function
     */
    subscribe(eventName, callback) {
      if (!window.eventBus) {
        this.logger.warn('EventBus not available, event subscription ignored');
        return () => {};
      }

      const id = window.eventBus.on(eventName, callback, {
        id: `${this.name}-${Math.random().toString(36).substring(2, 9)}`
      });

      this.subscriptions.push({ eventName, id });

      return () => this.unsubscribe(eventName, id);
    }

    /**
     * Unsubscribe from an event
     * @param {string} eventName - Event name
     * @param {string} id - Subscription ID
     */
    unsubscribe(eventName, id) {
      if (!window.eventBus) return;

      window.eventBus.off(eventName, id);

      // Remove from tracked subscriptions
      const index = this.subscriptions.findIndex(sub => sub.eventName === eventName && sub.id === id);
      if (index !== -1) {
        this.subscriptions.splice(index, 1);
      }
    }

    /**
     * Unsubscribe from all events
     */
    unsubscribeAll() {
      if (!window.eventBus) return;

      this.subscriptions.forEach(({ eventName, id }) => {
        window.eventBus.off(eventName, id);
      });

      this.subscriptions = [];
    }

    /**
     * Emit an event
     * @param {string} eventName - Event name
     * @param {Object} data - Event data
     * @private
     */
    _emitEvent(eventName, data = {}) {
      // Module-specific event
      const moduleEvent = `${this.name}.${eventName}`;

      // Use EventBus if available
      if (window.eventBus) {
        window.eventBus.emit(moduleEvent, Object.assign({ module: this.name }, data));
      }

      // Also dispatch DOM event
      const event = new CustomEvent(moduleEvent, {
        detail: Object.assign({ module: this.name }, data)
      });
      document.dispatchEvent(event);
    }

    /**
     * Safely execute a function with error handling
     * @param {Function} fn - Function to execute
     * @param {Array} args - Function arguments
     * @returns {*} - Function result
     */
    safeExecute(fn, ...args) {
      try {
        return fn(...args);
      } catch (error) {
        this.logger.error('Error executing function', error);

        // Report to error handler if available
        if (window.errorHandler) {
          window.errorHandler.handleError(error, {
            module: this.name,
            context: { args }
          });
        }

        throw error;
      }
    }

    /**
     * Create a DOM element
     * @param {string} tagName - Element tag name
     * @param {Object} options - Element options
     * @returns {HTMLElement} - Created element
     */
    createElement(tagName, options = {}) {
      if (window.domCache) {
        return window.domCache.createElement(tagName, options);
      }

      const element = document.createElement(tagName);

      // Set attributes
      if (options.attributes) {
        Object.entries(options.attributes).forEach(([key, value]) => {
          element.setAttribute(key, value);
        });
      }

      // Set properties
      if (options.properties) {
        Object.assign(element, options.properties);
      }

      // Add children
      if (options.children) {
        if (Array.isArray(options.children)) {
          options.children.forEach(child => {
            if (typeof child === 'string') {
              element.appendChild(document.createTextNode(child));
            } else if (child instanceof Node) {
              element.appendChild(child);
            }
          });
        } else if (typeof options.children === 'string') {
          element.textContent = options.children;
        } else if (options.children instanceof Node) {
          element.appendChild(options.children);
        }
      }

      return element;
    }

    /**
     * Get element by ID
     * @param {string} id - Element ID
     * @returns {HTMLElement|null} - Element or null if not found
     */
    getElementById(id) {
      return window.domCache
        ? window.domCache.getElementById(id)
        : document.getElementById(id);
    }

    /**
     * Get elements by selector
     * @param {string} selector - CSS selector
     * @returns {Array<HTMLElement>} - Array of elements
     */
    querySelectorAll(selector) {
      return window.domCache
        ? window.domCache.querySelectorAll(selector)
        : Array.from(document.querySelectorAll(selector));
    }

    /**
     * Create a throttled function
     * @param {Function} fn - Function to throttle
     * @param {number} limit - Throttle limit in milliseconds
     * @returns {Function} - Throttled function
     */
    throttle(fn, limit) {
      if (window.commonUtils && window.commonUtils.throttle) {
        return window.commonUtils.throttle(fn, limit);
      }

      let lastCall = 0;
      let timeout = null;

      const throttled = function(...args) {
        const now = Date.now();
        const remaining = limit - (now - lastCall);

        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }

        if (remaining <= 0) {
          lastCall = now;
          return fn.apply(this, args);
        } else {
          timeout = setTimeout(() => {
            lastCall = Date.now();
            fn.apply(this, args);
          }, remaining);
        }
      };

      throttled.cancel = function() {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
      };

      return throttled;
    }

    /**
     * Create a debounced function
     * @param {Function} fn - Function to debounce
     * @param {number} wait - Debounce wait time in milliseconds
     * @param {boolean} immediate - Whether to execute immediately
     * @returns {Function} - Debounced function
     */
    debounce(fn, wait, immediate = false) {
      if (window.commonUtils && window.commonUtils.debounce) {
        return window.commonUtils.debounce(fn, wait, immediate);
      }

      let timeout = null;

      const debounced = function(...args) {
        const callNow = immediate && !timeout;

        if (timeout) {
          clearTimeout(timeout);
        }

        timeout = setTimeout(() => {
          timeout = null;
          if (!immediate) {
            fn.apply(this, args);
          }
        }, wait);

        if (callNow) {
          return fn.apply(this, args);
        }
      };

      debounced.cancel = function() {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
      };

      return debounced;
    }
  }

  // Expose the class
  window.BaseModule = BaseModule;
})();
