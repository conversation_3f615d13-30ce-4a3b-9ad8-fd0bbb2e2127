// modules/charts/chartutilities/configManager.js
// Handles configuration, constants, and utility functions

/**
 * Config Manager
 * Handles configuration management and error handling for charts
 * Uses centralized logger and error handler for all logging and error reporting
 */

const logger = window.logger ? window.logger.createLogger('ConfigManager') : console;

(function() {
  /**
   * Configuration object for chart settings with type-safe defaults.
   */
  const CONFIG = window.CONFIG?.indicators
    ? {
        barInterval: Number(window.CONFIG.indicators.barInterval) || 300,
        maxBars: Number(window.CONFIG.indicators.maxBars) || 6000,
        futureBars: Number(window.CONFIG.indicators.futureBars) || 20,
        emaPeriod: Number(window.CONFIG.indicators.emaPeriod) || 180,
        sdPeriod: Number(window.CONFIG.indicators.sdPeriod) || 1440,
        cacheTTL: Number(window.CONFIG.indicators.cacheTTL) || 300000,
      }
    : {
        barInterval: 300,
        maxBars: 6000,
        futureBars: 20,
        emaPeriod: 180,
        sdPeriod: 1440,
        cacheTTL: 300000,
      };

  const PAIRS = [
    "ADA",
    "AAVE",
    "AVAX",
    "DOGE",
    "DOT",
    "FIL",
    "LINK",
    "MATIC",
    "UNI",
    "XRP",
    "XLM",
    "MKR",
    "SUSHI",
    "COMP",
    "CRV",
    "1INCH",
    "LRC",
    "FET",
    "DYDX",
    "INJ",
    "AXS",
    "GRT",
    "SNX",
    "YFI",
    "BAND",
    "KNC",
    "ENS",
    "CVX",
    "RNDR",
    "AUDIO",
    "NEXO",
    "PEPE",
    "PERP",
    "PYTH",
    "RAD",
    "GODS",
    "CTSI",
    "SKL",
    "FLR",
  ];

  /**
   * Utility functions for mathematical and common operations.
   */
  const utils = {
    normalize: window.mathUtils?.normalize,
    computeRollingMinMax: window.mathUtils?.computeRollingMinMax,
    ema: window.mathUtils?.ema,
    sma: window.mathUtils?.sma,
    stdev: window.mathUtils?.stdev,
    clamp: window.commonUtils?.clamp,
    lerp: window.mathUtils?.lerp,
    weightedAverage: window.mathUtils?.weightedAverage,
    arrayMinMax: window.mathUtils?.arrayMinMax,
    throttle: window.commonUtils?.throttle,
    debounce: window.commonUtils?.debounce,
    formatDollarValue: window.commonUtils?.formatDollarValue,
    filterValidBars: (bars) => {
      if (!Array.isArray(bars)) {
        logger.warn("Invalid bars array for filterValidBars:", bars);
        return [];
      }
      return bars.filter(
        (bar) =>
          bar &&
          Number.isFinite(bar.time) &&
          Number.isFinite(bar.open) &&
          Number.isFinite(bar.high) &&
          Number.isFinite(bar.low) &&
          Number.isFinite(bar.close) &&
          Number.isFinite(bar.volume),
      );
    },
  };

  /**
   * Handles errors with consistent logging and UI feedback.
   * @param {string} msg - Error message.
   * @param {Error} err - Error object.
   * @param {HTMLElement} overlay - Overlay element for UI feedback.
   */
  const handleError = (msg, err, overlay) => {
    logger.error(`${msg}: ${err.message}`);
    if (overlay) overlay.textContent = `${msg}: ${err.message}`;
  };

  // Export the configuration and utilities
  window.configManager = {
    CONFIG,
    PAIRS,
    utils,
    handleError
  };
})(); 