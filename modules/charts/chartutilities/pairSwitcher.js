// modules/charts/chartutilities/pairSwitcher.js
// Handles pair switching logic and chart transitions

/**
 * Pair Switcher
 * Handles robust chart pair switching and transition management
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("PairSwitcher")
    : console;
  // Use global configManager, domCache
  if (!window.configManager || !window.domCache) {
    throw new Error("configManager and domCache must be defined globally");
  }

  const CONFIG = window.configManager.CONFIG;
  const utils = window.configManager.utils;
  const domCache = window.domCache;

  let switchInProgress = false;
  let lastSwitchAbortController = null;

  /**
   * Switches the chart to a new trading pair.
   * @param {string} newPair - New trading pair.
   * @returns {Promise<void>}
   */
  async function switchPairInternal(newPair) {
    logger.log(`[pairSwitcher] switchPairInternal called with: ${newPair}`);
    logger.log(
      `[pairSwitcher] Current pair: ${window.currentPair}, switchInProgress: ${switchInProgress}`,
    );

    // Check if we actually need to switch by looking at the chart state
    const currentChartState = window.chartStates?.get(newPair);
    const isCurrentlyActive = currentChartState?.isActive;
    logger.log(`[pairSwitcher] Current chart state for ${newPair}:`, {
      exists: !!currentChartState,
      isActive: isCurrentlyActive,
      hasChart: !!currentChartState?.chart,
      hasPriceSeries: !!currentChartState?.chart?.priceSeries,
    });

    // Debug: Show all existing chart states
    if (window.chartStates) {
      logger.log(
        `[pairSwitcher] All chart states:`,
        Array.from(window.chartStates.entries()).map(([pair, state]) => ({
          pair,
          isActive: state?.isActive,
          hasChart: !!state?.chart,
          hasPriceSeries: !!state?.chart?.priceSeries,
        })),
      );
    }

    // Only skip if the chart is already active and properly initialized
    if (
      isCurrentlyActive &&
      currentChartState?.chart?.priceSeries &&
      switchInProgress === false
    ) {
      logger.log(
        `[pairSwitcher] Skipping switch - chart for ${newPair} is already active and initialized`,
      );
      return;
    }

    if (switchInProgress) {
      logger.log(`[pairSwitcher] Skipping switch - switch already in progress`);
      return;
    }

    switchInProgress = true;

    // CRITICAL FIX: Use centralized transition state management
    if (window.TransitionStateManager) {
      window.TransitionStateManager.startTransition("pair-switch");
    }

    // Enhanced abort controller management
    if (lastSwitchAbortController) {
      lastSwitchAbortController.abort();
      lastSwitchAbortController = null;
    }
    const abortController = new AbortController();
    lastSwitchAbortController = abortController;

    let overlay = domCache.get("overlay");
    if (!overlay) {
      // Fallback: try to get overlay directly from DOM
      const container =
        domCache.get("container") || document.querySelector(".chart-container");
      if (container) {
        overlay = container.querySelector(".loading-overlay");
        if (overlay) {
          // Cache it for future use
          domCache.set("overlay", overlay);
          logger.log("[pairSwitcher] Found overlay via fallback and cached it");
        }
      }
    }

    if (overlay) {
      overlay.style.display = "block";
      overlay.textContent = `Switching to ${newPair}...`;
    } else {
      logger.warn("[pairSwitcher] Overlay not found in domCache or DOM");
    }

    // Enhanced timeout with better error handling
    const timeout = setTimeout(() => {
      switchInProgress = false;
      if (overlay) {
        overlay.textContent = `Switch to ${newPair} timed out`;
        setTimeout(() => (overlay.style.display = "none"), 3000);
      }
      if (abortController) abortController.abort();
    }, 30000); // Increased timeout to 30 seconds for better reliability

    // CRITICAL FIX: Add transition state management
    const transitionState = {
      isTransitioning: true,
      startTime: Date.now(),
      cleanupComplete: false,
      newChartReady: false,
    };

    // Set global transition state
    window.chartTransitionState = transitionState;

    const container = domCache.get("container");
    const oldPair = window.currentPair;
    window.currentActivePair = newPair;
    window.updateActiveButtonState?.(newPair);
    window.clearChartConsole?.();
    window.directConsole?.clear?.();

    // Enhanced connection manager integration with transition coordination
    if (window.connectionManager) {
      try {
        // Dispatch pair change event for ConnectionManager to handle
        window.dispatchEvent(
          new CustomEvent("pairChanged", {
            detail: { from: oldPair, to: newPair },
          }),
        );

        // Wait for data stores to clear before proceeding
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        logger.warn(
          "[pairSwitcher.js] Error in connection manager pair switch:",
          error,
        );
      }
    }

    // Enhanced cleanup with better error handling and state management
    async function cleanupPrevious() {
      const cleanupPromises = [];

      for (const [pair, state] of window.chartStates.entries()) {
        try {
          // Mark state as inactive first
          state.isActive = false;

          // Enhanced WebSocket cleanup
          if (window.bitstampWsManager) {
            window.bitstampWsManager.resubscribeAllForPair(pair);
          }
          if (window.bybitWsManager) {
            window.bybitWsManager.resubscribeAllForPair(pair);
          }

          // Enhanced throttled functions cleanup
          if (state.throttledFunctions) {
            Object.values(state.throttledFunctions).forEach((func) => {
              if (func && typeof func.cancel === "function") {
                func.cancel();
              }
            });
            state.throttledFunctions = {};
          }

          // Enhanced manager cleanup with proper error handling
          const managerCleanups = [
            {
              name: "liquidationManager",
              fn: () => state.liquidationManager?.destroy?.(),
            },
            {
              name: "whaleAlertManager",
              fn: () => state.whaleAlertManager?.destroy?.(),
            },
            {
              name: "markerManager",
              fn: () => state.chart?.markerManager?.clearMarkers?.(),
            },
            {
              name: "deltaOiProfile",
              fn: () => window.deltaOiProfileManager?.cleanupProfile?.(pair),
            },
            {
              name: "stateCleanup",
              fn: () => state.deltaOiProfile?.cleanup?.(),
            },
            {
              name: "stateDestroy",
              fn: () => state.deltaOiProfile?.destroy?.(),
            },
          ];

          for (const { name, fn } of managerCleanups) {
            try {
              if (fn) {
                await Promise.resolve(fn());
              }
            } catch (error) {
              logger.warn(`Cleanup error for ${pair} (${name}):`, error);
            }
          }

          // Enhanced chart series cleanup
          if (
            state.chart?.extras?.markerSeries &&
            !state.chart.extras.markerSeries._internal_isDisposed
          ) {
            try {
              state.chart.priceChart?.removeSeries(
                state.chart.extras.markerSeries,
              );
            } catch (error) {
              logger.warn(`Error removing marker series for ${pair}:`, error);
            }
            state.chart.extras.markerSeries = null;
          }

          // Enhanced indicator cleanup
          const indicatorCleanups = [
            () => {
              if (state.chart?.cvdComponents && window.cvdModule?.cleanupCVD) {
                return window.cvdModule.cleanupCVD(
                  state.chart.cvdComponents,
                  state.chart.cvdComponents?.syncResources,
                );
              }
            },
            () => {
              if (
                state.chart?.perpCvdComponents &&
                window.perpCvdModule?.cleanupCVD
              ) {
                return window.perpCvdModule.cleanupCVD(
                  state.chart.perpCvdComponents,
                  state.chart.perpCvdComponents?.syncResources,
                );
              }
            },
            () => {
              if (
                state.chart?.perpImbalanceComponents &&
                window.perpImbalanceModule?.cleanupImbalance
              ) {
                return window.perpImbalanceModule.cleanupImbalance(
                  state.chart.perpImbalanceComponents,
                  state.chart.perpImbalanceComponents?.syncResources,
                );
              }
            },
          ];

          for (const cleanup of indicatorCleanups) {
            try {
              const result = cleanup();
              if (result && typeof result.then === "function") {
                await result;
              }
            } catch (error) {
              logger.warn(`Indicator cleanup error for ${pair}:`, error);
            }
          }

          // Clear indicator components safely
          if (state.chart) {
            state.chart.cvdComponents = null;
            state.chart.perpCvdComponents = null;
            state.chart.perpImbalanceComponents = null;
          }

          // Enhanced price series cleanup
          if (
            state.chart?.priceSeries &&
            !state.chart.priceSeries._internal_isDisposed
          ) {
            try {
              // Clean up price lines
              Object.values(state.chart.priceLines || {}).forEach((line) => {
                try {
                  if (line && typeof line.remove === "function") {
                    line.remove();
                  } else if (line && typeof line.destroy === "function") {
                    line.destroy();
                  } else if (line && line._internal_id) {
                    // Try to remove from chart if it has an internal ID
                    state.chart.priceChart?.removePriceLine(line);
                  }
                } catch (e) {
                  logger.warn(`Error removing price line:`, e);
                }
              });
              state.chart.priceLines = {};

              // Remove price series
              state.chart.priceChart?.removeSeries(state.chart.priceSeries);
            } catch (error) {
              logger.warn(`Error removing price series for ${pair}:`, error);
            }
          }

          // Enhanced chart cleanup
          if (
            state.chart?.priceChart &&
            !state.chart.priceChart._internal_isDisposed
          ) {
            try {
              state.chart.priceChart.remove();
            } catch (error) {
              logger.warn(`Error removing chart for ${pair}:`, error);
            }
            state.chart.priceChart = null;
          }

          // Clear polling intervals
          if (state.chartIntervals) {
            state.chartIntervals.forEach((interval) => {
              try {
                clearInterval(interval);
              } catch (error) {
                logger.warn(`Error clearing chart interval:`, error);
              }
            });
            state.chartIntervals = [];
          }
        } catch (cleanupError) {
          logger.error(`Error during chart cleanup for ${pair}:`, cleanupError);
        }
      }

      // Clear all chart states
      window.chartStates.clear();

      // Enhanced WebSocket state management
      if (window.bitstampWsManager) {
        const bitstampState = window.bitstampWsManager.getConnectionState();
        logger.log(`Bitstamp WebSocket state after cleanup:`, bitstampState);
      }
      if (window.bybitWsManager) {
        const bybitState = window.bybitWsManager.getConnectionState();
        logger.log(`Bybit WebSocket state after cleanup:`, bybitState);
      }
    }

    async function loadNewChart() {
      try {
        // Get container from domCache or fallback to DOM query
        const container =
          domCache.get("container") ||
          document.querySelector(".chart-container");
        if (!container) {
          throw new Error("Chart container not found");
        }

        // Enhanced button state management
        const activeButtons = Array.from(
          document.querySelectorAll(".pair-button, .popup-timeframe-btn"),
        ).filter(
          (btn) =>
            btn.id === `${newPair}-button` ||
            btn.dataset.pair === newPair ||
            btn.dataset.interval === (window.currentPopupChartInterval || "60"),
        );
        activeButtons.forEach((btn) => btn.classList.add("active"));

        // Enhanced chart container cleanup with performance optimizations
        const chartContainer =
          domCache.get("priceChartElement") ||
          container.querySelector(".price-chart");
        if (chartContainer) {
          const buttonContainer =
            domCache.get("pairSelector") ||
            document.querySelector(".pair-selector");
          const topLevelControls =
            domCache.get("liqControls") ||
            document.querySelector(".liq-controls");

          // Enhanced element cleanup with better error handling and performance
          const chartElements = chartContainer.querySelectorAll(
            ".tv-lightweight-charts, canvas, .pane-separator, .time-scale-box",
          );

          // Batch DOM operations to reduce reflows
          const elementsToRemove = Array.from(chartElements);
          if (elementsToRemove.length > 0) {
            // Use setTimeout to batch DOM operations with delay for better performance
            setTimeout(() => {
              elementsToRemove.forEach((e) => {
                try {
                  e.remove();
                } catch (err) {
                  logger.warn("Error removing chart element:", err);
                }
              });
            }, 50); // 50ms delay for better performance
          }

          // Enhanced net flow div cleanup with performance optimization
          const netFlowDiv = document.getElementById("bybit-net-flow-window");
          if (netFlowDiv && netFlowDiv.parentElement) {
            requestAnimationFrame(() => {
              try {
                netFlowDiv.parentElement.removeChild(netFlowDiv);
              } catch (error) {
                logger.warn("Error removing net flow div:", error);
              }
            });
          }

          // Enhanced container reordering with performance optimization
          if (buttonContainer && buttonContainer.parentElement) {
            requestAnimationFrame(() => {
              try {
                buttonContainer.parentElement.insertBefore(
                  buttonContainer,
                  buttonContainer.parentElement.firstChild,
                );
              } catch (error) {
                logger.warn("Error reordering button container:", error);
              }
            });
          }

          if (
            topLevelControls &&
            container.querySelector(".price-chart-container")
          ) {
            requestAnimationFrame(() => {
              try {
                container
                  .querySelector(".price-chart-container")
                  .appendChild(topLevelControls);
              } catch (error) {
                logger.warn("Error moving top level controls:", error);
              }
            });
          }
        }

        // Enhanced price chart container setup
        const priceChartContainer =
          domCache.get("priceChartContainer") ||
          document.querySelector(".price-chart-container");
        if (priceChartContainer) {
          const existingControls = {
            console:
              domCache.get("liquidationConsole") ||
              document.getElementById("liquidation-console"),
            settings:
              domCache.get("liqControls") ||
              document.querySelector(".liq-controls"),
            buttons:
              domCache.get("pairSelector") ||
              document.querySelector(".pair-selector"),
            chartContainer:
              domCache.get("priceChartElement") ||
              document.querySelector(".price-chart"),
          };

          // Enhanced console setup
          if (!existingControls.console) {
            if (!document.getElementById("liquidation-console")) {
              try {
                const newConsole = document.createElement("div");
                newConsole.id = "liquidation-console";
                newConsole.className = "liquidation-console";
                newConsole.style.cssText =
                  "display: block; visibility: visible; opacity: 1";
                priceChartContainer.appendChild(newConsole);
              } catch (error) {
                logger.warn("Error creating liquidation console:", error);
              }
            }
            window.ensureLiquidationConsoleTitle?.();
          }

          // Enhanced controls setup
          if (existingControls.settings) {
            try {
              priceChartContainer.appendChild(existingControls.settings);
            } catch (error) {
              logger.warn("Error moving settings controls:", error);
            }
          }

          if (
            existingControls.buttons &&
            existingControls.buttons.parentElement
          ) {
            try {
              existingControls.buttons.parentElement.insertBefore(
                existingControls.buttons,
                existingControls.buttons.parentElement.firstChild,
              );
            } catch (error) {
              logger.warn("Error reordering buttons:", error);
            }
          }
        }

        // Enhanced delay for DOM updates with transition coordination
        await new Promise((r) => setTimeout(r, 500)); // Increased delay for better stability

        // Mark cleanup as complete
        if (window.chartTransitionState) {
          window.chartTransitionState.cleanupComplete = true;
        }

        // Enhanced cache cleanup
        if (newPair === "BTC" && window.preCalculateDataCache) {
          window.preCalculateDataCache.delete("BTC_" + CONFIG.barInterval);
        }

        if (newPair === "BTC" && container) {
          const priceChartElement = container.querySelector(".price-chart");
          if (priceChartElement) {
            try {
              priceChartElement.innerHTML = "";
            } catch (error) {
              logger.warn("Error clearing price chart element:", error);
            }
          }
        }

        // Enhanced data loading with better error handling
        const data = await window.preCalculateData(
          newPair,
          overlay,
          (progress) => {
            if (abortController.signal.aborted) return;
            overlay.textContent = `Loading ${newPair} data... ${Math.round(progress * 100)}%`;
          },
          abortController.signal,
        );

        if (abortController.signal.aborted) {
          logger.log(`Pair switch to ${newPair} was aborted`);
          return;
        }

        if (!data) {
          throw new Error(`Failed to load data for ${newPair}`);
        }

        // Enhanced chart initialization
        logger.log(
          `[pairSwitcher] Starting chart initialization for ${newPair}`,
        );
        logger.log(
          `[pairSwitcher] chartInitializer available:`,
          !!window.chartInitializer,
        );
        logger.log(
          `[pairSwitcher] initializeChartAndMeter available:`,
          !!window.chartInitializer?.initializeChartAndMeter,
        );

        const state = window.chartInitializer.initializeChartAndMeter(
          container,
          data,
          newPair,
          (series) => {
            if (abortController.signal.aborted) return;
            // Progressive loading callback
          },
        );

        logger.log(
          `[pairSwitcher] Chart initialization completed for ${newPair}, state:`,
          !!state,
        );

        if (abortController.signal.aborted) {
          logger.log(
            `Pair switch to ${newPair} was aborted during chart initialization`,
          );
          return;
        }

        if (!state) {
          throw new Error(`Failed to initialize chart for ${newPair}`);
        }

        // Enhanced state management
        window.chartStates.set(newPair, state);
        window.currentPair = newPair;

        // Enhanced WebSocket subscription management
        if (
          window.bitstampWsManager &&
          window.bitstampWsManager.isConnected()
        ) {
          const bitstampChannels = [
            `order_book_${newPair.toLowerCase()}usd`,
            `live_trades_${newPair.toLowerCase()}usd`,
          ];
          bitstampChannels.forEach((channel) => {
            window.bitstampWsManager.subscribe(channel, (d) => {
              const s = window.chartStates.get(newPair);
              if (s && s.isActive) {
                window.websocketMessageQueue.add("bitstamp", d);
              }
            });
          });
        }

        if (window.bybitWsManager && window.bybitWsManager.isConnected()) {
          const bybitChannels = [
            `publicTrade.${newPair.toUpperCase()}USDT`,
            `liquidation.${newPair.toUpperCase()}USDT`,
          ];
          bybitChannels.forEach((channel) => {
            window.bybitWsManager.subscribe(channel, (d) => {
              const s = window.chartStates.get(newPair);
              if (s && s.isActive) {
                window.websocketMessageQueue.add("bybit", d);
              }
            });
          });
        }

        // Enhanced polling setup
        window.startChartPolling?.(newPair);

        // Subscribe to the new pair's WebSocket channels
        window.subscribePair?.(newPair);

        // Save the newly loaded pair to localStorage
        localStorage.setItem("lastChartPair", newPair);

        // Enhanced overlay management
        if (overlay) {
          overlay.style.display = "none";
        }
        clearTimeout(timeout);
        switchInProgress = false;
        lastSwitchAbortController = null;

        logger.log(`Successfully switched to ${newPair}`);

        // CRITICAL FIX: Dispatch chartSwitched event for popup chart compatibility
        document.dispatchEvent(
          new CustomEvent("chartSwitched", {
            detail: {
              newPair,
              previousPair: oldPair,
              timestamp: Date.now(),
            },
          }),
        );
      } catch (error) {
        logger.error(`Error loading chart for ${newPair}:`, error);
        if (overlay) {
          overlay.textContent = `Failed to load ${newPair}: ${error.message}`;
          setTimeout(() => (overlay.style.display = "none"), 5000);
        }
        switchInProgress = false;
        lastSwitchAbortController = null;
        clearTimeout(timeout);
      }
    }

    try {
      await cleanupPrevious();
      await loadNewChart();
    } catch (e) {
      if (abortController.signal.aborted && overlay) {
        overlay.textContent = `Switch to ${newPair} cancelled`;
        overlay.style.display = "none";
        overlay.textContent = `Error switching to ${newPair}: ${e.message}`;
        setTimeout(() => (overlay.style.display = "none"), 3000);
      }
    } finally {
      clearTimeout(timeout);
      switchInProgress = false;

      // CRITICAL FIX: Use centralized transition state management
      if (window.TransitionStateManager) {
        window.TransitionStateManager.endTransition();
      }

      // Signal charts module ready after initialization completes
      if (window.signalModuleReady) {
        window.signalModuleReady("charts", newPair);
      }
    }
  }

  // Export the function
  window.pairSwitcher = {
    switchPairInternal,
    switchInProgress: () => switchInProgress,
    lastSwitchAbortController: () => lastSwitchAbortController,
  };

  // CRITICAL FIX: Make switchPairInternal available globally
  window.switchPairInternal = switchPairInternal;
})();
