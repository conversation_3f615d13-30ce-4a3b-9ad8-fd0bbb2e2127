/**
 * Chart Optimizations for Production
 * Advanced performance optimizations, memory management, and error handling
 * for the main charts module
 */

(() => {
  'use strict';

  // Create logger instance
  const logger = window.logger ? window.logger.createLogger("ChartOptimizations") : console;

  // === MEMORY MANAGEMENT ===

  /**
   * Advanced memory manager for chart data and DOM elements
   */
  const MemoryManager = {
    // Object pools for frequently created/destroyed objects
    pools: {
      dataPoints: [],
      chartSeriesData: [],
      domElements: []
    },

    // Memory usage tracking
    memoryStats: {
      allocations: 0,
      deallocations: 0,
      poolHits: 0,
      poolMisses: 0,
      lastCleanup: Date.now()
    },

    // Garbage collection helpers
    gcThresholds: {
      maxAllocations: 1000,
      maxAge: 5 * 60 * 1000, // 5 minutes
      cleanupInterval: 2 * 60 * 1000 // 2 minutes
    },

    /**
     * Get object from pool or create new
     */
    getFromPool(poolName, factory) {
      const pool = this.pools[poolName];
      if (pool && pool.length > 0) {
        this.memoryStats.poolHits++;
        return pool.pop();
      }

      this.memoryStats.poolMisses++;
      this.memoryStats.allocations++;
      return factory();
    },

    /**
     * Return object to pool
     */
    returnToPool(poolName, obj, maxPoolSize = 50) {
      const pool = this.pools[poolName];
      if (pool && pool.length < maxPoolSize) {
        // Reset object state if it has a reset method
        if (typeof obj.reset === 'function') {
          obj.reset();
        }
        pool.push(obj);
        this.memoryStats.deallocations++;
      }
    },

    /**
     * Force garbage collection of old data
     */
    performGarbageCollection() {
      const now = Date.now();

      // Clean up old chart data from cache
      if (window.preCalculateDataCache) {
        const cacheKeys = Array.from(window.preCalculateDataCache.keys());
        let cleaned = 0;

        cacheKeys.forEach(key => {
          const data = window.preCalculateDataCache.get(key);
          if (data && data.timestamp && (now - data.timestamp) > this.gcThresholds.maxAge) {
            window.preCalculateDataCache.delete(key);
            cleaned++;
          }
        });

        if (cleaned > 0) {
          logger.info(`[MemoryManager] Cleaned ${cleaned} cached data entries`);
        }
      }

      // Clean object pools
      Object.keys(this.pools).forEach(poolName => {
        const pool = this.pools[poolName];
        const originalSize = pool.length;

        // Keep only most recent half of pool
        if (pool.length > 20) {
          pool.splice(0, Math.floor(pool.length / 2));
          logger.debug(`[MemoryManager] Trimmed ${poolName} pool from ${originalSize} to ${pool.length}`);
        }
      });

      this.memoryStats.lastCleanup = now;

      // Force browser garbage collection if available
      if (window.gc && typeof window.gc === 'function') {
        try {
          window.gc();
        } catch (e) {
          // Ignore - gc() may not be available in production
        }
      }
    },

    /**
     * Auto cleanup based on thresholds
     */
    checkAndCleanup() {
      const now = Date.now();
      const shouldCleanup =
        this.memoryStats.allocations > this.gcThresholds.maxAllocations ||
        (now - this.memoryStats.lastCleanup) > this.gcThresholds.cleanupInterval;

      if (shouldCleanup) {
        this.performGarbageCollection();
        this.memoryStats.allocations = 0;
      }
    },

    /**
     * Get memory usage statistics
     */
    getStats() {
      const poolSizes = {};
      Object.keys(this.pools).forEach(key => {
        poolSizes[key] = this.pools[key].length;
      });

      return {
        ...this.memoryStats,
        poolSizes,
        cacheSize: window.preCalculateDataCache ? window.preCalculateDataCache.size : 0,
        hitRatio: this.memoryStats.poolHits / (this.memoryStats.poolHits + this.memoryStats.poolMisses) || 0
      };
    }
  };

  // === PERFORMANCE OPTIMIZATION ===

  /**
   * Advanced performance optimizer for chart operations
   */
  const PerformanceOptimizer = {
    // Frame rate management
    frameManager: {
      targetFPS: 60,
      frameTime: 1000 / 60,
      lastFrameTime: 0,
      frameQueue: [],
      isProcessing: false
    },

    // CPU load monitoring
    cpuMonitor: {
      samples: [],
      maxSamples: 30,
      highLoadThreshold: 50, // milliseconds
      consecutiveHighLoad: 0,
      adaptiveMode: false
    },

    /**
     * Smart frame scheduling with adaptive refresh rates
     */
    scheduleFrame(callback, priority = 'normal') {
      const frame = {
        callback,
        priority,
        timestamp: Date.now(),
        id: Math.random().toString(36)
      };

      // Sort by priority: high, normal, low
      const priorityOrder = { high: 0, normal: 1, low: 2 };
      this.frameManager.frameQueue.push(frame);
      this.frameManager.frameQueue.sort((a, b) =>
        priorityOrder[a.priority] - priorityOrder[b.priority]
      );

      this.processFrameQueue();
      return frame.id;
    },

    /**
     * Process frame queue with adaptive scheduling
     */
    processFrameQueue() {
      if (this.frameManager.isProcessing || this.frameManager.frameQueue.length === 0) {
        return;
      }

      this.frameManager.isProcessing = true;
      const startTime = performance.now();

      requestAnimationFrame(() => {
        try {
          const budget = this.getFrameBudget();
          let processedCount = 0;

          while (this.frameManager.frameQueue.length > 0 && processedCount < budget.maxOperations) {
            const frame = this.frameManager.frameQueue.shift();
            const operationStart = performance.now();

            try {
              frame.callback();
            } catch (error) {
              logger.error('[PerformanceOptimizer] Frame callback error:', error);
            }

            const operationTime = performance.now() - operationStart;
            this.recordOperationTime(operationTime);

            // Break if we're approaching frame budget
            if ((performance.now() - startTime) > budget.timeLimit) {
              break;
            }

            processedCount++;
          }

          this.frameManager.lastFrameTime = performance.now() - startTime;
          this.updateCPUMetrics();

        } finally {
          this.frameManager.isProcessing = false;

          // Continue processing if there are remaining frames
          if (this.frameManager.frameQueue.length > 0) {
            setTimeout(() => this.processFrameQueue(), this.getNextFrameDelay());
          }
        }
      });
    },

    /**
     * Calculate frame budget based on current performance
     */
    getFrameBudget() {
      const baseTimeLimit = this.frameManager.frameTime * 0.8; // 80% of frame time
      const adaptiveMultiplier = this.cpuMonitor.adaptiveMode ? 0.5 : 1.0;

      return {
        timeLimit: baseTimeLimit * adaptiveMultiplier,
        maxOperations: this.cpuMonitor.adaptiveMode ? 3 : 10
      };
    },

    /**
     * Get delay for next frame based on current load
     */
    getNextFrameDelay() {
      if (this.cpuMonitor.consecutiveHighLoad > 3) {
        return 32; // ~30fps
      } else if (this.cpuMonitor.consecutiveHighLoad > 1) {
        return 20; // ~50fps
      }
      return 16; // 60fps
    },

    /**
     * Record operation timing for adaptive scheduling
     */
    recordOperationTime(time) {
      this.cpuMonitor.samples.push(time);
      if (this.cpuMonitor.samples.length > this.cpuMonitor.maxSamples) {
        this.cpuMonitor.samples.shift();
      }
    },

    /**
     * Update CPU metrics and adaptive mode
     */
    updateCPUMetrics() {
      if (this.cpuMonitor.samples.length < 5) return;

      const avgTime = this.cpuMonitor.samples.reduce((a, b) => a + b) / this.cpuMonitor.samples.length;
      const isHighLoad = avgTime > this.cpuMonitor.highLoadThreshold;

      if (isHighLoad) {
        this.cpuMonitor.consecutiveHighLoad++;
      } else {
        this.cpuMonitor.consecutiveHighLoad = Math.max(0, this.cpuMonitor.consecutiveHighLoad - 1);
      }

      // Enable adaptive mode if consistently high load
      this.cpuMonitor.adaptiveMode = this.cpuMonitor.consecutiveHighLoad > 5;

      if (this.cpuMonitor.adaptiveMode && Math.random() < 0.1) {
        logger.warn('[PerformanceOptimizer] Adaptive mode active due to high CPU load');
      }
    },

    /**
     * Get performance statistics
     */
    getStats() {
      const avgOperationTime = this.cpuMonitor.samples.length > 0 ?
        this.cpuMonitor.samples.reduce((a, b) => a + b) / this.cpuMonitor.samples.length : 0;

      return {
        frameQueueLength: this.frameManager.frameQueue.length,
        lastFrameTime: this.frameManager.lastFrameTime,
        avgOperationTime,
        adaptiveMode: this.cpuMonitor.adaptiveMode,
        consecutiveHighLoad: this.cpuMonitor.consecutiveHighLoad,
        targetFPS: this.cpuMonitor.adaptiveMode ? 30 : 60
      };
    }
  };

  // === DATA THROTTLING AND BATCHING ===

  /**
   * Smart data processing with throttling and batching
   */
  const DataProcessor = {
    buffers: new Map(),
    processors: new Map(),

    /**
     * Register a data processor with throttling options
     */
    registerProcessor(name, processor, options = {}) {
      const config = {
        maxBatchSize: options.maxBatchSize || 100,
        maxWaitTime: options.maxWaitTime || 16,
        priority: options.priority || 'normal',
        ...options
      };

      this.processors.set(name, { processor, config });
      this.buffers.set(name, []);
    },

    /**
     * Add data to processing queue
     */
    addData(processorName, data) {
      const buffer = this.buffers.get(processorName);
      const config = this.processors.get(processorName)?.config;

      if (!buffer || !config) {
        logger.warn(`[DataProcessor] Unknown processor: ${processorName}`);
        return;
      }

      buffer.push({
        data,
        timestamp: Date.now()
      });

      // Trigger processing if batch is full or adaptive mode suggests immediate processing
      if (buffer.length >= config.maxBatchSize ||
          (PerformanceOptimizer.cpuMonitor.adaptiveMode && buffer.length >= config.maxBatchSize / 2)) {
        this.processBuffer(processorName);
      }
    },

    /**
     * Process buffered data
     */
    processBuffer(processorName) {
      const buffer = this.buffers.get(processorName);
      const processorInfo = this.processors.get(processorName);

      if (!buffer || !processorInfo || buffer.length === 0) {
        return;
      }

      const { processor, config } = processorInfo;
      const batch = buffer.splice(0, config.maxBatchSize);

      PerformanceOptimizer.scheduleFrame(() => {
        try {
          const validData = batch
            .filter(item => (Date.now() - item.timestamp) < 5000) // Remove stale data
            .map(item => item.data);

          if (validData.length > 0) {
            processor(validData);
          }
        } catch (error) {
          logger.error(`[DataProcessor] Error in processor ${processorName}:`, error);
        }
      }, config.priority);
    },

    /**
     * Start automatic buffer processing
     */
    startAutoProcessing() {
      setInterval(() => {
        for (const [name, buffer] of this.buffers) {
          const config = this.processors.get(name)?.config;
          if (buffer.length > 0 && config) {
            // Process if data is waiting longer than maxWaitTime
            const oldestItem = buffer[0];
            if (oldestItem && (Date.now() - oldestItem.timestamp) > config.maxWaitTime) {
              this.processBuffer(name);
            }
          }
        }
      }, 8); // Check every 8ms
    }
  };

  // === WEBSOCKET MESSAGE OPTIMIZATION ===

  /**
   * Optimized WebSocket message queue with prioritization
   */
  const WebSocketQueue = {
    queues: {
      high: [],
      normal: [],
      low: []
    },

    processing: false,
    stats: {
      processed: 0,
      dropped: 0,
      errors: 0
    },

    /**
     * Add message to appropriate queue
     */
    add(source, message, priority = 'normal') {
      // Skip if transitioning
      if (window.TransitionStateManager?.isInTransition()) {
        this.stats.dropped++;
        return;
      }

      const queueItem = {
        source,
        message,
        timestamp: Date.now(),
        id: Math.random().toString(36)
      };

      // Prioritize trade and liquidation messages
      if (this.isHighPriorityMessage(message)) {
        priority = 'high';
      }

      this.queues[priority].push(queueItem);

      // Limit queue sizes to prevent memory issues
      this.trimQueue(priority, 200);

      if (!this.processing) {
        this.processQueues();
      }
    },

    /**
     * Check if message should be high priority
     */
    isHighPriorityMessage(message) {
      if (typeof message === 'string') {
        return message.includes('trade') ||
               message.includes('liquidation') ||
               message.includes('ticker');
      }

      if (typeof message === 'object' && message.event) {
        return ['trade', 'liquidation', 'ticker'].includes(message.event);
      }

      return false;
    },

    /**
     * Trim queue to maximum size
     */
    trimQueue(priority, maxSize) {
      const queue = this.queues[priority];
      if (queue.length > maxSize) {
        const removed = queue.splice(0, queue.length - maxSize);
        this.stats.dropped += removed.length;
      }
    },

    /**
     * Process all queues in priority order
     */
    processQueues() {
      if (this.processing) return;
      this.processing = true;

      const processNext = () => {
        // Process high priority first, then normal, then low
        const priorities = ['high', 'normal', 'low'];
        let processed = false;

        for (const priority of priorities) {
          const queue = this.queues[priority];
          if (queue.length > 0) {
            const batch = queue.splice(0, priority === 'high' ? 10 : 5);

            batch.forEach(item => {
              try {
                this.processMessage(item);
                this.stats.processed++;
              } catch (error) {
                logger.error('[WebSocketQueue] Message processing error:', error);
                this.stats.errors++;
              }
            });

            processed = true;
            break; // Process one priority level per frame
          }
        }

        if (processed) {
          PerformanceOptimizer.scheduleFrame(processNext, 'high');
        } else {
          this.processing = false;
        }
      };

      processNext();
    },

    /**
     * Process individual message
     */
    processMessage(item) {
      const { source, message } = item;

      // Route to appropriate handler
      if (source === 'bitstamp' && window.handleBitstampMessage) {
        window.handleBitstampMessage(message);
      } else if (source === 'bybit' && window.handleBybitMessage) {
        window.handleBybitMessage(message);
      } else if (window.handleOrderbookMessage) {
        window.handleOrderbookMessage(message, source);
      }
    },

    /**
     * Get queue statistics
     */
    getStats() {
      const queueSizes = {};
      Object.keys(this.queues).forEach(priority => {
        queueSizes[priority] = this.queues[priority].length;
      });

      return {
        ...this.stats,
        queueSizes,
        processing: this.processing
      };
    }
  };

  // === ERROR HANDLING AND RECOVERY ===

  /**
   * Advanced error handling with automatic recovery
   */
  const ErrorRecovery = {
    errors: [],
    maxErrors: 100,
    recoveryStrategies: new Map(),

    /**
     * Register recovery strategy for error type
     */
    registerStrategy(errorType, strategy) {
      this.recoveryStrategies.set(errorType, strategy);
    },

    /**
     * Handle error with automatic recovery
     */
    handleError(error, context = {}) {
      const errorInfo = {
        error,
        context,
        timestamp: Date.now(),
        stack: error.stack
      };

      this.errors.push(errorInfo);
      if (this.errors.length > this.maxErrors) {
        this.errors.shift();
      }

      // Try to recover
      this.attemptRecovery(error, context);

      // Log error
      logger.error('[ErrorRecovery] Error occurred:', error, context);
    },

    /**
     * Attempt automatic recovery
     */
    attemptRecovery(error, context) {
      const errorType = this.classifyError(error);
      const strategy = this.recoveryStrategies.get(errorType);

      if (strategy) {
        try {
          strategy(error, context);
          logger.info(`[ErrorRecovery] Recovery attempted for ${errorType}`);
        } catch (recoveryError) {
          logger.error('[ErrorRecovery] Recovery failed:', recoveryError);
        }
      }
    },

    /**
     * Classify error type
     */
    classifyError(error) {
      if (error.message?.includes('WebSocket')) return 'websocket';
      if (error.message?.includes('fetch') || error.message?.includes('network')) return 'network';
      if (error.message?.includes('chart') || error.message?.includes('series')) return 'chart';
      if (error.message?.includes('memory') || error.message?.includes('allocation')) return 'memory';
      return 'general';
    },

    /**
     * Get error statistics
     */
    getStats() {
      const errorTypes = {};
      this.errors.forEach(({ error }) => {
        const type = this.classifyError(error);
        errorTypes[type] = (errorTypes[type] || 0) + 1;
      });

      return {
        totalErrors: this.errors.length,
        errorTypes,
        recentErrors: this.errors.slice(-5).map(e => ({
          message: e.error.message,
          timestamp: e.timestamp,
          context: e.context
        }))
      };
    }
  };

  // === INITIALIZATION AND SETUP ===

  /**
   * Initialize all optimization systems
   */
  function initializeOptimizations() {
    logger.info('[ChartOptimizations] Initializing production optimizations...');

    // Set up memory management
    setInterval(() => {
      MemoryManager.checkAndCleanup();
    }, 60000); // Check every minute

    // Start data processing
    DataProcessor.startAutoProcessing();

    // Register common data processors
    DataProcessor.registerProcessor('priceData', (batch) => {
      // Process price data updates
      if (window.updateChartPrice) {
        batch.forEach(data => window.updateChartPrice(data));
      }
    }, { maxBatchSize: 50, priority: 'high' });

    DataProcessor.registerProcessor('orderbook', (batch) => {
      // Process orderbook updates
      if (window.updateOrderBook) {
        window.updateOrderBook(batch[batch.length - 1]); // Use latest
      }
    }, { maxBatchSize: 10, priority: 'high' });

    // Set up error recovery strategies
    ErrorRecovery.registerStrategy('websocket', (error, context) => {
      // Attempt to reconnect WebSocket
      if (context.wsManager && context.wsManager.reconnect) {
        context.wsManager.reconnect();
      }
    });

    ErrorRecovery.registerStrategy('chart', (error, context) => {
      // Attempt to recreate chart series
      if (context.chart && context.chart.remove && context.createChart) {
        try {
          context.chart.remove();
          context.createChart();
        } catch (e) {
          logger.error('[ErrorRecovery] Chart recovery failed:', e);
        }
      }
    });

    ErrorRecovery.registerStrategy('memory', (error, context) => {
      // Force garbage collection
      MemoryManager.performGarbageCollection();
    });

    // Export global interfaces
    window.chartOptimizations = {
      MemoryManager,
      PerformanceOptimizer,
      DataProcessor,
      WebSocketQueue,
      ErrorRecovery,
      getStats: () => ({
        memory: MemoryManager.getStats(),
        performance: PerformanceOptimizer.getStats(),
        websocketQueue: WebSocketQueue.getStats(),
        errors: ErrorRecovery.getStats()
      })
    };

    // Set up global message queue
    window.websocketMessageQueue = WebSocketQueue;

    logger.info('[ChartOptimizations] Production optimizations initialized');
  }

  // === PERFORMANCE MONITORING ===

  /**
   * Performance monitor for production debugging
   */
  const PerformanceMonitor = {
    start() {
      // Monitor frame rates
      let frameCount = 0;
      let lastTime = Date.now();

      const measureFPS = () => {
        frameCount++;
        const now = Date.now();

        if (now - lastTime >= 1000) {
          const fps = frameCount * 1000 / (now - lastTime);

          if (fps < 30) {
            logger.warn(`[PerformanceMonitor] Low FPS detected: ${fps.toFixed(1)}`);
          }

          frameCount = 0;
          lastTime = now;
        }

        requestAnimationFrame(measureFPS);
      };

      measureFPS();

      // Monitor memory usage
      if (performance.memory) {
        setInterval(() => {
          const memory = performance.memory;
          const used = memory.usedJSHeapSize / 1024 / 1024;
          const total = memory.totalJSHeapSize / 1024 / 1024;

          if (used / total > 0.9) {
            logger.warn(`[PerformanceMonitor] High memory usage: ${used.toFixed(1)}MB / ${total.toFixed(1)}MB`);
            MemoryManager.performGarbageCollection();
          }
        }, 30000); // Check every 30 seconds
      }
    }
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeOptimizations);
  } else {
    initializeOptimizations();
  }

  // Start performance monitoring
  PerformanceMonitor.start();

})();
