(function () {
  // === Utility Functions ===
  function logError(message, error) {
    console.error(`${message}: ${error.message}`);
    // Optional: Add integration with an error reporting service like Sentry
  }

  // Use global utility for formatting large numbers
  const formatLargeNumber = window.commonUtils.formatLargeNumber;

  // Use centralized throttle function
  const throttle = window.commonUtils.throttle;

  // tryFetchWithProxies, fetchBitstampData, fetchBybitData, fetchMarketData are removed, will use window.dataFetcher

  // Drawing Primitives and MarkerManager were removed as they are not used by window.popupChart directly
  // and UI/drag logic is in modules/charts/popupChart/PopupChartUI.js

  // === Message Queue for Real-time Updates ===
  let lastPopupMessageProcessTime = 0;
  const minPopupMessageInterval = 1; // 1ms for near real-time updates

  const popupMessageQueue = {
    add: (data) => {
      const now = Date.now();
      if (now - lastPopupMessageProcessTime >= minPopupMessageInterval) {
        lastPopupMessageProcessTime = now;
        if (window.popupChart && window.popupChart.wsHandler) {
          window.popupChart.wsHandler(data);
        }
      } else {
        setTimeout(
          () => {
            lastPopupMessageProcessTime = Date.now();
            if (window.popupChart && window.popupChart.wsHandler) {
              window.popupChart.wsHandler(data);
            }
          },
          minPopupMessageInterval - (now - lastPopupMessageProcessTime),
        );
      }
    },
  };

  // === Popup Chart Object ===
  (() => {
    // --- BitstampBTCWsManager (local to popup-chart.js) ---
    class BitstampBTCWsManager {
      constructor() {
        this.ws = null;
        this.connected = false;
        this.subscriptions = {};
        this.handlers = {};
        this.connecting = false;
        this.url = "wss://ws.bitstamp.net";
        this._reconnectTimeout = null;
        this._reconnectDelay = 2000;
      }
      isConnected() {
        return this.connected;
      }
      connect() {
        if (this.connected || this.connecting) return;
        this.connecting = true;
        this.ws = new WebSocket(this.url);
        this.ws.onopen = () => {
          console.log("[BitstampBTCWsManager] WebSocket connection opened.");
          this.connected = true;
          this.connecting = false;
          this._reconnectDelay = 2000;
          Object.keys(this.subscriptions).forEach((channel) => {
            this.ws.send(
              JSON.stringify({ event: "bts:subscribe", data: { channel } }),
            );
          });
        };
        this.ws.onclose = (event) => {
          // Added event parameter
          console.warn("[BitstampBTCWsManager] WebSocket connection closed.", {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
          });
          this.connected = false;
          this.connecting = false;
          this._scheduleReconnect();
        };
        this.ws.onerror = (event) => {
          // Added event parameter
          // Only log errors if we're not in the process of closing
          if (this.ws && this.ws.readyState !== WebSocket.CLOSED) {
            console.error("[BitstampBTCWsManager] WebSocket error:", event);
          }
          this.connected = false;
          this.connecting = false;
          this._scheduleReconnect();
        };
        this.ws.onmessage = (msg) => {
          let data;
          try {
            data = JSON.parse(msg.data);
          } catch (error) {
            // Added error parameter
            console.error(
              "[BitstampBTCWsManager] Error parsing WebSocket message:",
              error,
              "Raw data:",
              msg.data,
            );
            return; // Skip further processing of unparseable message
          }
          const channel = data.channel;
          if (channel && this.handlers[channel]) {
            this.handlers[channel].forEach((fn) => fn(data));
          }
        };
      }
      _scheduleReconnect() {
        if (this._reconnectTimeout) clearTimeout(this._reconnectTimeout);
        this._reconnectTimeout = setTimeout(() => {
          this.connect();
        }, this._reconnectDelay);
        this._reconnectDelay = Math.min(this._reconnectDelay * 2, 60000);
      }
      subscribe(channel, handler) {
        if (!this.subscriptions[channel]) {
          this.subscriptions[channel] = true;
          this.handlers[channel] = [];
          if (this.connected && this.ws) {
            this.ws.send(
              JSON.stringify({ event: "bts:subscribe", data: { channel } }),
            );
          }
        }
        if (this.handlers[channel] && this.handlers[channel].includes(handler))
          return;
        if (handler && typeof handler === "function") {
          this.handlers[channel].push(handler);
        }
      }
      unsubscribe(channel) {
        if (this.subscriptions[channel]) {
          delete this.subscriptions[channel];
          delete this.handlers[channel];
          if (this.connected && this.ws) {
            this.ws.send(
              JSON.stringify({ event: "bts:unsubscribe", data: { channel } }),
            );
          }
        }
      }

      close() {
        if (this.ws) {
          try {
            // Only close if not already closed
            if (this.ws.readyState !== WebSocket.CLOSED) {
              this.ws.close();
            }
          } catch (error) {
            console.warn(
              "[BitstampBTCWsManager] Error closing WebSocket:",
              error,
            );
          }
          this.ws = null;
        }
        this.connected = false;
        this.connecting = false;
        this.subscriptions = {};
        this.handlers = {};
      }
    }

    // Always create a dedicated WebSocket manager for the popup chart
    const popupWsManager = new BitstampBTCWsManager();

    window.popupChart = {
      // State variables
      chart: null,
      series: null,
      container: null,
      // chartData is now used ONLY for caching historical API data, not for bar storage
      chartData: {},
      barArray: [],
      isInitializing: false,
      currentSymbol: null,
      currentInterval: window.CONFIG?.popupChart?.defaultInterval || "60",
      currentBar: null,
      // _pendingBarUpdate: null, // Removed for throttling reversion
      // _updateTimeoutId: null,  // Removed for throttling reversion
      // THROTTLE_DELAY_MS: 200, // Removed for throttling reversion

      // Event handlers
      handleResize: null,
      wsHandler: null,
      wsManager: popupWsManager, // Dedicated WebSocket manager for popup chart

      currentBarUpdateToken: 0,

      // === Methods ===

      /**
       * Initializes the chart with the specified symbol and interval.
       * @param {string} symbol - The market symbol (e.g., 'BTCUSD').
       * @param {string} interval - The timeframe interval (e.g., '60' for 1 hour).
       */
      initialize: function (symbol, interval) {
        // Always set currentSymbol/currentInterval at the very start
        this.currentSymbol = symbol;
        this.currentInterval = interval || "60";

        // Prevent multiple rapid initializations
        if (this.isInitializing) {
          console.log(
            `[PopupChart] Skipping initialization - already initializing ${symbol}`,
          );
          return;
        }

        // Check if we're already initialized for this symbol and interval
        if (
          this.chart &&
          this.series &&
          this.currentSymbol === symbol &&
          this.currentInterval === interval
        ) {
          console.log(
            `[PopupChart] Skipping initialization - already initialized for ${symbol} ${interval}`,
          );
          return;
        }

        // If symbol is the same but interval changed, we need to reinitialize
        if (
          this.chart &&
          this.series &&
          this.currentSymbol === symbol &&
          this.currentInterval !== interval
        ) {
          console.log(
            `[PopupChart] Reinitializing for ${symbol} - interval changed from ${this.currentInterval} to ${interval}`,
          );
        }

        // Initializing popup chart
        this.isInitializing = true;

        try {
          this.cleanup();
          this.container = document.getElementById("popup-chart-container");
          if (!this.container) {
            throw new Error("Popup chart container not found");
          }
          this.container.style.display = "block";

          // Set initial popup position if not set (for stable drag)
          const wrapper = document.getElementById("popup-chart-wrapper");
          if (
            wrapper &&
            (wrapper.style.left === "" || wrapper.style.top === "")
          ) {
            wrapper.style.left = "10px";
            wrapper.style.top = "10px";
          }

          const width =
            this.container.clientWidth ||
            window.CONFIG?.popupChart?.defaultWidth ||
            400;
          const height =
            this.container.clientHeight ||
            window.CONFIG?.popupChart?.defaultHeight ||
            300;

          if (typeof LightweightCharts === "undefined") {
            throw new Error("LightweightCharts library not loaded");
          }

          const chartColors = window.CONFIG?.chart?.defaultColors || {
            background: "#0f141a",
            text: "#D3D3D3",
            grid: "#2A2A2A",
          };

          this.chart = LightweightCharts.createChart(this.container, {
            width: width,
            height: height,
            layout: {
              background: { color: chartColors.background, type: "solid" },
              textColor: chartColors.text,
              fontSize: 12,
              attributionLogo: false,
            },
            grid: {
              vertLines: { visible: false }, // Could be configured: chartColors.grid
              horzLines: { visible: false }, // Could be configured: chartColors.grid
            },
            timeScale: {
              timeVisible: true,
              secondsVisible: false,
              borderColor: chartColors.grid,
            },
            rightPriceScale: {
              borderColor: chartColors.grid,
              scaleMargins: { top: 0.1, bottom: 0.1 },
            },
          });

          // Set up ResizeObserver for the container
          if (typeof ResizeObserver !== "undefined") {
            const resizeObserver = new ResizeObserver((entries) => {
              for (const entry of entries) {
                const { width, height } = entry.contentRect;
                if (width > 50 && height > 50) {
                  // Resizing chart
                  this.chart.resize(width, height);
                }
              }
            });
            resizeObserver.observe(this.container);
            this.resizeObserver = resizeObserver;
          }

          // Creating candlestick series
          const candleStickColors = window.CONFIG?.chart?.candlestick || {
            upColor: "#AAAAAA",
            downColor: "#AAAAAA",
            borderColor: "#AAAAAA",
            wickUpColor: "#AAAAAA",
            wickDownColor: "#AAAAAA",
          };
          this.series = this.chart.addSeries(
            LightweightCharts.CandlestickSeries,
            {
              ...candleStickColors,
              lastValueVisible: true,
              priceLineVisible: true,
              priceLineSource: LightweightCharts.PriceLineSource.LastBar,
              priceFormat: { type: "price", precision: 2, minMove: 0.01 },
            },
          );

          // Hide crosshair when mouse leaves chart area
          this.chart.subscribeCrosshairMove((param) => {
            if (!param.point) {
              // Hide crosshair: set mode to hidden
              this.chart.applyOptions({
                crosshair: { mode: LightweightCharts.CrosshairMode.Hidden },
              });
            } else {
              // Restore crosshair: set mode to normal
              this.chart.applyOptions({
                crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
              });
            }
          });

          this.loadChartData(symbol, this.currentInterval);

          // Subscribe to websocket updates
          this.subscribeToWebSocket();

          // Add visibility change handling for chart stability - ENHANCED STABILITY
          this.handleVisibilityChange = () => {
            if (document.visibilityState === "visible" && this.currentSymbol) {
              // Enhanced visibility restoration with better error handling and reduced sensitivity
              setTimeout(() => {
                try {
                  // Enhanced connection verification with reduced sensitivity
                  if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    // WebSocket connection verified
                  } else {
                    console.warn(
                      "[PopupChart] WebSocket connection lost during visibility change, reconnecting",
                    );
                    // Add delay to prevent rapid reconnections
                    setTimeout(() => {
                      this.reconnect();
                    }, 3000); // 3 second delay
                  }

                  // Enhanced data refresh with better error handling and reduced frequency
                  // Only refresh if we have no historical data or very little data
                  setTimeout(() => {
                    if (this.barArray.length < 10) {
                      // Only reload if we have insufficient historical data
                      this.loadChartData(
                        this.currentSymbol,
                        this.currentInterval,
                      ).catch((error) => {
                        console.error(
                          "[PopupChart] Error refreshing chart data after visibility change:",
                          error,
                        );
                      });
                    } else {
                      // Preserving existing historical data after visibility change
                    }

                    // Signal popupchart module ready after visibility restoration
                    if (window.signalModuleReady) {
                      window.signalModuleReady(
                        "popupchart",
                        this.currentSymbol,
                      );
                    }
                  }, 2000);
                } catch (error) {
                  console.error(
                    "[PopupChart] Error in visibility change handler:",
                    error,
                  );
                }
              }, 3000); // Increased delay to 3 seconds for better stability
            } else if (document.visibilityState === "hidden") {
              // Enhanced hidden state management
              this._hiddenState = true;

              // Reduce update frequency when hidden
              if (this._updateInterval) {
                clearInterval(this._updateInterval);
                this._updateInterval = setInterval(() => {
                  if (this._hiddenState && this.currentSymbol) {
                    this.refreshStaleData();
                  }
                }, 30000); // 30 seconds when hidden (increased from default)
              }
            }
          };
          document.addEventListener(
            "visibilitychange",
            this.handleVisibilityChange,
          );

          // CRITICAL FIX: Sleep/wake event handlers
          this._handleComputerSleep = () => {
            this._sleepDetected = true;
            this._lastSleepTime = Date.now();
          };

          this._handleComputerWake = (event) => {
            const sleepDuration = event.detail?.sleepDuration || 0;

            this._sleepDetected = false;

            // Force reconnection after sleep
            setTimeout(() => {
              console.log("[PopupChart] Forcing reconnection after wake");
              this.reconnect();
            }, 2000);
          };

          // Add sleep/wake event listeners
          window.addEventListener("computer-sleep", this._handleComputerSleep);
          window.addEventListener("computer-wake", this._handleComputerWake);

          // Enhanced WebSocket visibility restoration event handling - IMPROVED STABILITY
          this.handleWebSocketVisibilityRestore = async (event) => {
            try {
              console.log(
                `[PopupChart] WebSocket visibility restored for bitstamp:`,
                event.detail,
              );

              // Enhanced connection state verification
              const connectionState = event.detail.connectionState;
              if (
                !connectionState ||
                connectionState.readyState !== WebSocket.OPEN
              ) {
                console.warn(
                  "[PopupChart] WebSocket not in OPEN state after visibility restore",
                );
                return;
              }

              // Enhanced data sync with better error handling and historical data preservation
              if (this.currentSymbol && this.currentBar) {
                try {
                  // Fetch latest bar to sync chart with current data
                  const symbol = this.currentSymbol
                    .replace("USD", "")
                    .toLowerCase();
                  const response = await fetch(
                    `https://www.bitstamp.net/api/v2/ohlc/${symbol}usd/?step=${this.mapIntervalToApi(this.currentInterval)}&limit=1`,
                  );

                  if (!response.ok) {
                    throw new Error(
                      `HTTP ${response.status}: ${response.statusText}`,
                    );
                  }

                  const data = await response.json();

                  if (
                    data &&
                    data.data &&
                    data.data.ohlc &&
                    data.data.ohlc.length > 0
                  ) {
                    const bar = data.data.ohlc[0];
                    const latestBar = {
                      time: parseInt(bar.timestamp),
                      open: parseFloat(bar.open),
                      high: parseFloat(bar.high),
                      low: parseFloat(bar.low),
                      close: parseFloat(bar.close),
                      volume: parseFloat(bar.volume),
                    };

                    // Enhanced bar update logic with better validation and historical preservation
                    const lastBar = this.barArray[this.barArray.length - 1];
                    if (
                      lastBar &&
                      Math.abs(lastBar.time - latestBar.time) <=
                        this.mapIntervalToApi(this.currentInterval)
                    ) {
                      // Same bar period - update the existing bar without clearing historical data
                      Object.assign(lastBar, latestBar);
                      this.currentBar = { ...lastBar };
                      this.currentBarVolume = lastBar.volume;

                      // Update the chart with all bars to maintain historical data
                      const filteredBars = window.utils.filterValidBars(
                        this.barArray,
                      );
                      if (filteredBars.length > 0) {
                        this.series.setData(filteredBars);
                        console.log(
                          `[PopupChart] Updated existing bar for ${this.currentSymbol} after visibility restore (${filteredBars.length} total bars)`,
                        );
                      }
                    } else {
                      // New bar period - add the new bar while preserving historical data
                      this.barArray.push(latestBar);
                      this.currentBar = { ...latestBar };
                      this.currentBarVolume = latestBar.volume;

                      // Maintain array size
                      if (this.barArray.length > 500) {
                        this.barArray.shift();
                      }

                      // Update the chart with all bars
                      const filteredBars = window.utils.filterValidBars(
                        this.barArray,
                      );
                      if (filteredBars.length > 0) {
                        this.series.setData(filteredBars);
                        console.log(
                          `[PopupChart] Added new bar for ${this.currentSymbol} after visibility restore (${filteredBars.length} total bars)`,
                        );
                      }
                    }

                    console.log(
                      `[PopupChart] Successfully synced chart data after visibility restore for ${this.currentSymbol}`,
                    );
                  } else {
                    console.warn(
                      "[PopupChart] No valid data received during visibility restore sync",
                    );
                  }
                } catch (error) {
                  console.error(
                    "[PopupChart] Failed to sync chart data after visibility restore:",
                    error,
                  );

                  // Enhanced fallback - preserve existing data and only reload if necessary
                  if (this.barArray.length === 0) {
                    try {
                      console.log(
                        "[PopupChart] No historical data available, attempting full chart reload",
                      );
                      await this.loadChartData(
                        this.currentSymbol,
                        this.currentInterval,
                      );
                    } catch (reloadError) {
                      console.error(
                        "[PopupChart] Full chart reload also failed:",
                        reloadError,
                      );
                    }
                  } else {
                    console.log(
                      "[PopupChart] Preserving existing historical data despite sync failure",
                    );
                  }
                }
              }
            } catch (error) {
              console.error(
                "[PopupChart] Error in WebSocket visibility restore handler:",
                error,
              );
            }
          };
          window.addEventListener(
            "websocket-visibility-restored-bitstamp",
            this.handleWebSocketVisibilityRestore,
          );

          window.addEventListener("resize", this.handleResize);
          if (typeof ResizeObserver !== "undefined") {
            const resizeObserver = new ResizeObserver(() =>
              this.handleResize(),
            );
            resizeObserver.observe(this.container);
            this.resizeObserver = resizeObserver;
          }
          // Popup chart initialized successfully
        } catch (error) {
          logError("Error initializing popup chart", error);
        } finally {
          this.isInitializing = false;
        }
      },

      /**
       * Handles chart resizing with throttling.
       */
      handleResize: throttle(function () {
        if (!this.chart || !this.container) return;
        try {
          const width = this.container.clientWidth || 400;
          const height = this.container.clientHeight || 300;
          if (width > 50 && height > 50) {
            // Resizing chart
            this.chart.resize(width, height);
          }
        } catch (error) {
          logError("Error resizing chart", error);
        }
      }, 100),

      /**
       * Updates the chart timeframe and reloads data.
       * @param {string} interval - The new interval to switch to.
       */
      updateTimeframe: function (interval) {
        if (!this.currentSymbol) {
          logError(
            "No current symbol found for popup chart",
            new Error("Missing symbol"),
          );
          return;
        }
        if (this.currentInterval === interval) {
          // Already using the same interval
          return;
        }

        // Updating popup chart timeframe

        // Unsubscribe from old interval's websocket
        this.unsubscribeFromWebSocket();

        this.currentInterval = interval;
        this.currentBar = null;
        this.currentBarVolume = 0; // Reset volume accumulator
        this.loadChartData(this.currentSymbol, interval);

        // Subscribe to new interval's websocket
        this.subscribeToWebSocket();
      },

      /**
       * Cleans up the chart and removes event listeners.
       */
      cleanup: function () {
        // Cleaning up popup chart
        try {
          // Unsubscribe from websocket updates
          this.unsubscribeFromWebSocket();

          window.removeEventListener("resize", this.handleResize);
          if (this.handleVisibilityChange) {
            document.removeEventListener(
              "visibilitychange",
              this.handleVisibilityChange,
            );
            this.handleVisibilityChange = null;
          }

          // CRITICAL FIX: Remove sleep/wake event listeners
          if (this._handleComputerSleep) {
            window.removeEventListener(
              "computer-sleep",
              this._handleComputerSleep,
            );
            this._handleComputerSleep = null;
          }
          if (this._handleComputerWake) {
            window.removeEventListener(
              "computer-wake",
              this._handleComputerWake,
            );
            this._handleComputerWake = null;
          }

          // Remove WebSocket visibility restoration listener
          window.removeEventListener(
            "websocket-visibility-restored-bitstamp",
            this.handleWebSocketVisibilityRestore,
          );
          if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
          }

          // Clear chart data and state
          this.barArray = [];
          this.currentBar = null; // Explicitly nullify
          this.currentBarVolume = 0; // Explicitly reset
          this.wsHandler = null; // Already there, good
          this.chartData = {}; // Clear internal cache

          // Clear throttled update state (Removed for throttling reversion)
          // if (this._updateTimeoutId) {
          //   clearTimeout(this._updateTimeoutId);
          //   this._updateTimeoutId = null;
          // }
          // this._pendingBarUpdate = null;

          // Properly clean up series
          if (this.series) {
            // No need to call series.setData([]) if chart is being removed
            this.series = null;
          }

          if (this.chart) {
            try {
              this.chart.remove();
            } catch (e) {
              logError("Error removing chart", e);
            }
            this.chart = null;
          }

          // Clear container
          if (this.container) {
            this.container.innerHTML = "";
            this.container.style.display = "none";
          }
          this.container = null;

          // Cleanup completed successfully
        } catch (error) {
          logError("Error during cleanup", error);
        }
      },

      /**
       * Loads and displays chart data for the specified symbol and interval.
       * Uses cached data if available, otherwise fetches from APIs with fallback.
       * @param {string} symbol - The market symbol (e.g., 'BTCUSD').
       * @param {string} interval - The timeframe interval (e.g., '60' for 1 hour).
       * @param {boolean} [isLineSeries=false] - Whether to display as a line series instead of candlesticks.
       */
      loadChartData: async function (symbol, interval, isLineSeries = false) {
        if (!this.series || !this.chart) {
          logError(
            "Cannot load data - chart or series not initialized",
            new Error("Missing chart or series"),
          );
          return;
        }
        // Remove any existing loading indicator
        const existingIndicator = document.getElementById(
          "chart-loading-indicator",
        );
        if (existingIndicator) existingIndicator.remove();
        this.showLoadingIndicator(this.container);

        const formattedSymbol = symbol.replace("USD", "").toLowerCase() + "usd";
        const apiInterval = this.mapIntervalToApi(interval);
        const cacheKey = `${formattedSymbol}_${apiInterval}`;
        const cachedData = this.getCachedData(cacheKey);

        const controller = new AbortController();
        const signal = controller.signal;
        const timeoutMs = window.CONFIG?.ui?.loadingTimeout || 7000;
        const raceTimeoutMs = timeoutMs + 1000;
        const abortTimeout = setTimeout(() => controller.abort(), timeoutMs);

        const exchange = formattedSymbol.includes("usd") ? "bitstamp" : "bybit";
        const fetchSymbol =
          exchange === "bitstamp"
            ? formattedSymbol
            : symbol.replace("USD", "").toUpperCase();
        let fetchInterval = apiInterval;
        if (exchange === "bybit") {
          if (typeof apiInterval === "number") {
            fetchInterval = this.mapApiIntervalToString(apiInterval);
          }
        }

        // Helper to fetch data with retries
        const fetchWithRetries = async (retries = 3, delay = 500) => {
          for (let attempt = 1; attempt <= retries; attempt++) {
            try {
              const data = await window.dataFetcher.getHistoricalData(
                exchange,
                fetchSymbol,
                fetchInterval,
                signal,
              );
              if (data && data.length > 0) return data;
              logError(
                `[PopupChart] Attempt ${attempt}: No data received from API`,
                new Error("No data"),
              );
            } catch (err) {
              logError(
                `[PopupChart] Attempt ${attempt}: Error fetching data`,
                err,
              );
            }
            if (attempt < retries)
              await new Promise((r) => setTimeout(r, delay));
          }
          return [];
        };

        let data = [];
        if (cachedData && cachedData.length > 0) {
          document.getElementById("chart-loading-indicator")?.remove();
          this.processChartData(cachedData, isLineSeries);
          // Fetch fresh data in background
          setTimeout(async () => {
            const freshData = await fetchWithRetries(2, 300);
            if (
              freshData &&
              freshData.length > 0 &&
              this.currentSymbol === symbol &&
              this.currentInterval === interval
            ) {
              this.cacheData(cacheKey, freshData);
              this.processChartData(freshData, isLineSeries);
            }
          }, 100);
          return;
        }

        try {
          data = await fetchWithRetries(3, 500);
          clearTimeout(abortTimeout);
        } catch (error) {
          clearTimeout(abortTimeout);
          logError("Error fetching market data via dataFetcher", error);
        }
        document.getElementById("chart-loading-indicator")?.remove();

        // Fallback: If still no data, try to get the latest price from the main chart
        if (!data || data.length === 0) {
          let fallbackPrice = 0;
          if (window.currentPair) {
            const mainChartState = window.chartStates?.get(window.currentPair);
            if (mainChartState?.data?.priceData?.length > 0) {
              const mainLastBar =
                mainChartState.data.priceData[
                  mainChartState.data.priceData.length - 1
                ];
              if (mainLastBar && Number.isFinite(mainLastBar.close)) {
                fallbackPrice = mainLastBar.close;
                logError(
                  "[PopupChart] Fallback: Using main chart last price for popup chart",
                  new Error(`Price: ${fallbackPrice}`),
                );
              }
            }
          }
          // As a last resort, use 1 if no price is available
          if (!fallbackPrice || !Number.isFinite(fallbackPrice))
            fallbackPrice = 1;
          // Create a single fallback bar
          const now = Math.floor(Date.now() / 1000);
          const barTime = Math.floor(now / apiInterval) * apiInterval;
          data = [
            {
              time: barTime,
              open: fallbackPrice,
              high: fallbackPrice,
              low: fallbackPrice,
              close: fallbackPrice,
              volume: 0,
            },
          ];
          logError(
            "[PopupChart] Fallback: Created new current bar with fallback price",
            new Error(`Price: ${fallbackPrice}`),
          );
        }

        // Defensive: If still no data, show error
        if (!data || data.length === 0) {
          if (this.container) {
            const errorMessage = document.createElement("div");
            errorMessage.id = "chart-error-message";
            errorMessage.style.cssText =
              "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 4px; z-index: 10;";
            errorMessage.textContent = `No valid data for this timeframe.`;
            this.container.appendChild(errorMessage);
            setTimeout(() => errorMessage.remove(), 3000);
          }
          return;
        }

        // Process and display the data
        if (
          window.performanceOptimizer &&
          typeof window.performanceOptimizer.scheduleWork === "function"
        ) {
          window.performanceOptimizer.scheduleWork(() => {
            this.processFetchedData(data, cacheKey, isLineSeries);
          }, "idle");
        } else {
          this.processFetchedData(data, cacheKey, isLineSeries);
        }
      },

      /**
       * Shows a loading indicator in the chart container.
       * @param {HTMLElement} container - The chart container element.
       */
      showLoadingIndicator: function (container) {
        if (!container) return;
        // Check for existing loading indicators in one query
        const existingLoading = document.querySelector(
          "#popup-chart-loading, #popup-chart-priority-loading, #chart-loading-indicator",
        );
        if (existingLoading) return;

        const loadingIndicator = document.createElement("div");
        loadingIndicator.id = "chart-loading-indicator";
        loadingIndicator.style.cssText =
          "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 4px; z-index: 10;";
        loadingIndicator.textContent = "Loading chart data...";
        container.appendChild(loadingIndicator);
      },

      /**
       * Processes and displays the fetched chart data.
       * @param {Array} data - The chart data to display.
       * @param {string} cacheKey - The key for caching the data.
       * @param {boolean} isLineSeries - Whether to display as a line series.
       */
      processFetchedData: function (data, cacheKey, isLineSeries) {
        if (!data || data.length === 0) {
          throw new Error("No data received from API");
        }
        // Ensure all bars have valid volume
        const safeData = data.map((b) => ({
          ...b,
          volume: Number.isFinite(b.volume) ? b.volume : 0,
        }));
        // Received data from API
        this.cacheData(cacheKey, safeData);
        this.processChartData(safeData, isLineSeries);
      },

      mapApiIntervalToString: function (intervalSeconds) {
        const reverseIntervalMap = {
          60: "1",
          300: "5",
          900: "15",
          1800: "30",
          3600: "60",
          14400: "240",
          86400: "D",
          // Note: Bitstamp doesn't support weekly or monthly intervals
          // For weekly/monthly, we'll use daily data
          604800: "D", // 1 week -> use daily data
          2592000: "D", // 1 month -> use daily data
        };
        return (
          reverseIntervalMap[intervalSeconds] || intervalSeconds.toString()
        ); // Fallback or pass as is
      },

      /**
       * Maps chart interval to API-compatible interval.
       * @param {string} interval - The chart interval (e.g., '60').
       * @returns {number} The API interval in seconds.
       */
      mapIntervalToApi: function (interval) {
        const intervals = {
          1: 60, // 1 minute
          5: 300, // 5 minutes
          15: 900, // 15 minutes
          30: 1800, // 30 minutes
          60: 3600, // 1 hour
          240: 14400, // 4 hours
          D: 86400, // 1 day
          "1D": 86400, // 1 day (alternate)
          // Use actual weekly and monthly intervals for proper aggregation
          "1W": 604800, // 1 week
          "1M": 2592000, // 1 month
        };
        return intervals[interval] || 3600; // Default to 1 hour in seconds
      },

      /**
       * Caches chart data for future use.
       * @param {string} key - The cache key.
       * @param {Array} data - The data to cache.
       */
      cacheData: function (key, data) {
        this.chartData[key] = data;
      },

      /**
       * Retrieves cached chart data.
       * @param {string} key - The cache key.
       * @returns {Array|null} The cached data or null if not found.
       */
      getCachedData: function (key) {
        return this.chartData[key] || null;
      },

      /**
       * Processes chart data and updates the series.
       * @param {Array} data - The data to process.
       * @param {boolean} isLineSeries - Whether to display as a line series.
       */
      processChartData: function (data, isLineSeries) {
        if (!this.series) {
          return;
        }
        if (isLineSeries) {
          const lineData = data.map((d) => ({ time: d.time, value: d.close }));
          try {
            this.series.setData(lineData);
          } catch (e) {
            logError("Error setting line data in processChartData", e);
          }
          this.barArray = [];
        } else {
          // Defensive: log and check data for 1D and 1W intervals
          if (
            this.currentInterval === "D" ||
            this.currentInterval === "1D" ||
            this.currentInterval === "1W"
          ) {
            if (!Array.isArray(data) || data.length === 0) {
              console.error(
                `[popupChart] No data provided to setData for interval ${this.currentInterval}.`,
              );
            } else if (
              !(
                "time" in data[0] &&
                "open" in data[0] &&
                "high" in data[0] &&
                "low" in data[0] &&
                "close" in data[0]
              )
            ) {
              console.error(
                `[popupChart] Data format invalid for interval ${this.currentInterval}`,
              );
            }
          }
          try {
            const filtered = window.utils.filterValidBars(data);
            if (filtered.length !== data.length) {
              console.error(
                `[popupChart] Invalid bars filtered out in processChartData for interval ${this.currentInterval}`,
                { original: data, filtered },
              );
            }
            if (filtered.length === 0) {
              // All bars were invalid, likely due to aggregation or data fetch issue
              logError(
                `[popupChart] All bars filtered out for interval ${this.currentInterval}. Possible data/aggregation issue.`,
                new Error("No valid bars for chart"),
              );
              // Optionally, show a user-friendly message
              if (this.container) {
                const errorMessage = document.createElement("div");
                errorMessage.id = "chart-error-message";
                errorMessage.style.cssText =
                  "position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.7); color: white; padding: 10px 20px; border-radius: 4px; z-index: 10;";
                errorMessage.textContent = `No valid data for this timeframe.`;
                this.container.appendChild(errorMessage);
                setTimeout(() => errorMessage.remove(), 3000);
              }
              return;
            }
            // Check for any null/undefined in filtered
            if (filtered.some((b) => !b || typeof b !== "object")) {
              console.error(
                "[popupChart] Null/undefined bar detected after filtering",
                filtered,
              );
            }

            // Iterate through filtered bars and log errors for invalid ones
            filtered.forEach((bar, index) => {
              if (
                !bar || // Check if bar itself is null or undefined
                !Number.isFinite(bar.time) ||
                !Number.isFinite(bar.open) ||
                !Number.isFinite(bar.high) ||
                !Number.isFinite(bar.low) ||
                !Number.isFinite(bar.close) ||
                !Number.isFinite(bar.volume)
              ) {
                logError(
                  `[popupChart] Invalid bar found in 'filtered' data at index ${index} before setData`,
                  new Error(`Invalid bar: ${JSON.stringify(bar)}`),
                );
                // Depending on requirements, we might choose to filter out this specific bar again
                // or prevent setData altogether. For now, logging is the primary goal.
              }
            });

            try {
              this.series.setData(filtered);
            } catch (e) {
              logError("Error setting candlestick data in processChartData", e);
              // console.error previously existed, logError is now preferred.
              // Keep original console.error for now if specific debugging context was intended.
              console.error(
                "[popupChart] Error calling setData on series (candlestick)",
                e,
                data,
              );
            }
          } catch (e) {
            // This catch is for window.utils.filterValidBars or other logic inside the outer try
            logError(
              "[popupChart] Error processing or filtering chart data",
              e,
            );
            console.error(
              // Keep original console.error for now
              "[popupChart] Error in processChartData (outer try)",
              e,
              data,
            );
          }

          // Store historical bars
          this.barArray = data.slice(); // `data` is historical, `filtered` was used for the setData above.
          // Using `data` directly here assumes `filterValidBars` was for display mostly
          // and we want to keep original data for barArray if possible.
          // Or, use `filtered_historical_data` if that was stored.
          // For now, let's assume `data` is the source of truth for historical bars.

          // Initialize currentBar based on the last historical bar or create a new one.
          const now = Math.floor(Date.now() / 1000);
          const apiInterval = this.mapIntervalToApi(this.currentInterval);
          const currentCalculatedBarStartTime =
            Math.floor(now / apiInterval) * apiInterval;

          let lastHistoricalBar =
            this.barArray.length > 0
              ? this.barArray[this.barArray.length - 1]
              : null;

          const localSafeNum = (val, fallback = 0) =>
            typeof val === "number" && isFinite(val) ? val : fallback;

          // Check if the last historical bar is recent enough
          const isLastBarRecent =
            lastHistoricalBar &&
            now - lastHistoricalBar.time <= apiInterval * 2;

          if (
            lastHistoricalBar &&
            lastHistoricalBar.time === currentCalculatedBarStartTime &&
            isLastBarRecent
          ) {
            // Use the existing bar if it's for the current time period and recent
            this.currentBar = { ...lastHistoricalBar };
            this.currentBarVolume = localSafeNum(lastHistoricalBar.volume);
          } else if (
            !lastHistoricalBar ||
            currentCalculatedBarStartTime > lastHistoricalBar.time ||
            !isLastBarRecent
          ) {
            // Create a new current bar if:
            // 1. No historical data exists
            // 2. Current time is ahead of the last historical bar
            // 3. Last historical bar is too old (more than 2 intervals)

            let openingPrice =
              lastHistoricalBar && isLastBarRecent
                ? localSafeNum(lastHistoricalBar.close)
                : 0;

            // If we have no recent data, try to get a current price from the main chart
            if (openingPrice === 0 && window.currentPair) {
              const mainChartState = window.chartStates?.get(
                window.currentPair,
              );
              if (mainChartState?.data?.priceData?.length > 0) {
                const mainLastBar =
                  mainChartState.data.priceData[
                    mainChartState.data.priceData.length - 1
                  ];
                if (mainLastBar && now - mainLastBar.time <= 300) {
                  // Within 5 minutes
                  openingPrice = localSafeNum(mainLastBar.close);
                }
              }
            }

            this.currentBar = {
              time: currentCalculatedBarStartTime,
              open: openingPrice,
              high: openingPrice,
              low: openingPrice,
              close: openingPrice,
              volume: 0,
            };
            this.currentBarVolume = 0;

            // Remove any existing bar at this time and add the new one
            const existingBarIndex = this.barArray.findIndex(
              (b) => b.time === this.currentBar.time,
            );
            if (existingBarIndex !== -1) {
              this.barArray.splice(existingBarIndex, 1);
            }
            this.barArray.push({ ...this.currentBar });

            console.log(
              `[PopupChart] Created new current bar at ${new Date(currentCalculatedBarStartTime * 1000).toISOString()} with price ${openingPrice}`,
            );
          } else {
            // If the last historical bar is newer than the calculated current bar, do not create a new bar
            this.currentBar = null;
            this.currentBarVolume = 0;
          }

          // Ensure barArray is not too long.
          if (this.barArray.length > 500) {
            this.barArray.shift();
          }

          // Update the series with the barArray (historical + current developing bar).
          const finalFilteredBars = window.utils.filterValidBars(this.barArray);
          try {
            this.series.setData(finalFilteredBars);
          } catch (e) {
            logError(
              "Error setting data in processChartData (final with currentBar)",
              e,
            );
          }
        }
        try {
          this.chart.timeScale().fitContent();
        } catch (e) {
          // Error fitting chart content
        }
      },

      /**
       * Fetches additional data for pagination or updates (stub method).
       * @param {string} symbol - The formatted symbol.
       * @param {number} interval - The API interval.
       * @param {Array} existingData - The current data.
       */
      fetchAdditionalData: function (symbol, interval, existingData) {
        // Stub for future pagination or real-time updates
        // Fetching additional data
      },

      /**
       * Subscribes to websocket updates for the current symbol.
       */
      subscribeToWebSocket: function () {
        if (!this.currentSymbol) return;
        if (!this.wsManager) return;

        const symbol = this.currentSymbol.replace("USD", "").toLowerCase();
        const channel = `live_trades_${symbol}usd`;

        // CRITICAL FIX: Always create a fresh handler for each pair
        this.wsHandler = (message) => {
          if (message.event === "trade" && message.data) {
            const price = parseFloat(message.data.price);
            const volume = parseFloat(message.data.amount);
            const type = message.data.type;
            const timestamp = parseInt(message.data.timestamp); // Extract timestamp from message

            if (
              Number.isFinite(price) &&
              Number.isFinite(volume) &&
              Number.isFinite(timestamp)
            ) {
              this.updateRealTimeBar(price, volume, type === 0, timestamp);
            }
          }
        };

        // Ensure connection
        if (!this.wsManager.isConnected()) {
          this.wsManager.connect();
        }

        // Subscribe to the channel with the new handler
        this.wsManager.subscribe(channel, (data) => {
          popupMessageQueue.add(data);
        });

        console.log(
          `[PopupChart] Subscribed to WebSocket channel: ${channel} for ${this.currentSymbol}`,
        );
      },

      /**
       * Unsubscribes from websocket updates.
       */
      unsubscribeFromWebSocket: function () {
        if (!this.currentSymbol || !this.wsManager) return;

        const symbol = this.currentSymbol.replace("USD", "").toLowerCase();
        const channel = `live_trades_${symbol}usd`;

        console.log(
          `[PopupChart] Unsubscribing from WebSocket channel: ${channel} for ${this.currentSymbol}`,
        );

        // Unsubscribe from the channel
        this.wsManager.unsubscribe(channel);

        // Clear the handler to prevent memory leaks
        this.wsHandler = null;
      },

      /**
       * Updates the current bar with real-time price data using accurate timestamp handling.
       *
       * Key improvements for real-time candle rendering:
       * 1. Uses WebSocket message timestamp instead of system time for accurate bar placement
       * 2. Properly manages bar transitions (current bar updates vs new bar creation)
       * 3. Leverages Lightweight Charts' efficient update mechanism without throttling
       * 4. Maintains data consistency between WebSocket and polling updates
       *
       * @param {number} price - Price value
       * @param {number} volume - Volume value
       * @param {boolean} isBuy - Whether this is a buy trade
       * @param {number} timestamp - Trade timestamp from WebSocket message
       */
      updateRealTimeBar: function (price, volume, isBuy, timestamp) {
        // Prevent updates during chart transitions for stability

        // Only accept valid, positive price, volume, and timestamp
        if (
          !Number.isFinite(price) ||
          !Number.isFinite(volume) ||
          !Number.isFinite(timestamp) ||
          price <= 0
        ) {
          logError(
            "Invalid price, volume, or timestamp in updateRealTimeBar",
            new Error(
              `Price: ${price}, Volume: ${volume}, Timestamp: ${timestamp}`,
            ),
          );
          return;
        }
        if (!this.series) {
          return;
        }

        // Always use trade timestamp (seconds) for bar calculations
        const interval = this.mapIntervalToApi(this.currentInterval);
        const barTime = Math.floor(timestamp / interval) * interval;

        // Validate timestamp is not too old (more than 1 hour old)
        const currentTime = Math.floor(Date.now() / 1000);
        if (timestamp < currentTime - 3600) {
          console.warn(
            `[PopupChart] Ignoring very old trade data: ${timestamp} (current: ${currentTime})`,
          );
          return;
        }

        // const interval = this.mapIntervalToApi(this.currentInterval);
        // const barTime = Math.floor(timestamp / interval) * interval; // Align timestamp to bar interval

        // Handle weekly and monthly intervals with proper timestamp logic
        const isWeekly =
          this.currentInterval === "1W" || this.currentInterval === 604800;
        const isMonthly =
          this.currentInterval === "1M" || this.currentInterval === 2592000;

        if (isWeekly || isMonthly) {
          this.updateWeeklyMonthlyBar(price, volume, timestamp, isWeekly);
          return;
        }

        // Standard intraday logic with timestamp-based bar management
        if (!this.currentBar || this.currentBar.time < barTime) {
          // New bar: trade timestamp is beyond the current bar's interval
          if (this.currentBar) {
            // Finalize the current bar
            const prevBarIndex = this.barArray.findIndex(
              (b) => b.time === this.currentBar.time,
            );
            if (prevBarIndex !== -1) {
              this.barArray[prevBarIndex].volume = this.currentBarVolume || 0;
            }
          }

          // Create new bar
          this.currentBarVolume = volume;
          this.currentBar = {
            time: barTime,
            open: price,
            high: price,
            low: price,
            close: price,
            volume: volume,
          };

          // Remove existing bar if it exists and add new one
          const existingBarIndex = this.barArray.findIndex(
            (b) => b.time === this.currentBar.time,
          );
          if (existingBarIndex !== -1) {
            this.barArray.splice(existingBarIndex, 1);
          }
          this.barArray.push({ ...this.currentBar });

          // Maintain array size
          if (this.barArray.length > 500) {
            this.barArray.shift();
          }

          // Update chart
          try {
            this.series.update({ ...this.currentBar });
          } catch (e) {
            logError("[popupChart] Error in series.update (new bar)", e);
          }
        } else if (this.currentBar.time === barTime) {
          // Update current bar: trade belongs to the same time interval
          this.currentBar.close = price;
          if (price > this.currentBar.high) this.currentBar.high = price;
          if (price < this.currentBar.low) this.currentBar.low = price;
          this.currentBarVolume += volume;
          this.currentBar.volume = this.currentBarVolume;

          // Update in barArray
          const barIndex = this.barArray.findIndex(
            (b) => b.time === this.currentBar.time,
          );
          if (barIndex !== -1) {
            this.barArray[barIndex] = { ...this.currentBar };
          }

          // Update chart
          try {
            this.series.update({ ...this.currentBar });
          } catch (e) {
            logError("[popupChart] Error in series.update (existing bar)", e);
          }
        } else {
          // Late trade: timestamp is before the current bar (unlikely in real-time, log for debugging)
          console.log(
            `[PopupChart] Late trade for timestamp ${timestamp}, current bar time ${this.currentBar.time}`,
          );
          return; // Skip updating to avoid disrupting real-time flow
        }

        // Track real-time update performance (log every 500 updates to reduce console noise)
        if (!this.realtimeUpdateCount) this.realtimeUpdateCount = 0;
        if (!this.lastRealtimeLogTime) this.lastRealtimeLogTime = Date.now();

        this.realtimeUpdateCount++;
        const now = Date.now();
        if (this.realtimeUpdateCount % 500 === 0) {
          const timeDiff = now - this.lastRealtimeLogTime;
          const updatesPerSecond = (500 * 1000) / timeDiff;
          console.log(
            `[PopupChart] Real-time updates: ${updatesPerSecond.toFixed(1)} updates/sec (${this.realtimeUpdateCount} total)`,
          );
          this.lastRealtimeLogTime = now;
        }
      },

      /**
       * Refreshes stale chart data with current price - ENHANCED STABILITY
       */
      refreshStaleData: function (currentPrice, timestamp) {
        if (!this.currentSymbol) return;

        console.log(
          `[PopupChart] Refreshing stale data for ${this.currentSymbol}`,
        );

        // Preserve existing historical data instead of clearing it
        const interval = this.mapIntervalToApi(this.currentInterval);
        const barTime = Math.floor(timestamp / interval) * interval;

        // Create new current bar
        this.currentBar = {
          time: barTime,
          open: currentPrice,
          high: currentPrice,
          low: currentPrice,
          close: currentPrice,
          volume: 0,
        };
        this.currentBarVolume = 0;

        // Update or add current bar to existing barArray instead of clearing
        const existingBarIndex = this.barArray.findIndex(
          (b) => b.time === barTime,
        );
        if (existingBarIndex !== -1) {
          // Update existing bar
          this.barArray[existingBarIndex] = { ...this.currentBar };
        } else {
          // Add new bar while preserving historical data
          this.barArray.push({ ...this.currentBar });

          // Maintain array size by removing oldest bars if needed
          if (this.barArray.length > 500) {
            this.barArray.shift();
          }
        }

        // Update the chart with all bars, not just the current one
        try {
          const filteredBars = window.utils.filterValidBars(this.barArray);
          if (filteredBars.length > 0) {
            this.series.setData(filteredBars);
            console.log(
              `[PopupChart] Refreshed chart with ${filteredBars.length} bars, current bar at ${new Date(barTime * 1000).toISOString()}`,
            );
          } else {
            console.warn(
              "[PopupChart] No valid bars after filtering during refresh",
            );
          }
        } catch (e) {
          logError("[popupChart] Error refreshing chart data", e);
        }

        // Fetch fresh historical data in the background with better error handling
        setTimeout(() => {
          if (this.currentSymbol && this.currentInterval) {
            this.loadChartData(this.currentSymbol, this.currentInterval).catch(
              (error) => {
                console.error(
                  "[PopupChart] Error loading fresh data during refresh:",
                  error,
                );
              },
            );
          }
        }, 2000); // Increased delay to prevent race conditions
      },

      /**
       * Updates weekly and monthly bars with proper timestamp handling
       */
      updateWeeklyMonthlyBar: function (price, volume, timestamp, isWeekly) {
        if (!this.barArray.length) return;

        const lastBar = this.barArray[this.barArray.length - 1];
        const getStartOfWeek = (timestampSeconds) => {
          const date = new Date(timestampSeconds * 1000);
          const day = date.getUTCDay();
          const diff = (day + 6) % 7;
          date.setUTCDate(date.getUTCDate() - diff);
          date.setUTCHours(0, 0, 0, 0);
          return Math.floor(date.getTime() / 1000);
        };
        const getStartOfMonth = (timestampSeconds) => {
          const date = new Date(timestampSeconds * 1000);
          date.setUTCDate(1);
          date.setUTCHours(0, 0, 0, 0);
          return Math.floor(date.getTime() / 1000);
        };

        const samePeriod = isWeekly
          ? getStartOfWeek(timestamp) === getStartOfWeek(lastBar.time)
          : getStartOfMonth(timestamp) === getStartOfMonth(lastBar.time);

        if (samePeriod) {
          // Update the last bar
          lastBar.high = Math.max(lastBar.high, price);
          lastBar.low = Math.min(lastBar.low, price);
          lastBar.close = price;
          lastBar.volume += volume;
          this.currentBar = { ...lastBar };
          this.currentBarVolume = lastBar.volume;
          this.barArray[this.barArray.length - 1] = { ...lastBar };

          try {
            this.series.update({ ...lastBar });
          } catch (e) {
            logError("[popupChart] Error in series.update (weekly/monthly)", e);
          }
        }
      },

      /**
       * Test function to verify real-time update performance
       * Can be called from browser console: window.popupChart.testRealtimeUpdates()
       */
      testRealtimeUpdates: function () {
        console.log("=== PopupChart Real-time Update Test ===");
        console.log(`Current symbol: ${this.currentSymbol}`);
        console.log(`Current interval: ${this.currentInterval}`);
        console.log(
          `Total real-time updates: ${this.realtimeUpdateCount || 0}`,
        );
        console.log(`Chart initialized: ${!!this.chart}`);
        console.log(
          `Series disposed: ${this.series?._internal_isDisposed || "N/A"}`,
        );
        console.log(`Current bar time: ${this.currentBar?.time || "N/A"}`);
        console.log(`Bar array length: ${this.barArray?.length || 0}`);
        console.log(
          `Last price: ${this.barArray?.[this.barArray.length - 1]?.close || "N/A"}`,
        );
        console.log(
          `WebSocket connected: ${this.wsManager?.isConnected() || false}`,
        );
        console.log("========================================");
      },

      /**
       * Manually refresh chart data
       * Can be called from browser console: window.popupChart.refreshData()
       */
      refreshData: function () {
        if (!this.currentSymbol || !this.currentInterval) {
          console.log("[PopupChart] No symbol or interval set, cannot refresh");
          return;
        }

        console.log(
          `[PopupChart] Manually refreshing data for ${this.currentSymbol} ${this.currentInterval}`,
        );

        // Clear current data
        this.barArray = [];
        this.currentBar = null;
        this.currentBarVolume = 0;

        // Reload chart data
        this.loadChartData(this.currentSymbol, this.currentInterval);
      },

      /**
       * Resets the popup chart for a new pair.
       * This method ensures all state is properly cleared before switching pairs.
       * @param {string} newSymbol - The new symbol to switch to.
       * @param {string} newInterval - The interval to use.
       */
      resetForNewPair: function (newSymbol, newInterval) {
        console.log(
          `[PopupChart] Resetting for new pair: ${newSymbol}, interval: ${newInterval}`,
        );

        // Clear current state
        this.clear();

        // Reset state variables
        this.currentSymbol = newSymbol;
        this.currentInterval = newInterval || "60";
        this.isInitializing = false;

        // Reset performance tracking
        this.realtimeUpdateCount = 0;
        this.lastRealtimeLogTime = 0;
        this.lastStaleRefresh = 0;
        this.currentBarUpdateToken = 0;

        // Reset bar state
        this.currentBar = null;
        this.currentBarVolume = 0;

        // Clear data arrays
        this.barArray = [];
        this.chartData = {};

        console.log(`[PopupChart] Reset complete for ${newSymbol}`);
      },

      clear: function () {
        console.log(`[PopupChart] Starting cleanup for ${this.currentSymbol}`);

        // Unsubscribe from WebSocket first
        this.unsubscribeFromWebSocket && this.unsubscribeFromWebSocket();

        // Clear WebSocket handler to prevent old handlers from being called
        this.wsHandler = null;

        // Preserve historical data during cleanup to prevent data loss
        const historicalBars = this.barArray.slice(0, -1); // Keep all but the current bar

        this.barArray = [];
        this.currentBar = null;
        this.currentBarVolume = 0;
        this.chartData = {};
        this.realtimeUpdateCount = 0;
        this.lastRealtimeLogTime = 0;

        if (this.series && typeof this.series.setData === "function") {
          try {
            // Only clear if we're actually switching symbols, not just refreshing
            if (this.currentSymbol) {
              this.series.setData([]);
            }
          } catch (e) {
            logError("[popupChart] Error clearing chart series", e);
          }
        }

        const loading = document.getElementById("chart-loading-indicator");
        if (loading) loading.remove();
        const errorMsg = document.getElementById("chart-error-message");
        if (errorMsg) errorMsg.remove();

        // CRITICAL FIX: Properly reset the WebSocket manager for new pair
        if (this.wsManager) {
          console.log("[PopupChart] Closing WebSocket manager for pair switch");
          try {
            // Check if WebSocket is in a state that can be closed
            if (
              this.wsManager.ws &&
              this.wsManager.ws.readyState !== WebSocket.CLOSED
            ) {
              this.wsManager.close();
            }
          } catch (error) {
            console.warn(
              "[PopupChart] Error closing WebSocket manager:",
              error,
            );
          }

          // Create a fresh WebSocket manager instance for the new pair
          // Add a small delay to prevent connection conflicts
          setTimeout(() => {
            try {
              this.wsManager = new BitstampBTCWsManager();
              console.log(
                "[PopupChart] Created new WebSocket manager instance",
              );
            } catch (error) {
              console.error(
                "[PopupChart] Error creating new WebSocket manager:",
                error,
              );
            }
          }, 50);
        }

        // Log cleanup for debugging
        console.log(
          `[PopupChart] Cleared chart data for ${this.currentSymbol}, preserved ${historicalBars.length} historical bars`,
        );
      },

      /**
       * Reconnects the WebSocket connection.
       */
      reconnect: function () {
        try {
          console.log("[PopupChart] Reconnecting WebSocket...");
          if (this.ws) {
            this.ws.close();
            this.ws = null;
          }
          this.wsHandler = null;
          setTimeout(() => {
            this.subscribeToWebSocket();
            // After reconnect, fetch the latest bar from the API to complete the current bar
            this.currentBarUpdateToken++;
            const thisToken = this.currentBarUpdateToken;
            this.fetchAndUpdateCurrentBarFromApi(thisToken);
          }, 1000);
        } catch (error) {
          console.error("[PopupChart] Error during reconnection:", error);
        }
      },

      fetchAndUpdateCurrentBarFromApi: async function (token) {
        if (!this.currentSymbol || !this.currentInterval) return;
        try {
          // Always use lowercase and concatenate base+quote for Bitstamp
          const symbol = this.currentSymbol.replace("USD", "").toLowerCase();
          const pair = symbol + "usd";
          const response = await fetch(
            `https://www.bitstamp.net/api/v2/ohlc/${pair}/?step=${this.mapIntervalToApi(this.currentInterval)}&limit=1`,
          );
          if (!response.ok)
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          const data = await response.json();
          // --- Race condition check ---
          if (token !== this.currentBarUpdateToken) return; // Outdated, ignore

          if (
            data &&
            data.data &&
            data.data.ohlc &&
            data.data.ohlc.length > 0
          ) {
            const bar = data.data.ohlc[0];
            const latestBar = {
              time: parseInt(bar.timestamp),
              open: parseFloat(bar.open),
              high: parseFloat(bar.high),
              low: parseFloat(bar.low),
              close: parseFloat(bar.close),
              volume: parseFloat(bar.volume),
            };
            const lastBar = this.barArray[this.barArray.length - 1];
            if (
              lastBar &&
              Math.abs(lastBar.time - latestBar.time) <=
                this.mapIntervalToApi(this.currentInterval)
            ) {
              Object.assign(lastBar, latestBar);
              this.currentBar = { ...lastBar };
              this.currentBarVolume = lastBar.volume;
            } else {
              this.barArray.push(latestBar);
              this.currentBar = { ...latestBar };
              this.currentBarVolume = latestBar.volume;
              if (this.barArray.length > 500) this.barArray.shift();
            }
            const filteredBars = window.utils.filterValidBars(this.barArray);
            if (filteredBars.length > 0) {
              this.series.setData(filteredBars);
            }
            console.log(
              `[PopupChart] Updated current bar from API after reconnect`,
            );
          }
        } catch (error) {
          if (token !== this.currentBarUpdateToken) return; // Outdated, ignore
          console.error(
            "[PopupChart] Failed to fetch current bar from API after reconnect:",
            error,
          );
        }
      },
    };

    // Listen for main chart pair switches and refresh popup chart if open
    let lastPairSwitch = null;
    let lastPairSwitchInterval = null;
    let pairSwitchTimeout = null;

    const handlePairSwitch = (newPair, eventType) => {
      const interval = window.popupChart.currentInterval || "60";
      const switchKey = `${newPair}-${interval}`;

      // Debounce rapid pair switches
      if (lastPairSwitch === switchKey && eventType !== "chartSwitched") {
        console.log(
          `[PopupChart] Skipping duplicate pair switch to ${newPair} ${interval} (${eventType})`,
        );
        return;
      }

      // Clear any pending timeout
      if (pairSwitchTimeout) {
        clearTimeout(pairSwitchTimeout);
      }

      // Set debounce timeout
      pairSwitchTimeout = setTimeout(() => {
        lastPairSwitch = switchKey;
        lastPairSwitchInterval = interval;
        pairSwitchTimeout = null;

        // Only refresh if popup is actually visible and open
        const popupContainer = document.getElementById("popup-chart-container");
        const popupWrapper = document.getElementById("popup-chart-wrapper");

        if (
          popupContainer &&
          popupWrapper &&
          popupContainer.style.display !== "none" &&
          popupWrapper.style.display === "flex"
        ) {
          console.log(
            `[PopupChart] ${eventType} event: switching popup to ${newPair}, interval=${interval}`,
          );

          // CRITICAL FIX: Ensure proper cleanup and initialization sequence
          const switchPopupPair = async () => {
            try {
              // Step 1: Reset popup chart for new pair
              const newSymbol = newPair.toUpperCase() + "USD";
              if (window.popupChart.resetForNewPair) {
                window.popupChart.resetForNewPair(newSymbol, interval);
              } else if (window.popupChart.clear) {
                window.popupChart.clear();
              }

              // Step 2: Wait a moment for cleanup to complete
              await new Promise((resolve) => setTimeout(resolve, 200));

              // Step 3: Initialize with new pair
              if (window.updatePopupChart) {
                window.updatePopupChart(newPair, interval);
              } else {
                window.popupChart.initialize(newSymbol, interval);
              }

              // Step 4: Signal popupchart module ready after pair switch
              setTimeout(() => {
                if (window.signalModuleReady) {
                  window.signalModuleReady("popupchart", newPair);
                }
              }, 1000); // Delay to ensure chart is initialized
            } catch (error) {
              console.error("[PopupChart] Error during pair switch:", error);
            }
          };

          // Execute the pair switch
          switchPopupPair();
        }
      }, 100); // 100ms debounce
    };

    document.addEventListener("pairChanged", (e) => {
      const newPair = e.detail?.pair;
      if (!newPair) return;
      handlePairSwitch(newPair, "pairChanged");
    });

    // Fallback listener for chartSwitched event (in case it's dispatched by other parts)
    document.addEventListener("chartSwitched", (e) => {
      const newPair = e.detail?.newPair;
      if (!newPair) return;
      handlePairSwitch(newPair, "chartSwitched");
    });

    // Draggable Popup Chart IIFE was removed as its logic is now in modules/charts/popupChart/PopupChartUI.js
  })();
})();
