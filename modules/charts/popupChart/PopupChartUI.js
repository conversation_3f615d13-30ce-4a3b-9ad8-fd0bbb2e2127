(function(window, document) {
    // Utility functions for popup chart
    const popupChartUtils = {
        showLoadingIndicator: function(container) {
            let loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            loadingIndicator.id = 'popup-chart-loading';
            loadingIndicator.textContent = 'Loading chart...';
            container.appendChild(loadingIndicator);
            return loadingIndicator;
        },
        updateTimeframeButtons: function(interval) {
            document.querySelectorAll('.popup-timeframe-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.interval === interval);
            });
        },
        isChartVisible: function() {
            const container = document.getElementById('popup-chart-wrapper');
            return container && container.style.display !== 'none';
        }
    };

    // --- Stable Drag Logic for Popup Chart ---
    function loadPopupChartState() {
        const popupChartWrapper = document.getElementById('popup-chart-wrapper');
        if (!popupChartWrapper) return;
        try {
            popupChartWrapper.style.width = '400px';
            popupChartWrapper.style.height = '300px';
            popupChartWrapper.style.left = '10px';
            popupChartWrapper.style.top = '10px';
            const state = JSON.parse(localStorage.getItem('popupChartState'));
            if (state) {
                popupChartWrapper.style.width = state.width || '400px';
                popupChartWrapper.style.height = state.height || '300px';
                popupChartWrapper.style.left = state.left || '10px';
                popupChartWrapper.style.top = state.top || '10px';
            }
        } catch (e) {
            console.warn('Error loading popup chart state:', e);
        }
    }

    function savePopupChartState() {
        const popupChartWrapper = document.getElementById('popup-chart-wrapper');
        if (!popupChartWrapper) return;
        localStorage.setItem('popupChartState', JSON.stringify({
            width: popupChartWrapper.style.width,
            height: popupChartWrapper.style.height,
            left: popupChartWrapper.style.left,
            top: popupChartWrapper.style.top,
            visible: popupChartWrapper.style.display === 'flex'
        }));
    }

    // Drag logic
    function setupPopupChartDrag() {
        const popupChartWrapper = document.getElementById('popup-chart-wrapper');
        const header = popupChartWrapper?.querySelector('.popup-chart-header');
        let isDragging = false, dragOffsetX, dragOffsetY;
        let lastDragFrame = 0;
        const minDragFrameInterval = 33; // ~30fps

        if (header && popupChartWrapper) {
            header.addEventListener('mousedown', e => {
                isDragging = true;
                dragOffsetX = e.clientX - popupChartWrapper.getBoundingClientRect().left;
                dragOffsetY = e.clientY - popupChartWrapper.getBoundingClientRect().top;
                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            });
        }

        function throttledDragAnimation() {
            const now = Date.now();
            if (now - lastDragFrame >= minDragFrameInterval) {
                lastDragFrame = now;
                const chartContainer = document.querySelector('.price-chart-container');
                const chartRect = chartContainer.getBoundingClientRect();
                let newLeft = lastEvent.clientX - dragOffsetX - chartRect.left;
                let newTop = lastEvent.clientY - dragOffsetY - chartRect.top;
                newLeft = Math.max(0, Math.min(newLeft, chartRect.width - popupChartWrapper.offsetWidth));
                newTop = Math.max(0, Math.min(newTop, chartRect.height - popupChartWrapper.offsetHeight));
                popupChartWrapper.style.left = newLeft + 'px';
                popupChartWrapper.style.top = newTop + 'px';
            } else {
                setTimeout(() => requestAnimationFrame(throttledDragAnimation), minDragFrameInterval - (now - lastDragFrame));
            }
        }

        function onMouseMove(e) {
            if (!isDragging) return;
            lastEvent = e;
            if (lastEvent) {
                requestAnimationFrame(throttledDragAnimation);
            }
        }

        function onMouseUp() {
            isDragging = false;
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
            savePopupChartState();
        }
    }

    // Popup chart initialization and update logic
    function initializePopupChart(symbol = 'BTCUSD', interval = '60') {
        if (!popupChartUtils.isChartVisible()) return;

        window.currentPopupChartInterval = interval;
        window.currentPopupChartSymbol = symbol;

        if (window.popupChart) {
            window.popupChart.initialize(symbol, interval);
            setTimeout(() => popupChartUtils.updateTimeframeButtons(interval), 50);
        }
    }

    function updatePopupChart(pair, interval) {
        const popupChartWrapper = document.getElementById('popup-chart-wrapper');
        const popupChartContainer = document.getElementById('popup-chart-container');
        if (!popupChartWrapper) return;

        // Always show the wrapper and container
        popupChartWrapper.style.display = 'flex';
        if (popupChartContainer) popupChartContainer.style.display = 'block';

        const symbol = pair.toUpperCase() + 'USD';
        const currentInterval = interval || window.currentPopupChartInterval || '60';

        window.currentPopupChartInterval = currentInterval;
        window.currentPopupChartSymbol = symbol;

        popupChartUtils.updateTimeframeButtons(currentInterval);

        if (window.popupChart) {
            // CRITICAL FIX: Ensure proper cleanup before initialization
            const performUpdate = async () => {
                try {
                    // Step 1: Reset popup chart for new pair
                    const newSymbol = symbol;
                    if (window.popupChart.resetForNewPair) {
                        window.popupChart.resetForNewPair(newSymbol, currentInterval);
                    } else if (window.popupChart.clear) {
                        window.popupChart.clear();
                    }
                    
                    // Step 2: Wait for cleanup to complete
                    await new Promise(resolve => setTimeout(resolve, 150));
                    
                    // Step 3: Show loading indicator
                    const loadingIndicator = popupChartUtils.showLoadingIndicator(popupChartWrapper);

                    // Step 4: Force state update before initialize
                    window.popupChart.currentSymbol = symbol;
                    window.popupChart.currentInterval = currentInterval;
                    
                    // Step 5: Initialize chart
                    window.popupChart.initialize(symbol, currentInterval);
                    loadingIndicator.remove();

                    // Step 6: Load chart data with delay
                    setTimeout(() => {
                        if (window.popupChart && window.currentPopupChartSymbol === symbol) {
                            window.popupChart.loadChartData(symbol, currentInterval);
                        }
                    }, 300);
                    
                } catch (error) {
                    console.error('Error updating popup chart:', error);
                    const loadingIndicator = document.getElementById('popup-chart-loading');
                    if (loadingIndicator) loadingIndicator.remove();
                }
            };
            
            performUpdate();
        }
    }

    // Timeframe button logic
    function updatePopupChartTimeframe(interval) {
        if (!window.currentPopupChartSymbol) {
            console.error('No current symbol found for popup chart');
            return;
        }
        if (window.currentPopupChartInterval === interval) {
            return;
        }
        window.currentPopupChartInterval = interval;
        popupChartUtils.updateTimeframeButtons(interval);
        if (window.popupChart && typeof window.popupChart.updateTimeframe === 'function') {
            window.popupChart.updateTimeframe(interval);
        } else {
            console.error('Popup chart not initialized');
        }
    }

    // DOMContentLoaded setup
    document.addEventListener('DOMContentLoaded', function() {
        loadPopupChartState();
        setupPopupChartDrag();

        // Toggle popup chart visibility
        const chartToggleBtn = document.getElementById('tradingview-toggle-btn');
        const popupChartWrapper = document.getElementById('popup-chart-wrapper');
        if (chartToggleBtn && popupChartWrapper) {
            chartToggleBtn.onclick = null;
            chartToggleBtn.addEventListener('click', function() {
                const isVisible = popupChartWrapper.style.display === 'flex';
                if (isVisible) {
                    popupChartWrapper.style.display = 'none';
                    this.classList.remove('active');
                } else {
                    popupChartWrapper.style.display = 'flex';
                    this.classList.add('active');
                    const currentPair = window.currentPair || 'BTC';
                    const interval = window.currentPopupChartInterval || '60';
                    window.currentPopupChartSymbol = currentPair.toUpperCase() + 'USD';
                    window.currentPopupChartInterval = interval;
                    popupChartUtils.updateTimeframeButtons(interval);
                    if (window.popupChart && window.popupChart.initialize) {
                        try {
                            window.popupChart.initialize(window.currentPopupChartSymbol, interval);
                        } catch (error) {
                            console.error('Error initializing popup chart:', error);
                        }
                    }
                }
                if (typeof savePopupChartState === 'function') {
                    savePopupChartState();
                }
            });
        }

        // Timeframe button click handlers
        document.querySelectorAll('.popup-timeframe-btn').forEach(button => {
            button.addEventListener('click', function() {
                updatePopupChartTimeframe(this.dataset.interval);
            });
        });
    });

    // Expose to window
    window.loadPopupChartState = loadPopupChartState;
    window.savePopupChartState = savePopupChartState;
    window.initializePopupChart = initializePopupChart;
    window.updatePopupChart = updatePopupChart;
    window.updatePopupChartTimeframe = updatePopupChartTimeframe;
})(window, document);
