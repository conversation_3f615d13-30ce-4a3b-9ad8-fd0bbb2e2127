/**
 * Main Charts Module
 * Handles chart rendering, orderbook management, and indicator management
 * Uses centralized logger and error handler for all logging and error reporting
 */

(() => {
  // Create logger instance for this module
  const logger = window.logger ? window.logger.createLogger("Charts") : console;
  // Configuration and constants moved to modules/charts/chartutilities/configManager.js
  // Use window.configManager.CONFIG, window.configManager.PAIRS, window.configManager.utils, window.configManager.handleError

  const domCache = new Map();
  const chartStates = new Map();
  window.chartStates = chartStates;
  let currentPair = "BTC";
  window.currentPair = currentPair;
  window.currentActivePair = currentPair;
  let chartVersion = 0;

  // Performance optimization moved to modules/charts/chartutilities/performanceOptimizer.js
  // Use window.performanceOptimizer functions

  // Polling management moved to modules/charts/chartutilities/pollingManager.js
  // Use window.pollingManager functions

  // Add missing core variables
  let isInitializing = false;
  const preCalculateDataCache = new Map();
  window.preCalculateDataCache = preCalculateDataCache;

  // === BEGIN: Centralized Transition State Management ===

  /**
   * Centralized transition state management to prevent racing conditions
   */
  const TransitionStateManager = {
    isTransitioning: false,
    transitionStartTime: 0,
    transitionType: null,
    pendingUpdates: new Set(),
    lastTransitionEnd: 0, // CRITICAL FIX: Track last transition end time

    startTransition(type = "chart-switch") {
      // CRITICAL FIX: Prevent rapid successive transitions
      const now = Date.now();
      if (this.isTransitioning) {
        logger.debug(`[TransitionStateManager] Transition already in progress (${this.transitionType}), ignoring new ${type}`);
        return false;
      }

      // CRITICAL FIX: Add minimum interval between transitions
      if (now - this.lastTransitionEnd < 500) {
        logger.debug(`[TransitionStateManager] Too soon after last transition (${now - this.lastTransitionEnd}ms), ignoring ${type}`);
        return false;
      }

      this.isTransitioning = true;
      this.transitionStartTime = now;
      this.transitionType = type;
      this.pendingUpdates.clear();

      // Set global transition state
      window.chartTransitionState = {
        isTransitioning: true,
        startTime: this.transitionStartTime,
        type: this.transitionType,
        cleanupComplete: false,
        newChartReady: false,
      };

      // Dispatch transition start event
      window.dispatchEvent(
        new CustomEvent("chart-transition-start", {
          detail: {
            type: this.transitionType,
            timestamp: this.transitionStartTime,
          },
        }),
      );

      logger.info(`[TransitionStateManager] Transition started: ${type}`);
      return true;
    },

    endTransition() {
      this.isTransitioning = false;
      const now = Date.now();
      const duration = now - this.transitionStartTime;
      this.lastTransitionEnd = now; // CRITICAL FIX: Track end time

      // Update global transition state
      if (window.chartTransitionState) {
        window.chartTransitionState.isTransitioning = false;
        window.chartTransitionState.endTime = Date.now();
        window.chartTransitionState.duration = duration;
        window.chartTransitionState.newChartReady = true;
      }

      // Dispatch transition end event
      window.dispatchEvent(
        new CustomEvent("chart-transition-end", {
          detail: {
            type: this.transitionType,
            duration: duration,
            timestamp: Date.now(),
          },
        }),
      );

      // Clear transition state after delay
      setTimeout(() => {
        window.chartTransitionState = null;
      }, 1000);

      logger.info(
        `[TransitionStateManager] Transition ended: ${this.transitionType} (${duration}ms)`,
      );
      this.transitionType = null;
    },

    isInTransition() {
      try {
        const localTransition = this.isTransitioning;
        const globalTransition =
          window.chartTransitionState &&
          window.chartTransitionState.isTransitioning;
        const result = localTransition || globalTransition;

        // Debug logging for transition state
        if (result && Math.random() < 0.1) {
          // Log 10% of transition checks to avoid spam
          logger.debug("[TransitionStateManager] isInTransition check:", {
            localTransition,
            globalTransition,
            result,
            transitionType: this.transitionType,
          });
        }

        return result;
      } catch (error) {
        logger.warn("[TransitionStateManager] Error in isInTransition:", error);
        return false; // Safe fallback
      }
    },

    addPendingUpdate(updateId) {
      this.pendingUpdates.add(updateId);
    },

    clearPendingUpdates() {
      this.pendingUpdates.clear();
    },
  };

  // Export transition state manager globally
  window.TransitionStateManager = TransitionStateManager;

  // === BEGIN: Transition State Monitoring ===

  /**
   * Transition state monitoring for debugging
   */
  const TransitionMonitor = {
    lastCheck: Date.now(),
    transitionCount: 0,
    totalTransitionTime: 0,

    logTransition(type, duration) {
      this.transitionCount++;
      this.totalTransitionTime += duration;
      const avgDuration = this.totalTransitionTime / this.transitionCount;

      logger.info(
        `[TransitionMonitor] ${type} completed in ${duration}ms (avg: ${avgDuration.toFixed(0)}ms, total: ${this.transitionCount})`,
      );
    },

    getStats() {
      return {
        totalTransitions: this.transitionCount,
        averageDuration:
          this.transitionCount > 0
            ? this.totalTransitionTime / this.transitionCount
            : 0,
        lastCheck: this.lastCheck,
      };
    },
  };

  // Monitor transition events
  window.addEventListener("chart-transition-end", (event) => {
    const duration = event.detail?.duration || 0;
    const type = event.detail?.type || "unknown";
    TransitionMonitor.logTransition(type, duration);
  });

  // Export monitor for debugging
  window.TransitionMonitor = TransitionMonitor;

  // === END: Transition State Monitoring ===

  // === END: Centralized Transition State Management ===

  // === BEGIN: Missing Core Functions ===

  /**
   * Waits for LightweightCharts library to load with fallback.
   * @returns {Promise<void>}
   */
  const waitForLightweightCharts = () =>
    new Promise((resolve, reject) => {
      let attempts = 0;
      let timeoutId = null;
      const check = () => {
        if (window.LightweightCharts) {
          setTimeout(resolve, 50);
        } else if (attempts++ < 50) {
          timeoutId = setTimeout(check, 100);
        } else {
          logger.error(
            "LightweightCharts failed to load, attempting fallback...",
          );
          const script = document.createElement("script");
          script.src =
            "https://cdn.jsdelivr.net/npm/lightweight-charts@5.0.0/dist/lightweight-charts.standalone.production.min.js";
          script.onload = () => {
            logger.info("Fallback LightweightCharts loaded successfully");
            resolve();
          };
          script.onerror = () =>
            reject(new Error("All attempts to load LightweightCharts failed"));
          document.head.appendChild(script);
        }
      };
      check();
    });

  /**
   * Waits for essential modules to load.
   * @param {number} timeout - Timeout in milliseconds.
   * @returns {Promise<void>}
   */
  function waitForModules(timeout = 10000) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const checkModules = () => {
        // Check if all required modules are available
        const hasCvdModule = !!window.cvdModule;
        const hasPerpCvdModule = !!window.perpCvdModule;
        const hasChartIndicators = !!window.chartIndicators;
        const hasChartInitializer = !!window.chartInitializer;

        if (
          hasCvdModule &&
          hasPerpCvdModule &&
          hasChartIndicators &&
          hasChartInitializer
        ) {
          logger.info(
            "[charts.js] All required modules loaded, proceeding with chart initialization",
          );
          resolve();
        } else if (Date.now() - startTime < timeout) {
          // Log which modules are missing for debugging
          if (!hasCvdModule)
            logger.debug("[charts.js] Waiting for cvdModule...");
          if (!hasPerpCvdModule)
            logger.debug("[charts.js] Waiting for perpCvdModule...");
          if (!hasChartIndicators)
            logger.debug("[charts.js] Waiting for chartIndicators...");
          if (!hasChartInitializer)
            logger.debug("[charts.js] Waiting for chartInitializer...");
          setTimeout(checkModules, 500); // Increased interval for better debugging
        } else {
          logger.warn(
            `[charts.js] Not all modules loaded after ${timeout}ms, proceeding with available modules. Missing:`,
            {
              cvdModule: !hasCvdModule,
              perpCvdModule: !hasPerpCvdModule,
              chartIndicators: !hasChartIndicators,
              chartInitializer: !hasChartInitializer,
            },
          );
          resolve();
        }
      };
      checkModules();
    });
  }

  /**
   * Pre-calculates data for a trading pair.
   * @param {string} pair - Trading pair.
   * @param {HTMLElement} overlay - Overlay element for UI feedback.
   * @param {Function} progressiveUpdate - Progress callback.
   * @param {AbortSignal} abortSignal - Signal to abort fetch.
   * @returns {Promise<Object|null>} Calculated data or null on error.
   */
  async function preCalculateData(
    pair,
    overlay,
    progressiveUpdate,
    abortSignal,
  ) {
    const cacheKey = `${pair}_${window.configManager.CONFIG.barInterval}`;
    if (preCalculateDataCache.has(cacheKey))
      return preCalculateDataCache.get(cacheKey);
    try {
      const timeoutDuration =
        window.configManager?.CONFIG?.ui?.loadingTimeout || 15000;
      const fetchTimeout = new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error(`Timeout fetching data for ${pair}`)),
          timeoutDuration,
        ),
      );
      const bitstampPair = `${pair.toLowerCase()}usd`;
      let progressiveBybitBars = [];
      const [rawPriceData, bybitData] = await Promise.all([
        Promise.race([
          window.fetchBitstampHistoricalData(
            bitstampPair,
            window.configManager.CONFIG.barInterval,
            undefined,
            abortSignal,
          ),
          fetchTimeout,
        ]),
        Promise.race([
          window.fetchBybitHistoricalData(
            pair,
            window.configManager.CONFIG.barInterval,
            window.configManager.CONFIG.maxBars,
            (bars) => {
              progressiveBybitBars = bars;
              if (typeof progressiveUpdate === "function")
                progressiveUpdate(bars);
            },
            abortSignal,
          ),
          fetchTimeout,
        ]),
      ]);
      const priceData = window.configManager.utils.filterValidBars(
        rawPriceData || [],
      );
      const cleanBybitData = window.configManager.utils.filterValidBars(
        bybitData || [],
      );
      if (!priceData.length) return null;

      // Check if chartIndicators is available, if not create a minimal fallback
      let indicatorResults;
      if (window.chartIndicators?.calculateAllIndicators) {
        indicatorResults =
          window.chartIndicators.calculateAllIndicators(priceData);
      } else {
        logger.warn(
          "[charts.js] chartIndicators not available, using fallback indicators",
        );
        indicatorResults = {
          bands: { t1: 0, t2: 0, b1: 0, b2: 0, time: 0 },
          vwap: {
            vwapValue: 0,
            upperBand: 0,
            lowerBand: 0,
            upperMidline: 0,
            lowerMidline: 0,
          },
          vwapData: [],
          emaBands: { ema: 0, upper: 0, lower: 0, time: 0 },
          caches: {},
        };
      }
      const allTimes = [
        ...new Set([
          ...(cleanBybitData.map((d) => d.time) || []),
          ...priceData.map((d) => d.time),
        ]),
      ].sort();
      const bybitMap = new Map(cleanBybitData.map((d) => [d.time, d]));
      const bitstampMap = new Map(priceData.map((d) => [d.time, d]));
      let lastBybitClose = bybitData[0]?.close || 0;
      let lastBitstampClose = priceData[0]?.close || 0;
      const aligned = allTimes.map((time) => {
        const bybit = bybitMap.get(time) || {
          time,
          open: lastBybitClose,
          high: lastBybitClose,
          low: lastBybitClose,
          close: lastBybitClose,
          volume: 0,
        };
        const bitstamp = bitstampMap.get(time) || {
          time,
          open: lastBitstampClose,
          high: lastBitstampClose,
          low: lastBitstampClose,
          close: lastBitstampClose,
          volume: 0,
        };
        lastBybitClose = bybit.close;
        lastBitstampClose = bitstamp.close;
        return { time, bybit, bitstamp };
      });
      const alignedBybit = aligned.map((d) => d.bybit);
      const alignedBitstamp = aligned.map((d) => d.bitstamp);
      // Check if chartIndicators.calculateLiqs is available
      let liqsData, liqsRaw, perpD, spotD;
      if (window.chartIndicators?.calculateLiqs) {
        const liqsResult = window.chartIndicators.calculateLiqs(
          alignedBybit,
          alignedBitstamp,
          window.configManager.CONFIG.sdPeriod,
        );
        liqsData = liqsResult.liqsData;
        liqsRaw = liqsResult.liqsRaw;
        perpD = liqsResult.perpD;
        spotD = liqsResult.spotD;
      } else {
        logger.warn(
          "[charts.js] chartIndicators.calculateLiqs not available, using fallback",
        );
        liqsData = [{ time: Math.floor(Date.now() / 1000), value: 0 }];
        liqsRaw = [];
        perpD = [{ value: 0 }];
        spotD = [{ value: 0 }];
      }
      const openInterestData = alignedBybit.map((b) => ({
        time: b.time,
        price: b.close,
        close: b.close,
        openInterest: b.volume * 10,
        priceChange: (b.close - b.open) / b.open,
        fundingRate: 0,
        buyFlow: b.volume * 0.6,
        sellFlow: b.volume * 0.4,
        hasOrderFlow: true,
      }));
      const result = {
        priceData,
        bands: indicatorResults.bands,
        vwap: indicatorResults.vwap,
        vwapData: indicatorResults.vwapData,
        emaBands: indicatorResults.emaBands,
        caches: indicatorResults.caches,
        liqsData,
        liqsRawWindow: liqsRaw.slice(-window.configManager.CONFIG.sdPeriod),
        sums: {
          perpSum: perpD[perpD.length - 1].value,
          spotSum: spotD[spotD.length - 1].value,
        },
        alignedBybit,
        alignedBitstamp,
        openInterestData,
        timing: {
          firstTime: allTimes[0],
          lastTime: allTimes[allTimes.length - 1],
        },
      };
      preCalculateDataCache.set(cacheKey, result);
      return result;
    } catch (e) {
      logger.error(`[preCalculateData] Error for ${pair}:`, e);
      if (window.errorHandler) {
        window.errorHandler.handleError(e, { source: "Charts", pair });
      }
      return null;
    }
  }

  /**
   * Subscribes to data sources for a trading pair.
   * @param {string} pair - Trading pair to subscribe to.
   */
  function subscribePair(pair) {
    const state = chartStates.get(pair);
    if (state?.isSubscribed) return;
    const lp = pair.toLowerCase();
    logger.info(`[charts.js] Setting up data sources for ${pair}...`);

    // Enhanced WebSocket connection verification with faster reconnection
    const verifyAndConnect = () => {
      // Ensure WebSocket managers are available and connected
      if (!window.bitstampWsManager) {
        logger.warn(
          `[charts.js] Bitstamp WebSocket manager not available - retrying in 500ms`,
        );
        setTimeout(() => subscribePair(pair), 500);
        return;
      }

      // Check connection health and reconnect if needed
      if (!window.bitstampWsManager.isConnected()) {
        logger.info(
          `[charts.js] Bitstamp WebSocket not connected - attempting reconnection for ${pair}`,
        );
        window.bitstampWsManager.reconnect(true);
        setTimeout(() => subscribePair(pair), 1000);
        return;
      }

      // Start chart polling
      window.pollingManager?.startChartPolling?.(pair);

      // CRITICAL FIX: Check for existing subscriptions to prevent duplicates
      const bitstampChannels = [
        `order_book_${lp}usd`,
        `live_trades_${lp}usd`,
      ];

      // Subscribe to Bitstamp channels for live price ticker and order book
      bitstampChannels.forEach((channel) => {
        // Check if already subscribed to prevent duplicates
        if (window.bitstampWsManager?.activeChannels?.has(channel)) {
          logger.debug(`[charts.js] Already subscribed to ${channel}, skipping`);
          return;
        }

        if (window.bitstampWsManager?.subscribe) {
          logger.info(`[charts.js] Subscribing to ${channel} for ${pair}`);
          try {
            window.bitstampWsManager.subscribe(channel, (d) => {
              const s = chartStates.get(pair);
              if (s && s.isActive && !window.TransitionStateManager?.isInTransition()) {
                window.websocketMessageQueue?.add("bitstamp", d);
              }
            }, pair); // Pass pair for tracking
          } catch (error) {
            logger.error(`[charts.js] Error subscribing to ${channel}:`, error);
            // Reduced retry attempts to prevent spam
            setTimeout(() => {
              if (!window.bitstampWsManager?.activeChannels?.has(channel)) {
                try {
                  window.bitstampWsManager.subscribe(channel, (d) => {
                    const s = chartStates.get(pair);
                    if (s && s.isActive && !window.TransitionStateManager?.isInTransition()) {
                      window.websocketMessageQueue?.add("bitstamp", d);
                    }
                  }, pair);
                } catch (retryError) {
                  logger.error(
                    `[charts.js] Retry subscription failed for ${channel}:`,
                    retryError,
                  );
                }
              }
            }, 2000); // Increased retry delay
          }
        } else {
          logger.warn(`[charts.js] WebSocket manager not available for ${channel}`);
        }
      });

      // CRITICAL FIX: Subscribe to Bybit channels with duplicate prevention
      if (window.bybitWsManager) {
        const bybitChannels = [
          `liquidation.${pair.toUpperCase()}USDT`,
          `publicTrade.${pair.toUpperCase()}USDT`,
        ];

        // Use the pair-specific subscription method if available
        if (window.bybitWsManager.subscribeForPair) {
          window.bybitWsManager.subscribeForPair(pair, bybitChannels);
        }

        bybitChannels.forEach((channel) => {
          // Check if already subscribed to prevent duplicates
          if (window.bybitWsManager?.activeChannels?.has(channel)) {
            logger.debug(`[charts.js] Already subscribed to ${channel}, skipping`);
            return;
          }

          logger.info(`[charts.js] Subscribing to ${channel} for ${pair}`);
          try {
            window.bybitWsManager.subscribe(channel, (d) => {
              const s = chartStates.get(pair);
              if (s && s.isActive && !window.TransitionStateManager?.isInTransition()) {
                window.websocketMessageQueue?.add("bybit", d);
              }
            }, pair); // Pass pair for tracking
          } catch (error) {
            logger.error(`[charts.js] Error subscribing to ${channel}:`, error);
            // Reduced retry attempts to prevent spam
            setTimeout(() => {
              if (!window.bybitWsManager?.activeChannels?.has(channel)) {
                try {
                  window.bybitWsManager.subscribe(channel, (d) => {
                    const s = chartStates.get(pair);
                    if (s && s.isActive && !window.TransitionStateManager?.isInTransition()) {
                      window.websocketMessageQueue?.add("bybit", d);
                    }
                  }, pair);
                } catch (retryError) {
                  logger.error(
                    `[charts.js] Retry subscription failed for ${channel}:`,
                    retryError,
                  );
                }
              }
            }, 2000); // Increased retry delay
          }
        });
      } else {
        logger.warn(
          `[charts.js] Bybit WebSocket manager not available for ${pair}`,
        );
      }

      if (state) state.isSubscribed = true;

      logger.info(`[charts.js] Completed setup for ${pair}`);
    };

    verifyAndConnect();
  }

  /**
   * Unsubscribes from data sources for a trading pair.
   * @param {string} pair - Trading pair to unsubscribe from.
   */
  function unsubscribePair(pair) {
    const state = chartStates.get(pair);
    if (!state?.isSubscribed) return;

    logger.info(`[charts.js] Unsubscribing from data sources for ${pair}...`);

    // Unsubscribe from Bitstamp channels
    if (window.bitstampWsManager) {
      const lp = pair.toLowerCase();
      const channels = [`order_book_${lp}usd`, `live_trades_${lp}usd`];

      channels.forEach((channel) => {
        try {
          window.bitstampWsManager.unsubscribe?.(channel);
        } catch (error) {
          logger.warn(
            `[charts.js] Error unsubscribing from ${channel}:`,
            error,
          );
        }
      });
    }

    // Unsubscribe from Bybit channels
    if (window.bybitWsManager) {
      const bybitChannels = [
        `liquidation.${pair.toUpperCase()}USDT`,
        `publicTrade.${pair.toUpperCase()}USDT`,
      ];

      bybitChannels.forEach((channel) => {
        try {
          window.bybitWsManager.unsubscribe?.(channel);
        } catch (error) {
          logger.warn(
            `[charts.js] Error unsubscribing from ${channel}:`,
            error,
          );
        }
      });
    }

    // Stop chart polling
    window.pollingManager?.stopChartPolling?.(pair);

    if (state) state.isSubscribed = false;

    logger.info(`[charts.js] Completed unsubscription for ${pair}`);
  }

  /**
   * Initializes the chart application.
   */
  async function initializeChart() {
    if (isInitializing) return;
    isInitializing = true;

    logger.info("[charts.js] Starting chart initialization...");
    logger.info("[charts.js] DOM cache contents:", {
      container: !!domCache.get("container"),
      overlay: !!domCache.get("overlay"),
      priceChartContainer: !!domCache.get("priceChartContainer"),
      priceChart: !!domCache.get("priceChartElement"),
    });

    try {
      // Wait for modules with a longer timeout to ensure all dependencies are loaded
      await waitForModules(8000);

      let container = domCache.get("container");
      if (!container) {
        // Try to re-initialize DOM cache
        const containerElement = document.querySelector(".chart-container");
        if (containerElement) {
          domCache.set("container", containerElement);
          domCache.set(
            "overlay",
            containerElement.querySelector(".loading-overlay"),
          );
          domCache.set(
            "priceChartContainer",
            containerElement.querySelector(".price-chart-container"),
          );
          domCache.set(
            "buttons",
            Array.from(containerElement.querySelectorAll(".pair-button")),
          );
          domCache.set(
            "pairSelector",
            containerElement.querySelector(".pair-selector"),
          );
          domCache.set(
            "liqControls",
            containerElement.querySelector(".liq-controls"),
          );
          domCache.set(
            "liquidationConsole",
            document.getElementById("liquidation-console"),
          );
          logger.info("[charts.js] Re-initialized DOM cache");
          container = containerElement;
        } else {
          throw new Error("Container not found and cannot be re-initialized");
        }
      }

      const overlay = domCache.get("overlay");
      if (overlay) {
        overlay.style.display = "block";
      }

      // Smart chart loading logic:
      // - Page refresh (F5/Ctrl+R): Always load BTC
      // - Tab focus/visibility restore: Load last opened chart
      const isPageRefresh =
        performance.navigation?.type === 1 ||
        performance.getEntriesByType?.("navigation")?.[0]?.type === "reload" ||
        !sessionStorage.getItem("chartSessionActive");

      // Mark session as active
      sessionStorage.setItem("chartSessionActive", "true");

      const lastChartPair = isPageRefresh
        ? "BTC" // Always BTC for page refresh
        : localStorage.getItem("lastChartPair") || "BTC"; // Last chart for tab focus

      // Debug information
      console.log("[charts.js] Chart loading decision:", {
        isPageRefresh,
        navigationType: performance.navigation?.type,
        navigationEntry:
          performance.getEntriesByType?.("navigation")?.[0]?.type,
        sessionActive: sessionStorage.getItem("chartSessionActive"),
        selectedPair: lastChartPair,
        lastSavedPair: localStorage.getItem("lastChartPair"),
      });

      logger.info(
        `[charts.js] Loading chart for ${isPageRefresh ? "page refresh" : "tab restore"}: ${lastChartPair}...`,
      );

      // Add timeout for data fetching
      const dataFetchPromise = preCalculateData(lastChartPair, overlay);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Data fetch timeout")), 15000),
      );

      const data = await Promise.race([dataFetchPromise, timeoutPromise]);
      if (!data) throw new Error(`No data for ${lastChartPair}`);

      logger.info("[charts.js] Initializing chart with data...");
      logger.info("[charts.js] Data summary:", {
        priceDataLength: data?.priceData?.length || 0,
        hasBands: !!data?.bands,
        hasVwap: !!data?.vwap,
        hasLiqsData: !!data?.liqsData?.length,
      });

      // Check if chartInitializer is available
      if (!window.chartInitializer?.initializeChartAndMeter) {
        throw new Error(
          "chartInitializer.initializeChartAndMeter not available",
        );
      }

      // Add timeout for chart initialization
      const chartInitPromise = new Promise((resolve, reject) => {
        try {
          const state = window.chartInitializer.initializeChartAndMeter(
            container,
            data,
            lastChartPair,
          );
          if (!state) {
            reject(new Error("Chart initialization returned null state"));
          } else {
            resolve(state);
          }
        } catch (error) {
          reject(error);
        }
      });

      const chartTimeoutPromise = new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error("Chart initialization timeout")),
          10000,
        ),
      );

      const state = await Promise.race([chartInitPromise, chartTimeoutPromise]);

      logger.info("[charts.js] Chart state created:", {
        hasState: !!state,
        isActive: state?.isActive,
        hasChart: !!state?.chart,
        hasPriceSeries: !!state?.chart?.priceSeries,
      });

      chartStates.set(lastChartPair, state);
      container.dataset.pair = lastChartPair;

      logger.info(`[charts.js] Subscribing to ${lastChartPair} data...`);
      subscribePair(lastChartPair);

      logger.info("[charts.js] Chart initialization completed successfully");
    } catch (e) {
      logger.error("[charts.js] Error during chart initialization:", e);
      if (window.errorHandler) {
        window.errorHandler.handleError(e, {
          source: "Charts",
          pair: lastChartPair,
        });
      }
    } finally {
      isInitializing = false;
    }
  }

  // Export missing functions
  window.waitForLightweightCharts = waitForLightweightCharts;
  window.initializeChart = initializeChart;
  window.preCalculateData = preCalculateData;
  window.subscribePair = subscribePair;
  window.unsubscribePair = unsubscribePair;

  // === BEGIN: Missing Event Handlers ===

  /**
   * Unified visibility event handler to prevent racing conditions
   */
  function handleUnifiedVisibilityChange() {
    // Skip if transition is in progress
    if (
      window.TransitionStateManager &&
      window.TransitionStateManager.isInTransition()
    ) {
      logger.debug(
        "[charts.js] Skipping visibility change during chart transition",
      );
      return;
    }

    const now = Date.now();
    const isVisible = document.visibilityState === "visible";

    // Debounce visibility changes
    if (
      window.lastVisibilityChange &&
      now - window.lastVisibilityChange < 2000
    ) {
      return;
    }
    window.lastVisibilityChange = now;

    if (isVisible) {
      logger.info(
        "[charts.js] Page became visible, triggering unified visibility restore",
      );

      // Dispatch unified visibility restore event
      window.dispatchEvent(
        new CustomEvent("visibility-restore", {
          detail: { timestamp: now },
        }),
      );

      // Trigger orderbook refresh with delay
      setTimeout(() => {
        if (
          window.OrderbookManager &&
          !window.TransitionStateManager.isInTransition()
        ) {
          window.OrderbookManager.handleVisibilityChange();
        }
      }, 1000);

      // Re-initialize the Delta OI Profile
      const state = chartStates.get(currentPair);
      if (state && window.deltaOiProfileManager) {
        window.deltaOiProfileManager.initializeProfiles(state);
      }
    } else {
      logger.info("[charts.js] Page became hidden");
      // Save the current pair to localStorage immediately when the tab becomes hidden
      if (window.currentPair) {
        localStorage.setItem("lastChartPair", window.currentPair);
      }
      window.dispatchEvent(
        new CustomEvent("visibility-hide", {
          detail: { timestamp: now },
        }),
      );
    }
  }

  /**
   * Handles memory pressure events
   */
  function handleMemoryPressure() {
    logger.info("[charts.js] Memory pressure detected, cleaning up...");
    window.memoryManager?.cleanupHistoricalData?.(chartStates.get(currentPair));
  }

  /**
   * Handles window resize events
   */
  function handleResize() {
    window.updateSize?.();
  }

  // Add event listeners for these handlers
  window.addEventListener("memory-pressure", handleMemoryPressure);
  window.addEventListener("resize", handleResize);

  // CRITICAL FIX: Add unified visibility event listener
  window.addEventListener("visibilitychange", handleUnifiedVisibilityChange);

  // === END: Missing Event Handlers ===

  // === END: Missing Core Functions ===

  const container = document.querySelector(".chart-container");
  if (container) {
    domCache.set("container", container);
    domCache.set("overlay", container.querySelector(".loading-overlay"));
    domCache.set(
      "priceChartContainer",
      container.querySelector(".price-chart-container"),
    );
    domCache.set(
      "buttons",
      Array.from(container.querySelectorAll(".pair-button")),
    );
    domCache.set("pairSelector", container.querySelector(".pair-selector"));
    domCache.set("liqControls", container.querySelector(".liq-controls"));
    domCache.set(
      "liquidationConsole",
      document.getElementById("liquidation-console"),
    );

    logger.info("[charts.js] DOM cache initialized successfully");
  } else {
    logger.warn("[charts.js] Chart container not found during initialization");
  }

  if (!window.eventBus) {
    window.eventBus = {
      events: {},
      subscribe: (e, cb) => {
        window.eventBus.events[e] = window.eventBus.events[e] || [];
        window.eventBus.events[e].push(cb);
        return () =>
          (window.eventBus.events[e] = window.eventBus.events[e].filter(
            (c) => c !== cb,
          ));
      },
      publish: (e, d) =>
        window.eventBus.events[e]?.forEach((cb) => {
          try {
            cb(d);
          } catch (err) {
            logger.error(`Error in ${e}:`, err);
          }
        }),
    };
    window.configManager?.PAIRS?.forEach((p) => {
      const liqHandler = (e) =>
        e.detail && window.eventBus.publish(`liquidation-${p}`, e.detail);
      const whaleHandler = (e) =>
        e.detail && window.eventBus.publish(`whale-alert-${p}`, e.detail);
      window.addEventListener(`liquidation-${p}`, liqHandler);
      window.addEventListener(`whale-alert-${p}`, whaleHandler);
    });
  }

  // Historical data fetching moved to modules/charts/chartutilities/dataFetcher.js
  // Use window.dataFetcher functions

  // Chart initialization moved to modules/charts/chartutilities/chartInitializer.js
  // Use window.chartInitializer.initializeChartAndMeter instead

  // WebSocket message handling moved to modules/charts/chartutilities/websocketMessageHandler.js
  // Use window.websocketMessageHandler functions

  // Pair switching moved to modules/charts/chartutilities/pairSwitcher.js
  // Use window.pairSwitcher.switchPairInternal

  // Settings management moved to modules/charts/chartutilities/settingsManager.js
  // Use window.settingsManager functions

  // Memory management moved to modules/charts/chartutilities/memoryManager.js
  // Use window.memoryManager.cleanupHistoricalData

  // Event handlers moved to modules/charts/chartutilities/eventHandlers.js
  // Initialize event handlers
  window.eventHandlers?.initEventHandlers();

  // === BEGIN: Comprehensive Orderbook Manager ===
  const OrderbookManager = {
    // State tracking
    isTabVisible: !document.hidden,
    lastVisibilityChange: Date.now(),
    pendingRefresh: false,
    refreshInProgress: false,
    lastRefreshTime: 0,
    MIN_REFRESH_INTERVAL: 2000,

    // CRITICAL FIX: Add transition state awareness
    isTransitioning() {
      if (window.TransitionStateManager) {
        return window.TransitionStateManager.isInTransition();
      }
      return (
        window.chartTransitionState &&
        window.chartTransitionState.isTransitioning
      );
    },

    // State validation
    isStateValid(state) {
      return (
        state &&
        state.isActive &&
        !state.isDisposed &&
        state.chart &&
        state.chart.priceSeries &&
        !state.chart.priceSeries._internal_isDisposed &&
        !this.isTransitioning()
      ); // CRITICAL FIX: Check transition state
    },

    // Line management
    clearOrderBookLines(state) {
      if (!this.isStateValid(state)) return;

      try {
        state.currentLines = Array.isArray(state.currentLines)
          ? state.currentLines
          : [];
        const lines = state.currentLines.filter(
          (l) => l && typeof l === "object",
        );
        state.currentLines = [];

        lines.forEach((line) => {
          try {
            if (
              line &&
              state.chart.priceSeries &&
              !state.chart.priceSeries._internal_isDisposed
            ) {
              state.chart.priceSeries.removePriceLine(line);
            }
          } catch (e) {
            logger.warn("[OrderbookManager] Error removing line:", e);
          }
        });
      } catch (e) {
        state.currentLines = [];
      }
    },

    // Update orderbook lines
    updateOrderBookLines(state) {
      // CRITICAL FIX: Check transition state before updating
      if (this.isTransitioning()) {
        logger.debug(
          "[OrderbookManager] Skipping orderbook update during chart transition",
        );
        return;
      }

      if (!this.isStateValid(state)) return;

      try {
        const { bids, asks } = state.data.orderBook || { bids: [], asks: [] };
        if (!bids.length || !asks.length) return;

        // Check if data has changed
        const hash = this.calculateOrderbookHash(bids, asks);
        if (hash === state._lastOrderBookHash) return;

        state._lastOrderBookHash = hash;
        this.clearOrderBookLines(state);
        this.renderOrderbookLines(state, bids, asks);
      } catch (e) {
        logger.error("[OrderbookManager] Error updating orderbook lines:", e);
      }
    },

    // Render orderbook lines
    renderOrderbookLines(state, bids, asks) {
      const lastPrice =
        state.data.priceData?.[state.data.priceData.length - 1]?.close || 0;
      if (!lastPrice) return;

      const pb = bids.map(([p, v]) => ({
        price: p,
        volume: v,
        dollarValue: p * v,
        type: "bid",
      }));
      const pa = asks.map(([p, v]) => ({
        price: p,
        volume: v,
        dollarValue: p * v,
        type: "ask",
      }));

      const allOrders = [...pb, ...pa]
        .sort((a, b) => b.dollarValue - a.dollarValue)
        .slice(0, 20);

      const maxValue = allOrders[0]?.dollarValue || 1;
      let bidShown = false,
        askShown = false;

      allOrders.forEach((order, i) => {
        try {
          const { price, volume, dollarValue, type } = order;
          const relSize = Math.max(0.5, Math.min(1, dollarValue / maxValue));
          const lineWidth = Math.max(1, Math.round(relSize * 3));
          const fmtValue =
            dollarValue >= 1e6
              ? `$${Math.floor(dollarValue / 1e6)}M`
              : `$${Math.floor(dollarValue / 1e3)}K`;

          const showLabel =
            (type === "bid" && !bidShown) || (type === "ask" && !askShown);
          if (type === "bid" && showLabel) bidShown = true;
          if (type === "ask" && showLabel) askShown = true;

          const baseOpacity = showLabel ? 0.45 : 0.25;
          const opacityDec = showLabel ? 0.02 : 0.03;

          const line = state.chart.priceSeries.createPriceLine({
            price,
            color:
              type === "bid"
                ? `rgba(0, 255, 255, ${baseOpacity - i * opacityDec})`
                : `rgba(255, 85, 85, ${baseOpacity - i * opacityDec})`,
            lineWidth,
            lineStyle: 0,
            axisLabelVisible: showLabel,
            title: showLabel ? `${type.toUpperCase()} ${fmtValue}` : "",
          });

          if (line && state.currentLines) {
            state.currentLines.push(line);
          }
        } catch (e) {
          logger.warn("[OrderbookManager] Error creating line:", e);
        }
      });
    },

    // Hash calculation
    calculateOrderbookHash(bids, asks) {
      const tb = bids.slice(0, 10);
      const ta = asks.slice(0, 10);
      return `${tb.map((b) => `${b[0]}:${b[1]}`).join("|")}#${ta.map((a) => `${a[0]}:${a[1]}`).join("|")}`;
    },

    // CRITICAL FIX: Enhanced visibility handling with transition awareness
    handleVisibilityChange() {
      // Skip if transition is in progress
      if (this.isTransitioning()) {
        logger.debug(
          "[OrderbookManager] Skipping visibility change during chart transition",
        );
        return;
      }

      const now = Date.now();
      if (now - this.lastRefreshTime < this.MIN_REFRESH_INTERVAL) return;

      this.lastRefreshTime = now;

      // Add delay to ensure chart state is stable
      setTimeout(() => {
        if (this.isTransitioning()) return;

        window.chartStates?.forEach((state) => {
          if (state && state.isActive && !state.isDisposed) {
            this.updateOrderBookLines(state);
          }
        });
      }, 500); // 500ms delay for stability
    },

    // Initialize event listeners
    init() {
      // CRITICAL FIX: Debounced visibility event handlers
      let visibilityTimeout;
      const debouncedVisibilityHandler = () => {
        clearTimeout(visibilityTimeout);
        visibilityTimeout = setTimeout(() => {
          if (!this.isTransitioning()) {
            this.handleVisibilityChange();
          }
        }, 1000); // 1 second debounce
      };

      window.addEventListener("visibility-restore", debouncedVisibilityHandler);
      window.addEventListener(
        "websocket-visibility-restored-bitstamp",
        debouncedVisibilityHandler,
      );

      // CRITICAL FIX: Listen for chart transition events
      window.addEventListener("chart-transition-start", () => {
        logger.debug(
          "[OrderbookManager] Chart transition started, pausing updates",
        );
      });

      window.addEventListener("chart-transition-end", () => {
        logger.debug(
          "[OrderbookManager] Chart transition ended, resuming updates",
        );
        // Small delay to ensure chart is fully ready
        setTimeout(() => {
          if (!this.isTransitioning()) {
            this.handleVisibilityChange();
          }
        }, 200);
      });
    },

    // Cleanup function for chart disposal
    cleanup(state) {
      if (state && state.currentLines) {
        this.clearOrderBookLines(state);
      }
    },
  };

  // Initialize orderbook manager
  OrderbookManager.init();

  // Export OrderbookManager globally for other modules to use
  window.OrderbookManager = OrderbookManager;

  // Create window.chartOrderbook interface
  window.chartOrderbook = {
    updateOrderBookLines: (state) =>
      OrderbookManager.updateOrderBookLines(state),
    clearOrderBookLines: (state) => OrderbookManager.clearOrderBookLines(state),
    updateLastPrice: (state, price) => {
      if (!state?.isActive || !state.data?.priceData?.length) return;
      const lastBar = state.data.priceData[state.data.priceData.length - 1];
      if (lastBar) lastBar.close = price;
      OrderbookManager.updateOrderBookLines(state);
    },
  };
  // === END: Comprehensive Orderbook Manager ===

  // Real-time update tracking for debugging
  let realtimeUpdateCount = 0;
  let lastRealtimeLogTime = Date.now();

  /**
   * Directly updates price data for a chart state with accurate timestamp handling.
   *
   * Key improvements for real-time candle rendering:
   * 1. Uses WebSocket message timestamp instead of system time for accurate bar placement
   * 2. Properly manages bar transitions (current bar updates vs new bar creation)
   * 3. Leverages Lightweight Charts' efficient update mechanism without throttling
   * 4. Maintains data consistency between WebSocket and polling updates
   * 5. Optimized for faster reconnection and more responsive updates
   *
   * @param {Object} state - Chart state to update.
   * @param {number} price - Price value.
   * @param {number} volume - Volume value.
   * @param {number} type - Trade type (0 for buy, 1 for sell).
   * @param {number} timestamp - Trade timestamp from WebSocket message.
   */
  function directUpdatePriceData(state, price, volume, type, timestamp) {
    // CRITICAL FIX: Check transition state before updating
    if (
      window.TransitionStateManager &&
      window.TransitionStateManager.isInTransition()
    ) {
      logger.debug("[charts.js] Skipping price update during chart transition");
      return;
    }

    if (!state?.chart?.priceSeries) return;

    // Optimized timestamp handling - reduce validation overhead
    const currentTime = Math.floor(Date.now() / 1000);
    const timeDiff = currentTime - timestamp;

    // Skip old data more efficiently (reduced from 300s to 60s for faster response)
    if (timeDiff > 60) {
      if (Math.random() < 0.001) {
        // Reduced logging frequency
        logger.debug(`[charts.js] Skipping old data: ${timeDiff}s old`);
      }
      return;
    }

    const barInterval = state.config.barInterval;
    const barTime = Math.floor(timestamp / barInterval) * barInterval;
    let currentBar = state.currentBars.currentBarBitstamp;

    // Optimized bar handling - reduce object creation overhead
    if (!currentBar || barTime > currentBar.time) {
      // Create new bar efficiently
      currentBar = {
        time: barTime,
        open: price,
        high: price,
        low: price,
        close: price,
        volume: volume,
      };
      state.currentBars.currentBarBitstamp = currentBar;
      state.data.priceData.push(currentBar);

      // Immediate chart update without additional checks
      state.chart.priceSeries.update(currentBar);
    } else if (barTime === currentBar.time) {
      // Update existing bar efficiently
      currentBar.close = price;
      if (price > currentBar.high) currentBar.high = price;
      if (price < currentBar.low) currentBar.low = price;
      currentBar.volume += volume;

      // Immediate chart update
      state.chart.priceSeries.update(currentBar);
    }
    // Skip late trades silently to avoid processing overhead

    // Optimized metrics update - only update if needed
    if (type !== undefined && state.metrics) {
      const isBuy = type === 0;
      const tradeValue = price * volume;
      if (isBuy) {
        state.metrics.buyVolume += volume;
        state.metrics.buyValue += tradeValue;
      } else {
        state.metrics.sellVolume += volume;
        state.metrics.sellValue += tradeValue;
      }
    }

    // Reduced debug logging frequency
    realtimeUpdateCount++;
    if (realtimeUpdateCount % 1000 === 0) {
      // Increased from 500 to 1000
      const now = Date.now();
      const timeDiff = now - lastRealtimeLogTime;
      const updatesPerSecond = (1000 * 1000) / timeDiff;
      logger.info(
        `[charts.js] Real-time updates: ${updatesPerSecond.toFixed(1)} updates/sec (${realtimeUpdateCount} total)`,
      );
      lastRealtimeLogTime = now;
    }
  }

  // Export directUpdatePriceData globally for websocketMessageHandler to use
  window.directUpdatePriceData = directUpdatePriceData;

  /**
   * Updates chart sizes when window or container resizes
   */
  function updateSize() {
    chartStates.forEach((state) => {
      if (
        state?.chart?.priceChart &&
        !state.chart.priceChart._internal_isDisposed
      ) {
        try {
          state.chart.priceChart.resize();
        } catch (e) {
          logger.debug("Error resizing price chart:", e);
        }
      }
    });
  }

  // Export updateSize globally
  window.updateSize = updateSize;

  /**
   * Test function to verify real-time update performance
   * Can be called from browser console: window.testRealtimeUpdates()
   */
  window.testRealtimeUpdates = () => {
    const state = chartStates.get(currentPair);
    if (!state) {
      logger.info("No active chart state found");
      return;
    }

    logger.info("=== Real-time Update Test ===");
    logger.info(`Current pair: ${currentPair}`);
    logger.info(`Total real-time updates: ${realtimeUpdateCount}`);
    logger.info(`Chart state active: ${state.isActive}`);
    logger.info(
      `Price series disposed: ${state.chart?.priceSeries?._internal_isDisposed || "N/A"}`,
    );
    logger.info(
      `Current bar time: ${state.currentBars?.currentBarBitstamp?.time || "N/A"}`,
    );
    logger.info(`Price data length: ${state.data?.priceData?.length || 0}`);
    logger.info(
      `Last price: ${state.data?.priceData?.[state.data.priceData.length - 1]?.close || "N/A"}`,
    );
    logger.info("============================");
  };

  /**
   * Emergency chart recovery function
   * Can be called from browser console: window.emergencyChartRecovery()
   */
  window.emergencyChartRecovery = async () => {
    logger.info("[charts.js] Starting emergency chart recovery...");

    try {
      // Reset initialization state
      isInitializing = false;

      // Clear existing chart states
      chartStates.forEach((state) => {
        if (
          state?.chart?.priceChart &&
          !state.chart.priceChart._internal_isDisposed
        ) {
          try {
            state.chart.priceChart.remove();
          } catch (e) {
            logger.debug("Error removing chart during recovery:", e);
          }
        }
        state.isActive = false;
        state.isDisposed = true;
      });
      chartStates.clear();

      // Clear DOM cache
      domCache.clear();

      // Re-initialize DOM cache
      const container = document.querySelector(".chart-container");
      if (container) {
        domCache.set("container", container);
        domCache.set("overlay", container.querySelector(".loading-overlay"));
        domCache.set(
          "priceChartContainer",
          container.querySelector(".price-chart-container"),
        );
        domCache.set(
          "buttons",
          Array.from(container.querySelectorAll(".pair-button")),
        );
        domCache.set("pairSelector", container.querySelector(".pair-selector"));
        domCache.set("liqControls", container.querySelector(".liq-controls"));
        domCache.set(
          "liquidationConsole",
          document.getElementById("liquidation-console"),
        );
      }

      // Wait a moment for cleanup
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Re-initialize chart
      if (window.initializeChart) {
        await window.initializeChart();
        logger.info("[charts.js] Emergency recovery completed successfully");
      } else {
        logger.error(
          "[charts.js] initializeChart function not available for recovery",
        );
      }
    } catch (e) {
      logger.error("[charts.js] Emergency recovery failed:", e);
    }
  };

  /**
   * Diagnostic function to check module loading status
   * Can be called from browser console: window.diagnoseChartLoading()
   */
  window.diagnoseChartLoading = () => {
    logger.info("=== Chart Loading Diagnosis ===");

    // Check core modules
    const modules = {
      LightweightCharts: !!window.LightweightCharts,
      configManager: !!window.configManager,
      domCache: !!window.domCache,
      chartInitializer: !!window.chartInitializer,
      cvdModule: !!window.cvdModule,
      perpCvdModule: !!window.perpCvdModule,
      chartIndicators: !!window.chartIndicators,
      dataFetcher: !!window.dataFetcher,
      pairSwitcher: !!window.pairSwitcher,
      settingsManager: !!window.settingsManager,
      memoryManager: !!window.memoryManager,
      performanceOptimizer: !!window.performanceOptimizer,
      pollingManager: !!window.pollingManager,
      websocketMessageHandler: !!window.websocketMessageHandler,
      eventHandlers: !!window.eventHandlers,
    };

    logger.info("Module Status:");
    Object.entries(modules).forEach(([name, loaded]) => {
      logger.info(`  ${name}: ${loaded ? "✓" : "✗"}`);
    });

    // Check DOM elements
    const elements = {
      container: !!document.querySelector(".chart-container"),
      overlay: !!document.querySelector(".loading-overlay"),
      priceChartContainer: !!document.querySelector(".price-chart-container"),
      priceChart: !!document.querySelector(".price-chart"),
    };

    logger.info("DOM Elements:");
    Object.entries(elements).forEach(([name, exists]) => {
      logger.info(`  ${name}: ${exists ? "✓" : "✗"}`);
    });

    // Check chart states
    logger.info("Chart States:");
    logger.info(`  Total states: ${chartStates.size}`);
    logger.info(`  Current pair: ${currentPair}`);
    logger.info(`  Is initializing: ${isInitializing}`);

    // Check WebSocket managers
    const wsManagers = {
      bitstampWsManager: !!window.bitstampWsManager,
      bybitWsManager: !!window.bybitWsManager,
    };

    logger.info("WebSocket Managers:");
    Object.entries(wsManagers).forEach(([name, exists]) => {
      logger.info(`  ${name}: ${exists ? "✓" : "✗"}`);
    });

    logger.info("============================");
  };

  /**
   * Enhanced diagnostic function to check DOM structure
   * Can be called from browser console: window.diagnoseDOMStructure()
   */
  window.diagnoseDOMStructure = () => {
    logger.info("=== DOM Structure Diagnosis ===");

    const chartContainer = document.querySelector(".chart-container");
    if (chartContainer) {
      logger.info("Chart Container:", {
        exists: true,
        children: chartContainer.children.length,
        className: chartContainer.className,
        style: chartContainer.style.cssText,
      });

      const priceChartElement = chartContainer.querySelector(".price-chart");
      if (priceChartElement) {
        logger.info("Price Chart Element:", {
          exists: true,
          dimensions: {
            width: priceChartElement.offsetWidth,
            height: priceChartElement.offsetHeight,
          },
          style: {
            display: priceChartElement.style.display,
            visibility: priceChartElement.style.visibility,
            opacity: priceChartElement.style.opacity,
          },
        });
      } else {
        logger.info("Price Chart Element: NOT FOUND");
      }

      const loadingOverlay = chartContainer.querySelector(".loading-overlay");
      if (loadingOverlay) {
        logger.info("Loading Overlay:", {
          exists: true,
          display: loadingOverlay.style.display,
          textContent: loadingOverlay.textContent,
        });
      } else {
        logger.info("Loading Overlay: NOT FOUND");
      }
    } else {
      logger.info("Chart Container: NOT FOUND");
    }

    logger.info("============================");
  };

  /**
   * Force hide loading overlay
   * Can be called from browser console: window.forceHideOverlay()
   */
  window.forceHideOverlay = () => {
    // Hide all loading overlays
    const overlays = document.querySelectorAll(".loading-overlay");
    overlays.forEach((overlay, index) => {
      overlay.style.display = "none";
      overlay.style.visibility = "hidden";
      overlay.style.opacity = "0";
      logger.info(`[charts.js] Loading overlay ${index + 1} force hidden`);
    });

    // Specifically target the pair loading overlay
    const pairLoadingOverlay = document.getElementById("pair-loading-overlay");
    if (pairLoadingOverlay) {
      pairLoadingOverlay.style.display = "none";
      pairLoadingOverlay.style.visibility = "hidden";
      pairLoadingOverlay.style.opacity = "0";
      logger.info("[charts.js] Pair loading overlay force hidden");
    } else {
      logger.info("[charts.js] Pair loading overlay not found");
    }
  };

  /**
   * Comprehensive diagnostic function for transition and racing condition issues
   * Can be called from browser console: window.diagnoseTransitionIssues()
   */
  window.diagnoseTransitionIssues = () => {
    logger.info("=== Transition and Racing Condition Diagnosis ===");

    // Check transition state
    const transitionState = {
      TransitionStateManager: !!window.TransitionStateManager,
      isInTransition: window.TransitionStateManager
        ? window.TransitionStateManager.isInTransition()
        : "N/A",
      chartTransitionState: !!window.chartTransitionState,
      transitionType: window.TransitionStateManager
        ? window.TransitionStateManager.transitionType
        : "N/A",
      transitionDuration: window.chartTransitionState
        ? Date.now() - window.chartTransitionState.startTime
        : "N/A",
    };

    logger.info("Transition State:");
    Object.entries(transitionState).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Check chart states
    logger.info("Chart States:");
    if (window.chartStates) {
      Array.from(window.chartStates.entries()).forEach(([pair, state]) => {
        logger.info(`  ${pair}:`, {
          isActive: state?.isActive,
          isDisposed: state?.isDisposed,
          hasChart: !!state?.chart,
          hasPriceSeries: !!state?.chart?.priceSeries,
          priceSeriesDisposed: state?.chart?.priceSeries?._internal_isDisposed,
        });
      });
    }

    // Check WebSocket managers
    const wsManagers = {
      bitstampWsManager: !!window.bitstampWsManager,
      bitstampConnected: window.bitstampWsManager?.isConnected(),
      bybitWsManager: !!window.bybitWsManager,
      bybitConnected: window.bybitWsManager?.isConnected(),
    };

    logger.info("WebSocket Managers:");
    Object.entries(wsManagers).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Check polling state
    const pollingState = {
      pollingManager: !!window.pollingManager,
      chartPollingInterval: !!window.chartPollingInterval,
    };

    logger.info("Polling State:");
    Object.entries(pollingState).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Check orderbook state
    const orderbookState = {
      OrderbookManager: !!window.OrderbookManager,
      currentPair: window.currentPair,
      currentActivePair: window.currentActivePair,
    };

    logger.info("Orderbook State:");
    Object.entries(orderbookState).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Check visibility state
    const visibilityState = {
      "document.hidden": document.hidden,
      "document.visibilityState": document.visibilityState,
      lastVisibilityChange: window.lastVisibilityChange
        ? Date.now() - window.lastVisibilityChange
        : "N/A",
    };

    logger.info("Visibility State:");
    Object.entries(visibilityState).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    logger.info("============================");
  };

  /**
   * Comprehensive stability check for racing condition fixes
   * Can be called from browser console: window.checkStability()
   */
  window.checkStability = () => {
    logger.info("=== Stability Check for Racing Condition Fixes ===");

    // Check transition state management
    const transitionChecks = {
      "TransitionStateManager exists": !!window.TransitionStateManager,
      "isInTransition method works":
        typeof window.TransitionStateManager?.isInTransition === "function",
      "Currently in transition":
        window.TransitionStateManager?.isInTransition() || false,
      "TransitionMonitor exists": !!window.TransitionMonitor,
      "Transition stats": window.TransitionMonitor?.getStats(),
    };

    logger.info("Transition Management:");
    Object.entries(transitionChecks).forEach(([key, value]) => {
      logger.info(`  ${key}: ${JSON.stringify(value)}`);
    });

    // Check WebSocket stability
    const wsChecks = {
      "bitstampWsManager exists": !!window.bitstampWsManager,
      "bybitWsManager exists": !!window.bybitWsManager,
      "bitstamp connected": window.bitstampWsManager?.isConnected() || false,
      "bybit connected": window.bybitWsManager?.isConnected() || false,
    };

    logger.info("WebSocket Stability:");
    Object.entries(wsChecks).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Check orderbook management
    const orderbookChecks = {
      "OrderbookManager exists": !!window.OrderbookManager,
      "isTransitioning method works":
        typeof window.OrderbookManager?.isTransitioning === "function",
      "Currently in transition (orderbook)":
        window.OrderbookManager?.isTransitioning() || false,
    };

    logger.info("Orderbook Management:");
    Object.entries(orderbookChecks).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Check polling management
    const pollingChecks = {
      "pollingManager exists": !!window.pollingManager,
      "chartPollingInterval exists": !!window.chartPollingInterval,
    };

    logger.info("Polling Management:");
    Object.entries(pollingChecks).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Check chart states
    const chartChecks = {
      "chartStates exists": !!window.chartStates,
      currentPair: window.currentPair,
      currentActivePair: window.currentActivePair,
      "active chart states": window.chartStates
        ? Array.from(window.chartStates.entries()).filter(
            ([_, state]) => state?.isActive,
          ).length
        : 0,
    };

    logger.info("Chart States:");
    Object.entries(chartChecks).forEach(([key, value]) => {
      logger.info(`  ${key}: ${value}`);
    });

    // Overall stability assessment
    const allChecks = [
      ...Object.values(transitionChecks).slice(0, 3), // First 3 transition checks
      ...Object.values(wsChecks).slice(0, 2), // First 2 WS checks
      ...Object.values(orderbookChecks).slice(0, 2), // First 2 orderbook checks
      ...Object.values(pollingChecks), // All polling checks
      ...Object.values(chartChecks).slice(0, 2), // First 2 chart checks
    ];

    const stabilityScore = allChecks.filter((check) =>
      check === true || check === false ? check : true,
    ).length;
    const totalChecks = allChecks.length;
    const stabilityPercentage = (stabilityScore / totalChecks) * 100;

    logger.info(
      `\nStability Assessment: ${stabilityScore}/${totalChecks} (${stabilityPercentage.toFixed(1)}%)`,
    );

    if (stabilityPercentage >= 90) {
      logger.info(
        "✅ System is stable - racing condition fixes are working properly",
      );
    } else if (stabilityPercentage >= 70) {
      logger.warn(
        "⚠️ System is mostly stable - some components may need attention",
      );
    } else {
      logger.error(
        "❌ System has stability issues - racing conditions may still occur",
      );
    }

    logger.info("============================");
  };

  /**
   * Enhanced WebSocket stability management during transitions
   */
  function stabilizeWebSocketsDuringTransition() {
    if (
      !window.TransitionStateManager ||
      !window.TransitionStateManager.isInTransition()
    ) {
      return;
    }

    logger.info("[charts.js] Stabilizing WebSockets during transition...");

    // Pause WebSocket message processing during transitions
    if (window.bitstampWsManager) {
      window.bitstampWsManager.pauseMessageProcessing?.();
    }
    if (window.bybitWsManager) {
      window.bybitWsManager.pauseMessageProcessing?.();
    }

    // Resume after transition ends
    const resumeWebSockets = () => {
      if (window.bitstampWsManager) {
        window.bitstampWsManager.resumeMessageProcessing?.();
      }
      if (window.bybitWsManager) {
        window.bybitWsManager.resumeMessageProcessing?.();
      }
      logger.info("[charts.js] WebSocket processing resumed after transition");
    };

    // Listen for transition end
    const transitionEndHandler = () => {
      setTimeout(resumeWebSockets, 500); // Small delay to ensure chart is ready
      window.removeEventListener("chart-transition-end", transitionEndHandler);
    };

    window.addEventListener("chart-transition-end", transitionEndHandler);
  }

  // Call stabilization when transition starts
  window.addEventListener(
    "chart-transition-start",
    stabilizeWebSocketsDuringTransition,
  );

  /**
   * Cleans up resources before page unload.
   */
  function cleanup() {
    // Stop chart polling
    window.pollingManager?.stopChartPolling();

    // Clean up WebSocket connections
    if (window.bitstampWsManager) {
      window.bitstampWsManager.close();
    }
    if (window.bybitWsManager) {
      window.bybitWsManager.close();
    }
    if (window.cleanupOrderbookWebSocket) {
      window.cleanupOrderbookWebSocket();
    }

    window.removeEventListener("memory-pressure", handleMemoryPressure);
    window.removeEventListener("resize", handleResize);
    chartStates.forEach((state) => {
      if (!state) return;

      // Clean up orderbook lines
      OrderbookManager.cleanup(state);

      ["liquidationManager", "whaleAlertManager"].forEach((manager) => {
        if (state[manager]?.destroy) {
          try {
            state[manager].destroy();
          } catch (e) {
            logger.debug(`Error destroying ${manager}:`, e);
          }
          state[manager] = null;
        }
      });
      if (state.chart) {
        const { priceChart, extras } = state.chart;
        if (extras?.markerSeries && !extras.markerSeries._internal_isDisposed) {
          try {
            priceChart.removeSeries(extras.markerSeries);
          } catch (e) {
            logger.debug("Error removing marker series:", e);
          }
        }
        if (priceChart && !priceChart._internal_isDisposed) {
          try {
            priceChart.remove();
          } catch (e) {
            logger.debug("Error removing price chart:", e);
          }
        }
      }

      state.isActive = false;
      state.isDisposed = true;
    });
    chartStates.clear();
    window.chartStates = new Map();
  }
  window.addEventListener("beforeunload", () => {
    cleanup();
    // Clear session marker on page unload to detect true refreshes
    sessionStorage.removeItem("chartSessionActive");
  });

  // Clear session marker on page hide to better detect refreshes vs tab switches
  document.addEventListener("visibilitychange", () => {
    if (document.hidden) {
      // Don't clear session storage here - we want to preserve it for tab switches
      // Only clear on actual page unload (handled in beforeunload)
    }
  });

  // Chart initialization is now triggered by simpleLoader.js after all modules are loaded
  // The automatic initialization has been removed to prevent race conditions

  // Manual trigger function for chart initialization
  window.triggerChartInitialization = async () => {
    logger.info("[charts.js] Manual chart initialization triggered...");

    try {
      // Wait for LightweightCharts with a reasonable timeout
      if (window.waitForLightweightCharts) {
        logger.info("[charts.js] Waiting for LightweightCharts...");
        await window.waitForLightweightCharts();
      }

      // Add retry logic for chart initialization
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          if (window.initializeChart) {
            logger.info(
              `[charts.js] Initializing chart (attempt ${retryCount + 1}/${maxRetries})...`,
            );
            await window.initializeChart();
            logger.info(
              "[charts.js] Manual chart initialization completed successfully",
            );
            break; // Success, exit retry loop
          } else {
            logger.error("[charts.js] initializeChart function not available");
            break;
          }
        } catch (e) {
          retryCount++;
          logger.error(
            `[charts.js] Chart initialization attempt ${retryCount} failed:`,
            e,
          );

          if (retryCount >= maxRetries) {
            logger.error(
              "[charts.js] All chart initialization attempts failed",
            );
            throw e;
          }

          // Wait before retry
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }
      }
    } catch (e) {
      logger.error("[charts.js] Error during manual chart initialization:", e);
      if (window.errorHandler) {
        window.errorHandler.handleError(e, { source: "Charts", pair: "BTC" });
      }
    }
  };
})();
