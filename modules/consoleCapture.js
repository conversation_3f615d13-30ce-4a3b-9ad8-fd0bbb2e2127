/**
 * Console Capture
 * Captures and filters console output for the dashboard
 * Uses centralized logger and error handler for all logging and error reporting
 * Optimized for production with object pooling and improved performance
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("ConsoleCapture")
    : console;
  // --- Config & Thresholds ---
  const DEFAULT_THRESHOLD = 1000;
  const config = {
    MAX_MESSAGES: 50,
    threshold:
      Number(localStorage.getItem("consoleMessageThreshold")) ||
      DEFAULT_THRESHOLD,
    manual: localStorage.getItem("manualThresholdOverride")
      ? Number(localStorage.getItem("manualThresholdOverride"))
      : null,
    DEBUG: false, // Set to true for debug logs
  };

  // --- Object Pool for DOM Elements ---
  const elementPool = {
    pool: [],
    maxSize: 10,

    get() {
      if (this.pool.length > 0) {
        return this.pool.pop();
      }

      const msgDiv = document.createElement("div");
      msgDiv.className = "liquidation-message";
      return msgDiv;
    },

    release(element) {
      if (this.pool.length < this.maxSize) {
        element.className = "liquidation-message";
        element.textContent = "";
        element.style.cssText = "";
        this.pool.push(element);
      }
    },
  };

  // --- Utilities ---
  function shouldDisplay(type, size) {
    const threshold =
      config.manual !== null && !isNaN(config.manual)
        ? config.manual
        : config.threshold;
    return size >= threshold;
  }

  function formatSize(num) {
    if (num >= 1_000_000) return (num / 1_000_000).toFixed(1) + "M";
    if (num >= 1_000) return (num / 1_000).toFixed(1) + "K";
    return num.toFixed(1);
  }

  function getConsoleElement() {
    return document.getElementById("console-capture");
  }
  function getTitleElement() {
    const el = getConsoleElement();
    return el ? el.querySelector(".console-capture-title") : null;
  }

  // --- Message Queue with Batch Processing ---
  const queue = [];
  let domReady = false;
  let processingBatch = false;

  // --- Optimized Deduplication Cache ---
  const recentMessages = new Map(); // key: `${type}|${size}`, value: timestamp
  const DEDUP_WINDOW = 2000; // ms
  let lastCleanupTime = 0;
  const CLEANUP_INTERVAL = 5000; // Cleanup every 5 seconds instead of every call

  function cleanOldMessages() {
    const now = Date.now();
    if (now - lastCleanupTime < CLEANUP_INTERVAL) return;

    lastCleanupTime = now;
    for (const [key, ts] of recentMessages.entries()) {
      if (now - ts > DEDUP_WINDOW) recentMessages.delete(key);
    }
  }

  function isDuplicate(type, size) {
    const key = `${type}|${size}`;
    const now = Date.now();
    cleanOldMessages(); // Optimized cleanup
    if (
      recentMessages.has(key) &&
      now - recentMessages.get(key) < DEDUP_WINDOW
    ) {
      return true;
    }
    recentMessages.set(key, now);
    return false;
  }

  function flushQueue() {
    if (processingBatch) return;
    processingBatch = true;

    // Process in batches for better performance
    const BATCH_SIZE = 10;
    let processed = 0;

    const processBatch = () => {
      while (queue.length && processed < BATCH_SIZE) {
        const { text, type, size } = queue.shift();
        addMessageToDOM(text, type, size);
        processed++;
      }

      if (queue.length > 0) {
        processed = 0;
        requestAnimationFrame(processBatch);
      } else {
        processingBatch = false;
      }
    };

    processBatch();
  }

  // --- Optimized Core DOM Logic ---
  function addMessageToDOM(text, type, size) {
    if (!shouldDisplay(type, size)) return;
    if (isDuplicate(type, size)) return;
    const el = getConsoleElement();
    if (!el) return;

    const label = [
      "long",
      "short",
      "bybit-liquidation-long",
      "bybit-liquidation-short",
    ].includes(type)
      ? "L"
      : ["whale-buy", "whale-sell"].includes(type)
        ? "T"
        : "?";
    const displayText = `${label}$${formatSize(size)}`;

    // Use object pool for better memory management
    const msgDiv = elementPool.get();
    msgDiv.className = "liquidation-message " + type;
    msgDiv.textContent = displayText;

    const title = getTitleElement();
    if (title && title.nextSibling) {
      el.insertBefore(msgDiv, title.nextSibling);
    } else if (title) {
      el.appendChild(msgDiv);
    } else {
      el.insertBefore(msgDiv, el.firstChild);
    }

    // Remove old messages and return to pool
    const msgs = el.getElementsByClassName("liquidation-message");
    while (msgs.length > config.MAX_MESSAGES) {
      const oldMsg = msgs[msgs.length - 1];
      elementPool.release(oldMsg);
      oldMsg.remove();
    }

    if (config.DEBUG) {
      logger.log(`[consoleCapture] Message rendered: ${displayText}`);
    }
  }

  // --- Optimized Public API ---
  window.consoleCaptureAddMessage = function (text, type, size) {
    // Early validation to avoid unnecessary processing
    if (!shouldDisplay(type, size)) return;

    if (!domReady || !getConsoleElement()) {
      queue.push({ text, type, size });
    } else {
      // Use requestIdleCallback for better performance if available
      if (window.requestIdleCallback) {
        window.requestIdleCallback(
          () => {
            addMessageToDOM(text, type, size);
          },
          { timeout: 100 },
        );
      } else {
        addMessageToDOM(text, type, size);
      }
    }
  };

  // Clear all messages but keep the title - optimized with object pooling
  window.clearChartConsole = function () {
    const el = getConsoleElement();
    if (!el) return;
    const title = getTitleElement();
    const messages = Array.from(
      el.getElementsByClassName("liquidation-message"),
    );

    // Return elements to pool before removing
    messages.forEach((msg) => {
      elementPool.release(msg);
      msg.remove();
    });
  };

  // Threshold setters/getters
  window.setConsoleMessageThreshold = (v) => {
    config.threshold = Number(v) || DEFAULT_THRESHOLD;
    localStorage.setItem("consoleMessageThreshold", config.threshold);
  };
  window.setManualThresholdOverride = (v) => {
    if (v === null || v === undefined || v === "") {
      config.manual = null;
      localStorage.removeItem("manualThresholdOverride");
    } else {
      config.manual = Number(v);
      localStorage.setItem("manualThresholdOverride", v);
    }
  };

  // --- DOM Ready ---
  function onDomReady() {
    domReady = true;
    flushQueue();
  }
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    onDomReady();
  } else {
    document.addEventListener("DOMContentLoaded", onDomReady);
  }
})();
