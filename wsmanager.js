class WebSocketManager {
  constructor(url, exchange, options = {}) {
    this.url = url;
    this.exchange = exchange;
    this.name = options.name || exchange;
    this.connected = false;
    this.connecting = false;
    this.reconnectDelay = options.reconnectDelay || 2000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.pingInterval = options.pingInterval || 30000;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.subscriptions = new Map(); // channel -> true or metadata
    this.pendingSubscriptions = new Set();
    this.handlers = new Map(); // channel -> [handlers]
    this.pingTimer = null;
    this.lastPongTime = 0;
    this.intentionalClose = false;
    this.connectionTimeout = null;
    this.messageCallback = null;
    this.networkStatus = navigator.onLine;

    // Track last message received time for freeze detection (Bitstamp)
    this.lastMessageTimestamp = Date.now();

    // DEBUG flag for easy removal of debug logs
    this.DEBUG = false;

    // Throttle log messages to avoid console spam
    this.throttledLog = commonUtils.throttle(console.log, 1000);

    // Bind methods that will be used as callbacks
    this._handleNetworkStatusChange =
      this._handleNetworkStatusChange.bind(this);
    this._handleVisibilityChange = this._handleVisibilityChange.bind(this);
    this._handleConnectionOpen = this._handleConnectionOpen.bind(this);
    this._handleMessage = this._handleMessage.bind(this);

    // Track last time the tab was hidden
    this.lastHiddenTime = null;

    // Add event listeners for online/offline events
    window.addEventListener("online", () =>
      this._handleNetworkStatusChange(true),
    );
    window.addEventListener("offline", () =>
      this._handleNetworkStatusChange(false),
    );

    // Use unified visibility system from ConnectionManager instead of individual handler
    // Individual visibility handling removed to eliminate conflicts

    // Debounce for visibility reconnects
    this._visibilityReconnectDebounce = false;



    // Connect immediately
    this.connect();

    // Register with connection manager if available
    if (window.connectionManager) {
      window.connectionManager.registerManager(this.exchange, this);
    }

    // At the top of the WebSocketManager class (constructor):
    this.messageQueue = [];
    this.processingMessages = false;

    // --- Batching for processMessage and _handleAlertLogic ---
    if (!this._batchedMessageQueue) {
      this._batchedMessageQueue = [];
      this._processingMessageBatch = false;
    }
    if (!this._batchedAlertQueue) {
      this._batchedAlertQueue = [];
      this._processingAlertBatch = false;
    }

    // CRITICAL FIX: Sleep/wake event handlers
    this._setupSleepWakeHandlers();
    

  }

  // Add ping/pong mechanism
  startPingPong() {
    this.stopPingPong();
    this.lastPongTime = Date.now();

    this.pingTimer = setInterval(() => {
      if (!this.connected || !this.ws) {
        this.stopPingPong();
        return;
      }

      // Check if we've received a pong recently with more lenient timeout
      const now = Date.now();
      if (now - this.lastPongTime > this.pingInterval * 3) { // Increased from 2 to 3
        this.reconnect(true);
        return;
      }

      // --- Bitstamp freeze detection with more lenient timeout ---
      if (this.exchange === "bitstamp") {
        // If no message received in 120 seconds, force reconnect (increased from 60)
        if (now - this.lastMessageTimestamp > 120000) {
          this.throttledLog(
            "reconnect",
            `[WSManager-${this.exchange}] No message received in 120s. Forcing reconnect. Last message at: ${new Date(this.lastMessageTimestamp).toISOString()}`
          );
          this.reconnect(true);
          return;
        }
      }

      // --- Bybit freeze detection with more lenient timeout ---
      if (this.exchange === "bybit") {
        const bybitFreezeTimeout = 180000; // 180 seconds (increased from 90)
        if (now - this.lastMessageTimestamp > bybitFreezeTimeout) {
          this.throttledLog(
            "reconnect",
            `[WSManager-${this.exchange}] No message received in ${bybitFreezeTimeout / 1000}s. Forcing reconnect. Last message at: ${new Date(this.lastMessageTimestamp).toISOString()}`
          );
          this.reconnect(true); // Force reconnect
          return;
        }
      }

      // Send ping based on exchange with better error handling
      // Note: _sendPing() has its own error handling.
      // Bitstamp's specific logic (readyState check and lastPongTime update) remains here
      // as it's part of its unique keep-alive within the main ping timer.
      if (this.exchange === "bybit") {
        // For Bybit, we can directly call _sendPing or send here.
        // Calling _sendPing centralizes the actual send logic.
        this._sendPing();
      } else if (this.exchange === "bitstamp") {
        // Bitstamp doesn't support actual ping messages.
        // Check connection state directly.
        if (this.ws.readyState !== WebSocket.OPEN) {
          this.reconnect(true);
        } else {
          // If connection is open, just update pong time.
          // Actual message check (lastMessageTimestamp) is done above.
          this.lastPongTime = now;
        }
      }
      // For other exchanges that might be added and use a generic ping,
      // they would need to be handled or use _sendPing() if it defaults.
      // However, current _sendPing is mostly for Bybit or generic.
      // Consider if a direct this._sendPing() call is better for all non-Bitstamp cases.
      // For now, keeping Bybit explicit to match previous logic structure minus try-catch.
    }, this.pingInterval);
  }

  stopPingPong() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  connect() {
    // Only allow connect if not already connecting or connected and ws is null or closed
    if (
      this.connected ||
      this.connecting ||
      (this.ws &&
        this.ws.readyState !== WebSocket.CLOSED &&
        this.ws.readyState !== undefined)
    ) {
      return;
    }

    this.connecting = true;

    // If ws exists and is not closed, wait for onclose before connecting
    if (
      this.ws &&
      this.ws.readyState !== WebSocket.CLOSED &&
      this.ws.readyState !== undefined
    ) {
      this._pendingConnectAfterClose = true;
      this._cleanupExistingConnection();
      return;
    }

    try {
      // Check network status before attempting to connect
      if (!navigator.onLine) {
        this.connecting = false;
        setTimeout(() => this.reconnect(false), this.reconnectDelay);
        return;
      }

      // Create new WebSocket connection
      this.ws = new WebSocket(this.url);

      // Set binary type for better performance with binary messages
      if (this.exchange === "bybit") {
        this.ws.binaryType = "arraybuffer";
      }

      // Set connection timeout with adaptive timing based on previous attempts
      const baseTimeout = 10000;
      const maxTimeout = 30000;
      const timeout = Math.min(
        baseTimeout * (1 + this.reconnectAttempts * 0.5),
        maxTimeout,
      );

      this.connectionTimeout = setTimeout(() => {
        if (!this.connected) {
          this._cleanupExistingConnection();
          this.connecting = false;
          // Only reconnect if not already connecting (guard against race)
          if (!this.connecting) {
            this.reconnect();
          }
        }
      }, timeout);

      // Connection opened handler
      this.ws.onopen = this._handleConnectionOpen.bind(this);

      // Message handler with performance optimizations
      this.ws.onmessage = (event) => {
        this.lastMessageTimestamp = Date.now();
        this.messageQueue.push(event);
        if (!this.processingMessages) {
          this.processingMessages = true;
          const processBatch = () => {
            const MAX_PER_BATCH = 5; // Increased from 2 for better throughput
            let processed = 0;
            const start = performance.now();
            while (this.messageQueue.length && processed < MAX_PER_BATCH) {
              const evt = this.messageQueue.shift();
              this._handleMessage(evt);
              processed++;
              // If processing is taking too long, break early
              if (performance.now() - start > 4) break; // Reduced from 8ms to 4ms for faster processing
            }
            if (this.messageQueue.length) {
              // Use requestAnimationFrame for smoother processing
              requestAnimationFrame(processBatch);
            } else {
              this.processingMessages = false;
            }
          };
          // Start processing immediately for price data
          requestAnimationFrame(processBatch);
        }
      };

      // Error handler - log but let onclose handle reconnection
      this.ws.onerror = (error) => {
        this.reportError(error);
      };

      // Close handler with reconnection logic
      this.ws.onclose = (event) => {
        clearTimeout(this.connectionTimeout);
        this.connected = false;
        this.connecting = false;
        // Only set ws = null here for proper sequencing
        this.ws = null;
        // If a connect was queued, do it now
        if (this._pendingConnectAfterClose) {
          this._pendingConnectAfterClose = false;
          setTimeout(() => this.connect(), 10);
        } else if (!this.intentionalClose) {
          // Don't reconnect if we're intentionally closing
          if (!this.connecting && !this._pendingReconnect) {
            setTimeout(() => this.reconnect(), 100);
          }
        }
        this.intentionalClose = false;
      };
    } catch (error) {
      clearTimeout(this.connectionTimeout);
      this.connected = false;
      this.connecting = false;

      // Schedule reconnection with a small delay
      setTimeout(() => {
        if (!this.connecting && !this._pendingReconnect) {
          this.reconnect();
        }
      }, 500);
    }
  }

  reconnect(force = false) {
    // Prevent redundant/parallel reconnects
    if (this.connecting || this._pendingReconnect) {
      return;
    }
    
    // Enhanced debouncing for forced reconnects with longer intervals
    if (force) {
      const now = Date.now();
      if (now - this._lastForcedReconnect < 5000) { // Increased from 500ms to 5 seconds
        return;
      }
      this._lastForcedReconnect = now;
    }
    
    this._pendingReconnect = true;
    
    // Don't attempt reconnection if we're offline
    if (!navigator.onLine) {
      this._pendingReconnect = false;
      return;
    }

    this.stopPingPong();

    // Clean up existing connection with better error handling
    if (
      this.ws &&
      this.ws.readyState !== WebSocket.CLOSED &&
      this.ws.readyState !== undefined
    ) {
      try {
        // Only attempt to close if the connection is still open or connecting
        if (
          this.ws.readyState === WebSocket.OPEN ||
          this.ws.readyState === WebSocket.CONNECTING
        ) {
          this._pendingConnectAfterClose = true;
          this.ws.close();
          // Wait for onclose to trigger connect
          this._pendingReconnect = false;
          return;
        }
      } catch (e) {
        // Ignore errors when closing
        console.warn(`[WSManager-${this.exchange}] Error closing connection:`, e);
      }
      // ws will be set to null in onclose
    }

    if (force) {
      this.reconnectAttempts = 0;
      this.connected = false; // Explicitly set
      this.connecting = false; // Explicitly set
      this._pendingReconnect = false;
      
      // Add delay for forced reconnections to prevent rapid reconnects
      setTimeout(() => {
        this.connect();
      }, 2000); // 2 second delay for forced reconnections
      return;
    }

    // Check if we've reached max attempts with better backoff
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      const longDelay = this.reconnectDelay * 20; // Increased from 10 to 20
      // Reset reconnect attempts after a longer timeout to try again later
      setTimeout(() => {
        this.reconnectAttempts = 0;
        if (navigator.onLine) this.connect();
      }, longDelay);
      this._pendingReconnect = false;
      return;
    }

    // Calculate exponential backoff with a maximum limit and better scaling
    const maxBackoffMultiplier = 15; // Increased from 10 to 15
    const backoffMultiplier = Math.min(
      Math.pow(1.8, this.reconnectAttempts), // Increased from 1.5 to 1.8
      maxBackoffMultiplier,
    );
    const delay = this.reconnectDelay * backoffMultiplier;

    this.reconnectAttempts++;

    // Log reconnection attempt with better information
    console.log(
      `[WSManager-${this.exchange}] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
    );

    // Schedule reconnection with better error handling
    setTimeout(() => {
      // Double-check online status before attempting to connect
      if (navigator.onLine) {
        if (!this.connecting && !this._pendingReconnect) {
          this.connected = false; // Explicitly set
          this.connecting = false; // Explicitly set
          this.connect();
        }
      } else {
        // Try again later when potentially back online with longer delay
        setTimeout(() => this.reconnect(false), this.reconnectDelay * 2);
      }
      this._pendingReconnect = false;
    }, delay);
  }

  processMessage(data) {
    // Find all handlers that should receive this message
    for (const [channel, handlers] of this.handlers.entries()) {
      // For Bitstamp
      if (this.exchange === "bitstamp" && data.channel === channel) {
        handlers.forEach((handler) => handler(data));
        return;
      }

      // For Bybit
      if (this.exchange === "bybit" && data.topic === channel) {
        handlers.forEach((handler) => handler(data));
        return;
      }
    }
  }

  subscribe(channel, handler) {
    // Initialize handlers array for this channel if it doesn't exist
    let isNew = false;
    if (!this.handlers.has(channel)) {
      this.handlers.set(channel, []);
      isNew = true;
    }
    this.handlers.get(channel).push(handler);

    // Only add to subscriptions if this is a new channel
    if (isNew) {
      this.subscriptions.set(channel, true);
      if (this.connected) {
        this.sendSubscription([channel]);
      } else {
        this.pendingSubscriptions.add(channel);
      }
    }

    // Debug log for subscribe
    if (this.DEBUG && this.exchange === "bitstamp") {
      console.debug(
        `[WS-DEBUG] [${this.exchange}] subscribe() called for channel:`,
        channel,
        "at",
        new Date().toISOString(),
      );
    }

    return this;
  }

  unsubscribe(channel, handler) {
    if (!this.handlers.has(channel)) return;

    if (this.DEBUG && this.exchange === "bitstamp") {
      console.debug(
        `[WS-DEBUG] [${this.exchange}] unsubscribe() called for channel:`,
        channel,
        "at",
        new Date().toISOString(),
      );
    }

    if (handler) {
      // Remove specific handler
      const arr = this.handlers.get(channel).filter((h) => h !== handler);

      if (arr.length === 0) {
        // No handlers left, fully unsubscribe
        this.sendUnsubscription([channel]);
        this.handlers.delete(channel);
        this.subscriptions.delete(channel);
        this.pendingSubscriptions.delete(channel);
      } else {
        this.handlers.set(channel, arr);
      }
    } else {
      // Remove all handlers for this channel
      this.sendUnsubscription([channel]);
      this.handlers.delete(channel);
      this.subscriptions.delete(channel);
      this.pendingSubscriptions.delete(channel);
    }
  }

  sendSubscription(channels) {
    if (!this.connected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      // Queue for later if not open
      if (Array.isArray(channels)) {
        channels.forEach(channel => this.pendingSubscriptions.add(channel));
      } else if (channels) {
        this.pendingSubscriptions.add(channels);
      }
      return;
    }
    // If no channels provided, subscribe to all current channels
    if (!channels || (Array.isArray(channels) && channels.length === 0)) {
      channels = Array.from(this.subscriptions.keys());
    }
    if (!Array.isArray(channels)) channels = [channels];

    try {
      let msgSent = false;
      if (this.exchange === "bitstamp") {
        for (const channel of channels) {
          const msg = this._createBitstampSubscribeMessage(channel);
          this.ws.send(JSON.stringify(msg));
          if (this.DEBUG) {
            this.throttledLog(
              "debug",
              `[WSManager-${this.exchange}] Sent SUBSCRIBE request for channel: ${channel}`,
            );
          }
          msgSent = true;
        }
      } else if (this.exchange === "bybit") {
        const msg = this._createBybitSubscribeMessage(channels);
        this.ws.send(JSON.stringify(msg));
        if (this.DEBUG) {
          this.throttledLog(
            "debug",
            `[WSManager-${this.exchange}] Sent SUBSCRIBE request for channels: ${JSON.stringify(msg.args)}`,
          );
        }
        msgSent = true;
      }
      // General log if not in DEBUG and message was sent (optional, can be noisy)
      // if (msgSent && !this.DEBUG) {
      //    this.throttledLog('info', `[WSManager-${this.exchange}] Sent subscription request for ${channels.length} channel(s).`);
      // }
    } catch (error) {
      this.throttledLog(
        "error",
        `[WSManager-${this.exchange}] Error subscribing to ${channels}: ${error.message}`,
      );
    }
  }

  sendUnsubscription(channels) {
    if (!this.connected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      // If not open, just skip (unsub will be handled on reconnect if needed)
      return;
    }
    // If no channels provided, unsubscribe from all current channels
    if (!channels || (Array.isArray(channels) && channels.length === 0)) {
      channels = Array.from(this.subscriptions.keys());
    }
    if (!Array.isArray(channels)) channels = [channels];

    try {
      if (this.exchange === "bitstamp") {
        for (const channel of channels) {
          const msg = this._createBitstampUnsubscribeMessage(channel);
          if (this.DEBUG) {
            console.debug(
              `[WS-DEBUG] [${this.exchange}] Sending UNSUBSCRIBE:`,
              msg,
              "at",
              new Date().toISOString(),
            );
          }
          this.ws.send(JSON.stringify(msg));
        }
      } else if (this.exchange === "bybit") {
        const msg = this._createBybitUnsubscribeMessage(channels);
        if (this.DEBUG) {
          console.debug(
            `[WS-DEBUG] [${this.exchange}] Sending UNSUBSCRIBE:`,
            msg,
            "at",
            new Date().toISOString(),
          );
        }
        this.ws.send(JSON.stringify(msg));
      }
      this.throttledLog(
        "unsubscribe",
        `Unsubscribed from ${this.exchange} channel(s): ${channels}`,
      );
    } catch (error) {
      this.throttledLog(
        "error",
        `Error unsubscribing from ${channels}: ${error}`,
      );
      this.reportError(error);
    }
  }

  resubscribeAll() {
    // Clear pending subscriptions
    this.pendingSubscriptions.clear();
    // Resubscribe to all channels in batch
    const channels = Array.from(this.subscriptions.keys()).filter(Boolean);
    if (channels.length > 0) {
      // Debounce resubscribeAll calls
      const now = Date.now();
      if (this.lastResubscribeAllTime && (now - this.lastResubscribeAllTime) < 3000) {
        // Skip if we resubscribed recently (within 3 seconds)
        return;
      }
      this.lastResubscribeAllTime = now;
      
      this.sendSubscription(channels);
    }
  }

  close() {
    this.intentionalClose = true;
    this._cleanupExistingConnection();
    this.connected = false;
    this.connecting = false;
  }

  // Add isConnected method for external status checking
  isConnected() {
    return (
      this.connected &&
      this.ws &&
      this.ws.readyState === WebSocket.OPEN &&
      navigator.onLine
    );
  }

  // Report errors to the error manager
  reportError(error, options = {}) {
    // Fallback to console
    console.error(`WebSocket error (${this.exchange}):`, error);
    return null;
  }

  // Get connection status information
  getStatus() {
    return {
      exchange: this.exchange,
      connected: this.connected,
      connecting: this.connecting,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions.keys()),
      pendingSubscriptions: Array.from(this.pendingSubscriptions),
      lastPongTime: this.lastPongTime,
      lastMessageTime: this.lastMessageTime || 0,
    };
  }

  // Debug method to log current state
  // debug() method removed as unused.

  // Helper methods for cleaner code organization
  _cleanupExistingConnection() {
    if (this.ws) {
      this.intentionalClose = true;
      // Only attempt to close if the connection is not already closing or closed.
      // WebSocket.CONNECTING = 0, WebSocket.OPEN = 1, WebSocket.CLOSING = 2, WebSocket.CLOSED = 3
      if (this.ws.readyState < WebSocket.CLOSING) {
        // Check if CONNECTING (0) or OPEN (1)
        try {
          this.ws.close();
        } catch (e) {
          // Ignore errors when closing
        }
      }
      // Wait for onclose event to nullify ws
      // this.ws will be set to null in onclose handler
    }

    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }

    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  _rebindAllHandlers() {
    for (const [channel, handlers] of this.handlers.entries()) {
      handlers.forEach((handler) => {
        // Only re-subscribe if not already present in subscriptions
        if (!this.subscriptions.has(channel)) {
          this.subscribe(channel, handler);
        }
      });
    }
  }

  _handleConnectionOpen() {
    clearTimeout(this.connectionTimeout);
    this.connected = true;
    this.connecting = false;
    this.reconnectAttempts = 0;
    this.lastPongTime = Date.now();
    this.startPingPong();
    this.resubscribeAll();
    this._rebindAllHandlers(); // Ensure all handlers are re-attached after reconnect
    this._startDataFlowWatchdog(); // Start watchdog after reconnect
    window.dispatchEvent(
      new CustomEvent(`websocket-connected-${this.exchange.toLowerCase()}`, {
        detail: { timestamp: Date.now() },
      }),
    );
  }

  _handleMessage(e) {
    // Update timestamps for connection health monitoring
    const now = Date.now();
    this.lastMessageTime = now;
    this.lastPongTime = now; // Update pong time on any message

    // Skip message processing when tab is hidden to save resources
    if (this._hiddenMessageProcessing) {
      return;
    }

    try {
      let data;

      // Fast path for string data (most common case)
      if (typeof e.data === "string") {
        // Try direct parsing first - most messages will be valid JSON
        try {
          data = JSON.parse(e.data);
        } catch (parseError) {
          // Only attempt recovery for specific error types
          if (parseError.message.includes("position")) {
            // Attempt to recover from position-based JSON errors
            const posMatch = parseError.message.match(/position (\d+)/i);
            if (posMatch && posMatch[1]) {
              const errorPos = parseInt(posMatch[1]);
              // Truncate the string slightly before the error position
              const safePos = Math.max(0, errorPos - 10);
              const truncated = e.data.substring(0, safePos);
              // Try to close the JSON properly
              const fixedJson = truncated + "]}"; // Simple fix attempt
              try {
                data = JSON.parse(fixedJson);
                // Don't log recovery to reduce console noise
              } catch (e) {
                // If recovery failed, throw a more specific error
                throw new Error(
                  `Failed to recover malformed JSON: ${parseError.message}`,
                );
              }
            } else {
              throw parseError;
            }
          } else {
            throw parseError;
          }
        }
      } else if (e.data instanceof ArrayBuffer) {
        // Handle binary data for Bybit - use cached decoder if available
        if (!this._textDecoder) {
          this._textDecoder = new TextDecoder();
        }
        const rawData = this._textDecoder.decode(e.data);
        try {
          data = JSON.parse(rawData);
        } catch (parseError) {
          throw new Error(`Binary data parse error: ${parseError.message}`);
        }
      } else {
        // Handle other data types (Blob, etc.) - should be rare
        throw new Error(`Unsupported message data type: ${typeof e.data}`);
      }

      // Fast return for pong messages
      if (data.op === "pong") {
        if (this.exchange === "bybit") {
            this.throttledLog("pong", `[WSManager-${this.exchange}] Pong received.`);
        }
        return;
      }

      // LOG ALL RAW BYBIT LIQUIDATION MESSAGES
      if (this.exchange === "bybit" && data.topic?.startsWith("liquidation.")) {
        console.log("[BYBIT RAW LIQUIDATION]", data);
      }

      // --- Batching for processMessage and _handleAlertLogic ---
      if (!this._batchedMessageQueue) {
        this._batchedMessageQueue = [];
        this._processingMessageBatch = false;
      }
      if (!this._batchedAlertQueue) {
        this._batchedAlertQueue = [];
        this._processingAlertBatch = false;
      }

      // Enhanced batching with adaptive batch size based on system performance
      // Add timestamp validation to prevent processing old messages after visibility changes
      const currentTime = Date.now();
      
      // Add timestamp to the message for age validation
      const messageWithTimestamp = {
        ...data,
        _receivedAt: currentTime
      };
      
      this._batchedMessageQueue.push(messageWithTimestamp);
      if (!this._processingMessageBatch) {
        this._processingMessageBatch = true;
        let BATCH_SIZE = this._getOptimalBatchSize();
        const processMessageBatch = () => {
          let processed = 0;
          const start = performance.now();
          while (this._batchedMessageQueue.length && processed < BATCH_SIZE) {
            const msg = this._batchedMessageQueue.shift();
            
            // Check message age to prevent processing old messages after visibility changes
            const currentTime = Date.now();
            const messageAge = currentTime - (msg._receivedAt || currentTime);
            
            // Skip messages that are too old (more than 10 seconds) to prevent "Cannot update oldest data" errors
            if (messageAge > 10000) {
              // Only log occasionally to avoid console spam (1% of old messages)
              if (Math.random() < 0.01) {
                this.throttledLog(
                  "debug",
                  `Skipping old message in batch: ${messageAge}ms old (${this.exchange})`
                );
              }
              continue; // Skip this message and process the next one
            }
            
            const msgStart = performance.now();
            this.processMessage(msg);
            const msgDuration = performance.now() - msgStart;
            if (msgDuration > 4) { // Reduced from 8ms to 4ms
              // If a single message is slow, reduce batch size
              BATCH_SIZE = Math.max(1, Math.floor(BATCH_SIZE / 2));
            }
            processed++;
            // Keep each batch under 4ms to avoid blocking (reduced from 8ms)
            if (performance.now() - start > 4) break;
          }
          if (this._batchedMessageQueue.length) {
            // Use requestAnimationFrame for smoother processing
            requestAnimationFrame(processMessageBatch);
          } else {
            this._processingMessageBatch = false;
          }
        };
        // Start processing immediately
        requestAnimationFrame(processMessageBatch);
      }

      // --- Batching for _handleAlertLogic ---
      const isBybitLiq = this.exchange === "bybit" && data.topic?.startsWith("liquidation.");
      const shouldAlert = isBybitLiq || (!this._lastAlertCheck || now - this._lastAlertCheck > 100);
      if (shouldAlert) {
        if (!isBybitLiq) this._lastAlertCheck = now;
        this._batchedAlertQueue.push(data);
        if (!this._processingAlertBatch) {
          this._processingAlertBatch = true;
          let BATCH_SIZE = this._getOptimalBatchSize();
          const processAlertBatch = () => {
            let processed = 0;
            const start = performance.now();
            while (this._batchedAlertQueue.length && processed < BATCH_SIZE) {
              const alertData = this._batchedAlertQueue.shift();
              const alertStart = performance.now();
              this._handleAlertLogic(alertData);
              const alertDuration = performance.now() - alertStart;
              if (alertDuration > 4) { // Reduced from 8ms to 4ms
                BATCH_SIZE = Math.max(1, Math.floor(BATCH_SIZE / 2));
              }
              processed++;
              if (performance.now() - start > 4) break; // Reduced from 8ms to 4ms
            }
            if (this._batchedAlertQueue.length) {
              // Use requestAnimationFrame for smoother processing
              requestAnimationFrame(processAlertBatch);
            } else {
              this._processingAlertBatch = false;
            }
          };
          // Start processing immediately
          requestAnimationFrame(processAlertBatch);
        }
      }
    } catch (error) {
      // Only log errors at a throttled rate to prevent console spam
      this.throttledLog(
        "error",
        `Error processing ${this.exchange} message: ${error.message}`,
      );
    }

    this.lastMessageTimestamp = Date.now();
    if (this._dataFlowWatchdogTimer) {
      clearTimeout(this._dataFlowWatchdogTimer);
      this._dataFlowWatchdogTimer = null;
    }
  }

  // Enhanced batch size calculation based on system performance
  _getOptimalBatchSize() {
    // Start with base batch size - increased for better throughput
    let baseSize = 8; // Increased from 3 to 8 for better throughput
    
    // Reduce batch size if system is under load
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
      baseSize = 4; // Reduced from 2 to 4
    }
    
    // Reduce batch size if memory is constrained
    if (performance.memory && performance.memory.usedJSHeapSize > performance.memory.jsHeapSizeLimit * 0.8) {
      baseSize = Math.max(2, baseSize - 2); // Reduced minimum from 1 to 2
    }
    
    // Reduce batch size if tab is not focused
    if (document.visibilityState !== 'visible') {
      baseSize = Math.max(2, baseSize - 2); // Reduced minimum from 1 to 2
    }
    
    return baseSize;
  }

  // Network status change handler
  _handleNetworkStatusChange(isOnline) {
    this.throttledLog(
      "network",
      `Network status changed: ${isOnline ? "online" : "offline"}`,
    );
    this.networkStatus = isOnline;

    if (isOnline) {
      // We're back online, check connection and reconnect if needed
      if (!this.isConnected()) {
        this.throttledLog(
          "network",
          `Network restored, reconnecting ${this.exchange} WebSocket`,
        );
        // Reset reconnect attempts to ensure quick reconnection
        this.reconnectAttempts = 0;
        this.reconnect(true);
      }
    } else {
      // We're offline, no need to keep trying to reconnect
      this.throttledLog(
        "network",
        `Network offline, pausing ${this.exchange} WebSocket reconnection`,
      );
      this._cleanupExistingConnection();
    }
  }

  // Simple visibility change handler that works with unified ConnectionManager
  _handleVisibilityChange() {
    const now = Date.now();

    // Debounce visibility changes to prevent spam
    if (this.lastVisibilityChange && (now - this.lastVisibilityChange) < 2000) {
      return; // Skip if we just changed visibility recently
    }
    this.lastVisibilityChange = now;

    if (document.visibilityState === "hidden") {
      this.lastHiddenTime = now;
      this.throttledLog("visibility", `${this.exchange} tab hidden - maintaining connection with reduced ping frequency`);
      
      // Reduce ping frequency when hidden
      if (this.pingTimer) {
        clearInterval(this.pingTimer);
        this.pingTimer = setInterval(() => {
          if (!this.connected || !this.ws) {
            this.stopPingPong();
            return;
          }
          if (now - this.lastPongTime > 180000) {
            this.throttledLog("ping", `Connection timeout while hidden, reconnecting ${this.exchange}`);
            this.reconnect(true);
            return;
          }
          this._sendPing();
        }, 120000); // 2 minutes
      }
      
      this._hiddenSubscriptions = new Set(this.subscriptions.keys());
      this._hiddenMessageProcessing = true;
      
    } else if (document.visibilityState === "visible") {
      this.throttledLog("visibility", `${this.exchange} tab visible - restoring normal operation`);
      
      // Simple: Just restart normal ping and clear hidden state
      this._hiddenMessageProcessing = false;
      this.lastHiddenTime = null;
      
      if (this.isConnected()) {
        this.startPingPong();
      }
      
      // Dispatch event for other modules
      window.dispatchEvent(
        new CustomEvent(`websocket-visibility-restored-${this.exchange.toLowerCase()}`,
          {
            detail: {
              timestamp: Date.now(),
              exchange: this.exchange,
              subscriptions: Array.from(this.subscriptions.keys()),
              connectionState: {
                readyState: this.ws?.readyState,
                connected: this.connected,
                lastPongTime: this.lastPongTime
              }
            },
          }
        )
      );
    }
  }

  // CRITICAL FIX: Sleep/wake event handlers
  _setupSleepWakeHandlers() {
    // Handle computer sleep
    this._handleComputerSleep = (event) => {
      this.throttledLog("sleep", `${this.exchange} computer sleep detected`);
      this._sleepDetected = true;
      this._lastSleepTime = Date.now();
    };
    
    // Handle computer wake
    this._handleComputerWake = (event) => {
      const sleepDuration = event.detail?.sleepDuration || 0;
      this.throttledLog("wake", `${this.exchange} computer wake detected after ${Math.round(sleepDuration/1000)}s sleep`);
      
      this._sleepDetected = false;
      
      // Force reconnection after sleep
      setTimeout(() => {
        if (this._sleepDetected === false) { // Double check we're not still sleeping
          this.throttledLog("wake", `${this.exchange} forcing reconnection after wake`);
          this.reconnect(true); // Force reconnection
        }
      }, 3000); // 3 second delay to allow system to stabilize
    };
    
    // Add event listeners
    window.addEventListener('computer-sleep', this._handleComputerSleep);
    window.addEventListener('computer-wake', this._handleComputerWake);
  }

  // Enhanced resubscription with retry logic
  _resubscribeWithRetry(maxRetries = 3, retryDelay = 1000) {
    let retryCount = 0;
    
    const attemptResubscribe = () => {
      if (retryCount >= maxRetries) {
        this.throttledLog(
          "resubscribe",
          `${this.exchange} resubscription failed after ${maxRetries} attempts, forcing reconnection`
        );
        this.reconnect(true);
        return;
      }
      
      try {
        this.resubscribeAll();
        this.throttledLog(
          "resubscribe",
          `${this.exchange} resubscription successful (attempt ${retryCount + 1})`
        );
      } catch (error) {
        retryCount++;
        this.throttledLog(
          "resubscribe",
          `${this.exchange} resubscription attempt ${retryCount} failed: ${error.message}`
        );
        setTimeout(attemptResubscribe, retryDelay);
      }
    };
    
    attemptResubscribe();
  }

  // Send ping to check connection
  _sendPing() {
    if (!this.connected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      switch (this.exchange) {
        case "bybit":
          this.ws.send(JSON.stringify({ op: "ping" }));
          break;
        case "bitstamp":
          // Bitstamp does not support server-side ping/pong via WebSocket messages.
          // Its connection liveness is checked in startPingPong via lastMessageTimestamp
          // and by updating lastPongTime if the connection is merely OPEN.
          // So, _sendPing for Bitstamp is a no-op if connection is otherwise fine.
          break;
        default:
          // Fallback for other exchanges
          this.ws.send(JSON.stringify({ op: "ping" }));
      }
    } catch (error) {
      this.throttledLog(
        "ping",
        `Error sending ping to ${this.exchange}: ${error}`,
      );
      this.reconnect(true);
    }
  }

  //+------------------------------------------------------------------------+
  //| Payload Creation Helpers                                               |
  //+------------------------------------------------------------------------+

  _createBitstampSubscribeMessage(channel) {
    return {
      event: "bts:subscribe",
      data: { channel },
    };
  }

  _createBybitSubscribeMessage(channelsArray) {
    return {
      op: "subscribe",
      args: channelsArray,
    };
  }

  _createBitstampUnsubscribeMessage(channel) {
    return {
      event: "bts:unsubscribe",
      data: { channel },
    };
  }

  _createBybitUnsubscribeMessage(channelsArray) {
    return {
      op: "unsubscribe",
      args: channelsArray,
    };
  }

  //+------------------------------------------------------------------------+
  //| Alert Logic Helper                                                     |
  //+------------------------------------------------------------------------+

  _handleAlertLogic(data) {
    // --- DEDUPLICATION STATE ---
    if (!window._wsDedup) {
      window._wsDedup = {
        liquidations: new Set(),
        whales: new Set(),
        lastCleanup: 0,
        clearOld: function (set, ms = 5000) {
          const now = Date.now();
          // Only cleanup every 10 seconds to reduce overhead
          if (now - this.lastCleanup < 10000) return;
          this.lastCleanup = now;

          const toDelete = [];
          for (const key of set) {
            const [ts] = key.split("|");
            if (now - Number(ts) > ms) toDelete.push(key);
          }
          // Batch delete operations for better performance
          toDelete.forEach((key) => set.delete(key));
        },
      };
    }

    // Whale/Liquidation alert logic (Bybit) - optimized for performance
    if (
      this.exchange === "bybit" &&
      data.topic?.startsWith("liquidation.") &&
      data.data
    ) {
      const liquidation = Array.isArray(data.data) ? data.data[0] : data.data;

      // Parse price and size as floats
      const price = parseFloat(liquidation.price);
      const size = parseFloat(liquidation.size);

      if (isNaN(price) || isNaN(size)) {
        console.log("[wsmanager.js] Liquidation message has invalid price or size, skipping:", { price, size, liquidation });
        return;
      }

      const value = price * size;

      // Cache threshold to avoid repeated localStorage access
      if (
        !this._cachedLiqThreshold ||
        Date.now() - this._lastThresholdCheck > 5000  // Reduced from 30000 to 5000ms
      ) {
        this._cachedLiqThreshold =
          (typeof localStorage !== "undefined" &&
            localStorage.getItem("liquidationThreshold") &&
            parseFloat(localStorage.getItem("liquidationThreshold"))) ||
          100000;
        this._lastThresholdCheck = Date.now();
        console.log("[wsmanager.js] Liquidation threshold updated:", this._cachedLiqThreshold);
      }

      // MANUAL THRESHOLD OVERRIDE: If set, bypass all other threshold checks
      const manualThreshold = window.manualThresholdOverride;
      const shouldDisplay = manualThreshold !== null && manualThreshold !== undefined
        ? value >= manualThreshold
        : value >= this._cachedLiqThreshold;

      // Make side assignment robust and case-insensitive
      const sideRaw = typeof liquidation.side === "string" ? liquidation.side.trim().toLowerCase() : "";
      const side = sideRaw === "buy" ? "LONG" : sideRaw === "sell" ? "SHORT" : "UNKNOWN";
      // Use Bybit-specific liquidation types for correct labeling
      const liqType =
        side === "LONG"
          ? "bybit-liquidation-long"
          : side === "SHORT"
          ? "bybit-liquidation-short"
          : "bybit-liquidation-unknown";

      if (shouldDisplay) {
        const ts = Date.now();
        const dedupKey = `${Math.round(ts / 2000) * 2000}|${price}|${size}|${liquidation.side}`;
        window._wsDedup.clearOld(window._wsDedup.liquidations, 5000);

        if (!window._wsDedup.liquidations.has(dedupKey)) {
          window._wsDedup.liquidations.add(dedupKey);

          // Defer expensive DOM operations
          setTimeout(() => {
            if (window.consoleCaptureAddMessage) {
              const formattedValue =
                window.utils && window.utils.formatLargeNumber
                  ? window.utils.formatLargeNumber(value)
                  : value.toLocaleString(undefined, {
                      maximumFractionDigits: 0,
                    });
              console.log("[wsmanager.js] Sending liquidation to console capture:", {
                text: `L $${formattedValue}`,
                type: liqType,
                value: value
              });
              window.consoleCaptureAddMessage(
                `L $${formattedValue}`,
                liqType,
                value,
              );
            }
          }, 0);
        }
      } else {
        console.log("[wsmanager.js] Liquidation below threshold, not displaying:", { value, threshold: this._cachedLiqThreshold });
      }
    }

    // Whale alert logic (Bitstamp) - optimized for performance
    if (
      this.exchange === "bitstamp" &&
      data.channel?.startsWith("live_trades_") &&
      data.event === "trade" &&
      data.data
    ) {
      const trade = data.data;

      // Fast path: check if we have required fields
      if (
        !trade.price ||
        (!trade.amount && !trade.size && !trade.qty) ||
        !("type" in trade)
      )
        return;

      const price = parseFloat(trade.price);
      const size = parseFloat(trade.amount || trade.size || trade.qty);

      // Use Bitstamp trade.type field for buy/sell detection
      const side = trade.type === 0 ? "BUY" : trade.type === 1 ? "SELL" : "";
      if (!side) return;

      const value = price * size;

      // Cache threshold to avoid repeated localStorage access
      if (
        !this._cachedWhaleThreshold ||
        Date.now() - this._lastWhaleThresholdCheck > 30000
      ) {
        this._cachedWhaleThreshold =
          (typeof localStorage !== "undefined" &&
            localStorage.getItem("whaleAlertThreshold") &&
            parseFloat(localStorage.getItem("whaleAlertThreshold"))) ||
          10000;
        this._lastWhaleThresholdCheck = Date.now();
      }

      if (value >= this._cachedWhaleThreshold) {
        const ts = Date.now();
        const dedupKey = `${Math.round(ts / 2000) * 2000}|${price}|${size}|${side}`;
        window._wsDedup.clearOld(window._wsDedup.whales, 5000);

        if (!window._wsDedup.whales.has(dedupKey)) {
          window._wsDedup.whales.add(dedupKey);

          // Defer expensive DOM operations
          setTimeout(() => {
            if (window.consoleCaptureAddMessage) {
              const formattedValue =
                window.utils && window.utils.formatLargeNumber
                  ? window.utils.formatLargeNumber(value)
                  : value.toLocaleString(undefined, {
                      maximumFractionDigits: 0,
                    });
              const whaleType = side === "BUY" ? "whale-buy" : "whale-sell";
              window.consoleCaptureAddMessage(
                `T $${formattedValue}`,
                whaleType,
                value,
              );
            }
          }, 0);
        }
      }
    }
  }

  _startDataFlowWatchdog() {
    if (this._dataFlowWatchdogTimer) clearTimeout(this._dataFlowWatchdogTimer);
    this._dataFlowWatchdogTimer = setTimeout(() => {
      const now = Date.now();
      // If no new message in 10s after reconnect, force resubscribe
      if (now - this.lastMessageTimestamp > 10000) {
        console.warn(`[WSManager-${this.exchange}] No data after reconnect, forcing resubscribe to all channels.`);
        this.resubscribeAll();
        // Start another timer: if still no data after 10s, force socket close/reopen
        this._dataFlowWatchdogTimer = setTimeout(() => {
          if (Date.now() - this.lastMessageTimestamp > 20000) {
            console.error(`[WSManager-${this.exchange}] Still no data after forced resubscribe, closing and reopening socket.`);
            this._cleanupExistingConnection();
            this.connect();
          }
        }, 10000);
      }
    }, 10000);
  }

  /**
   * Unsubscribes and resubscribes to all Bybit channels for a given pair.
   * Ensures all handlers are re-attached and no stale subscriptions remain.
   * @param {string} pair - Trading pair (e.g., 'BTC')
   */
  resubscribeAllForPair(pair) {
    if (!this.connected || !this.ws) {
      this.throttledLog(
        "resubscribe",
        `${this.exchange} not connected, cannot resubscribe for ${pair}`,
      );
      return;
    }

    // Enhanced pair-specific resubscription with better error handling
    const pairChannels = Array.from(this.subscriptions.keys()).filter(channel => 
      channel.toLowerCase().includes(pair.toLowerCase())
    );

    // Removed hardcoded supportedPairs restriction to allow all Bitstamp pairs

    if (pairChannels.length === 0) {
      // Only log if this is unexpected (e.g., we're switching to a pair that should have channels)
      if (this.lastResubscribePair !== pair) {
        this.throttledLog(
          "resubscribe",
          `${this.exchange} no channels found for pair ${pair}`,
        );
        this.lastResubscribePair = pair;
      }
      return;
    }

    // Debounce resubscription for the same pair
    const now = Date.now();
    if (this.lastResubscribeTime && this.lastResubscribePair === pair && (now - this.lastResubscribeTime) < 5000) {
      // Skip if we resubscribed to this pair recently (within 5 seconds)
      return;
    }

    this.lastResubscribeTime = now;
    this.lastResubscribePair = pair;

    // Only log if this is a new resubscription or after a significant delay
    this.throttledLog(
      "resubscribe",
      `${this.exchange} resubscribing to ${pairChannels.length} channels for ${pair}`,
    );

    this.sendSubscription(pairChannels);
  }

  // Enhanced connection state management
  getConnectionState() {
    return {
      connected: this.connected,
      connecting: this.connecting,
      readyState: this.ws?.readyState,
      lastMessageTime: this.lastMessageTime,
      lastPongTime: this.lastPongTime,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions.keys()),
      hiddenMessageProcessing: this._hiddenMessageProcessing || false,
      networkStatus: this.networkStatus,
      visibilityState: document.visibilityState
    };
  }

  // Enhanced subscription management for pair switching
  subscribeForPair(pair, channels) {
    if (!Array.isArray(channels)) {
      channels = [channels];
    }

    this.throttledLog(
      "subscribe",
      `${this.exchange} subscribing to ${channels.length} channels for ${pair}`,
    );

    // Unsubscribe from other pairs first to prevent conflicts
    const otherPairChannels = Array.from(this.subscriptions.keys()).filter(channel => 
      !channel.toLowerCase().includes(pair.toLowerCase())
    );

    if (otherPairChannels.length > 0) {
      this.throttledLog(
        "subscribe",
        `${this.exchange} unsubscribing from ${otherPairChannels.length} other pair channels`,
      );
      this.sendUnsubscription(otherPairChannels);
    }

    // Subscribe to new pair channels
    channels.forEach(channel => {
      this.subscriptions.set(channel, true);
    });

    if (this.connected) {
      this.sendSubscription(channels);
    } else {
      channels.forEach(channel => {
        this.pendingSubscriptions.add(channel);
      });
    }
  }
}

// Create just one manager per exchange
window.bitstampWsManager = new WebSocketManager(
  "wss://ws.bitstamp.net",
  "bitstamp",
);
window.bybitWsManager = new WebSocketManager(
  window.CONFIG.websockets.bybit.url,
  "bybit",
  {
    pingInterval: 2000, // More aggressive pinging (2 seconds)
    reconnectDelay: window.CONFIG.websockets.bybit.reconnectDelay,
    maxReconnectAttempts: window.CONFIG.websockets.bybit.maxReconnectAttempts
  }
);

// Make the class available globally
window.WebSocketManager = WebSocketManager;

// Function to force refresh threshold caches when settings are updated
window.refreshThresholdCaches = function() {
  if (window.bybitWsManager) {
    window.bybitWsManager._cachedLiqThreshold = null;
    window.bybitWsManager._cachedWhaleThreshold = null;
    window.bybitWsManager._lastThresholdCheck = 0;
    window.bybitWsManager._lastWhaleThresholdCheck = 0;
    console.log("[wsmanager.js] Threshold caches cleared, will refresh on next check");
  }
  if (window.bitstampWsManager) {
    window.bitstampWsManager._cachedWhaleThreshold = null;
    window.bitstampWsManager._lastWhaleThresholdCheck = 0;
  }
};

// Test function to verify liquidation subscription status
window.testLiquidationSubscription = function() {
  console.log("=== LIQUIDATION SUBSCRIPTION TEST ===");
  
  // Check Bybit WebSocket status
  if (window.bybitWsManager) {
    const status = window.bybitWsManager.getStatus();
    console.log("Bybit WebSocket Status:", {
      connected: status.connected,
      connecting: status.connecting,
      subscriptions: Array.from(status.subscriptions),
      reconnectAttempts: status.reconnectAttempts
    });
    
    // Check if liquidation subscription exists
    const currentPair = window.currentActivePair || 'BTC';
    const liquidationChannel = `liquidation.${currentPair.toUpperCase()}USDT`;
    const hasLiquidationSub = status.subscriptions.includes(liquidationChannel);
    console.log(`Liquidation subscription for ${liquidationChannel}:`, hasLiquidationSub);
    
    // Check if handler exists even if subscription isn't in the list
    const hasHandler = window.bybitWsManager.subscriptions.has(liquidationChannel);
    console.log(`Handler exists for ${liquidationChannel}:`, hasHandler);
    
    if (hasLiquidationSub) {
      console.log("✅ Liquidation subscription is active");
    } else {
      console.log("⚠️ Liquidation subscription missing! Attempting to subscribe...");
      window.ensureLiquidationSubscription();
    }
  } else {
    console.error("❌ Bybit WebSocket manager not found!");
  }
  
  // Check console capture system
  console.log("Console Capture Status:", {
    domReady: typeof window.consoleCaptureAddMessage === 'function',
    threshold: window.liquidationThreshold,
    consoleThreshold: window.consoleMessageThreshold
  });
  
  // Test console capture with a mock liquidation
  if (window.consoleCaptureAddMessage) {
    console.log("🧪 Testing console capture with mock liquidation...");
    window.consoleCaptureAddMessage("L $150,000", "bybit-liquidation-long", 150000);
  }
  
  console.log("=== END TEST ===");
};

// Function to manually ensure liquidation subscription is set up
window.ensureLiquidationSubscription = function() {
  console.log("=== ENSURING LIQUIDATION SUBSCRIPTION ===");
  
  const currentPair = window.currentActivePair || 'BTC';
  const liquidationChannel = `liquidation.${currentPair.toUpperCase()}USDT`;
  
  if (!window.bybitWsManager) {
    console.error("❌ Bybit WebSocket manager not found!");
    return;
  }
  
  if (!window.bybitWsManager.isConnected()) {
    console.error("❌ Bybit WebSocket not connected!");
    return;
  }
  
  // Check if subscription already exists
  const status = window.bybitWsManager.getStatus();
  const hasSubscription = status.subscriptions.includes(liquidationChannel);
  
  if (hasSubscription) {
    console.log("✅ Liquidation subscription already exists");
    return;
  }
  
  console.log("🔄 Creating liquidation subscription...");
  
  // Create the subscription with proper handler
  window.bybitWsManager.subscribe(liquidationChannel, (data) => {
    console.log("[MANUAL] Liquidation message received:", data);
    
    // Process the liquidation message
    if (data?.data) {
      const liquidations = Array.isArray(data.data) ? data.data : [data.data];
      liquidations.forEach((liq) => {
        if (liq?.price && liq?.side) {
          const price = parseFloat(liq.price);
          const size = parseFloat(liq.size || liq.qty || 0);
          const side = liq.side.toLowerCase();
          const value = price * size;
          
          if (Number.isFinite(price) && Number.isFinite(size) && size > 0) {
            console.log(`[MANUAL] Processing liquidation: ${side} $${value.toLocaleString()} @ $${price}`);
            
            // Publish to event bus for other components
            if (window.eventBus?.publish) {
              window.eventBus.publish(`liquidation-${currentPair}`, {
                price: price,
                amount: size,
                side: side,
                timestamp: Math.floor(Date.now() / 1000),
              });
            }
          }
        }
      });
    }
  });
  
  console.log("✅ Liquidation subscription created successfully");
  
  // Verify the subscription was created
  setTimeout(() => {
    const newStatus = window.bybitWsManager.getStatus();
    const nowHasSubscription = newStatus.subscriptions.includes(liquidationChannel);
    console.log(`Verification: Subscription now exists: ${nowHasSubscription}`);
  }, 1000);
};

// Comprehensive test for liquidation system integration
window.testLiquidationSystemIntegration = function() {
  console.log("=== LIQUIDATION SYSTEM INTEGRATION TEST ===");
  
  // 1. Test threshold settings
  console.log("1. Testing threshold settings...");
  const originalThreshold = window.liquidationThreshold;
  const originalConsoleThreshold = window.consoleMessageThreshold;
  
  console.log("   Current thresholds:", {
    liquidation: window.liquidationThreshold,
    console: window.consoleMessageThreshold
  });
  
  // Test threshold changes
  if (typeof window.setLiquidationThreshold === 'function') {
    window.setLiquidationThreshold(50000);
    console.log("   ✅ Liquidation threshold updated to:", window.liquidationThreshold);
  }
  
  if (typeof window.setConsoleMessageThreshold === 'function') {
    window.setConsoleMessageThreshold(25000);
    console.log("   ✅ Console threshold updated to:", window.consoleMessageThreshold);
  }
  
  // 2. Test console capture with different thresholds
  console.log("2. Testing console capture with different thresholds...");
  const testCases = [
    { type: "bybit-liquidation-long", value: 10000, expected: "filtered" },
    { type: "bybit-liquidation-long", value: 30000, expected: "displayed" },
    { type: "bybit-liquidation-long", value: 60000, expected: "displayed" }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`   Test ${index + 1}: ${testCase.type} $${testCase.value} (${testCase.expected})`);
    if (window.consoleCaptureAddMessage) {
      window.consoleCaptureAddMessage(`Test ${index + 1}`, testCase.type, testCase.value);
    }
  });
  
  // 3. Test chart switching simulation
  console.log("3. Testing chart switching simulation...");
  const currentPair = window.currentActivePair || 'BTC';
  const testPair = currentPair === 'BTC' ? 'ETH' : 'BTC';
  
  console.log(`   Current pair: ${currentPair}`);
  console.log(`   Test pair: ${testPair}`);
  
  // Check if switchPair function exists
  if (typeof window.switchPair === 'function') {
    console.log("   ✅ switchPair function available");
  } else {
    console.log("   ❌ switchPair function not found");
  }
  
  // Check if unsubscribePair properly handles liquidation
  if (window.bybitWsManager) {
    const status = window.bybitWsManager.getStatus();
    const currentLiquidationChannel = `liquidation.${currentPair.toUpperCase()}USDT`;
    const testLiquidationChannel = `liquidation.${testPair.toUpperCase()}USDT`;
    
    console.log("   Current liquidation subscription:", status.subscriptions.includes(currentLiquidationChannel));
    console.log("   Test pair liquidation subscription:", status.subscriptions.includes(testLiquidationChannel));
  }
  
  // 4. Test settings persistence
  console.log("4. Testing settings persistence...");
  const storedLiquidationThreshold = localStorage.getItem("liquidationThreshold");
  const storedConsoleThreshold = localStorage.getItem("consoleMessageThreshold");
  
  console.log("   Stored thresholds:", {
    liquidation: storedLiquidationThreshold,
    console: storedConsoleThreshold
  });
  
  // 5. Test WebSocket manager threshold caches
  console.log("5. Testing WebSocket manager threshold caches...");
  if (typeof window.refreshThresholdCaches === 'function') {
    console.log("   ✅ refreshThresholdCaches function available");
    window.refreshThresholdCaches();
    console.log("   ✅ Threshold caches refreshed");
  } else {
    console.log("   ❌ refreshThresholdCaches function not found");
  }
  
  // 6. Test console capture performance
  console.log("6. Testing console capture performance...");
  if (typeof window.getConsoleCaptureMetrics === 'function') {
    const metrics = window.getConsoleCaptureMetrics();
    console.log("   Console capture metrics:", metrics);
  }
  
  // 7. Test liquidation message flow
  console.log("7. Testing liquidation message flow...");
  if (window.liqEventCallback) {
    console.log("   ✅ liqEventCallback function available");
    // Test the callback
    window.liqEventCallback({ side: "buy", value: 75000 });
  } else {
    console.log("   ❌ liqEventCallback function not found");
  }
  
  // Restore original thresholds
  if (typeof window.setLiquidationThreshold === 'function') {
    window.setLiquidationThreshold(originalThreshold);
  }
  if (typeof window.setConsoleMessageThreshold === 'function') {
    window.setConsoleMessageThreshold(originalConsoleThreshold);
  }
  
  console.log("=== INTEGRATION TEST COMPLETE ===");
  console.log("Check the console capture UI for test messages");
  console.log("All systems should be working together properly!");
};

// Test function to simulate the exact liquidation message received
window.testExactLiquidation = function() {
  console.log("=== TESTING EXACT LIQUIDATION MESSAGE ===");
  
  // Check current thresholds
  console.log("Current thresholds:", {
    liquidation: window.liquidationThreshold,
    console: window.consoleMessageThreshold,
    whale: window.whaleAlertThreshold
  });
  
  // Fix console threshold if it's too high
  if (window.consoleMessageThreshold > 1000) {
    console.log("⚠️ Console threshold too high, setting to $100...");
    if (typeof window.setConsoleMessageThreshold === 'function') {
      window.setConsoleMessageThreshold(100);
    } else {
      window.consoleMessageThreshold = 100;
    }
    console.log("✅ Console threshold updated to:", window.consoleMessageThreshold);
  }
  
  // Simulate the exact liquidation message from the console
  const testLiquidation = {
    topic: 'liquidation.BTCUSDT',
    type: 'snapshot',
    ts: 1751261438649,
    data: {
      updatedTime: 1751261438649,
      symbol: 'BTCUSDT',
      side: 'Buy',
      size: '0.002',
      price: '107642.20'
    }
  };
  
  console.log("Simulating liquidation message:", testLiquidation);
  
  // Process it through the WebSocket manager
  if (window.bybitWsManager) {
    window.bybitWsManager._handleAlertLogic(testLiquidation);
  } else {
    console.error("❌ Bybit WebSocket manager not found!");
  }
  
  console.log("=== END EXACT LIQUIDATION TEST ===");
};

// Function to fix console threshold for liquidation messages
window.fixConsoleThreshold = function() {
  console.log("=== FIXING CONSOLE THRESHOLD ===");
  
  const currentThreshold = window.consoleMessageThreshold;
  console.log("Current console threshold:", currentThreshold);
  
  if (currentThreshold > 1000) {
    console.log("⚠️ Console threshold too high for liquidations, fixing...");
    
    // Set console threshold to match liquidation threshold
    const liquidationThreshold = window.liquidationThreshold || 100;
    const newThreshold = Math.min(liquidationThreshold, 1000); // Cap at $1000
    
    if (typeof window.setConsoleMessageThreshold === 'function') {
      window.setConsoleMessageThreshold(newThreshold);
    } else {
      window.consoleMessageThreshold = newThreshold;
    }
    
    // Save to localStorage
    try {
      localStorage.setItem("consoleMessageThreshold", newThreshold.toString());
      console.log("✅ Console threshold updated to:", newThreshold);
      console.log("✅ Threshold saved to localStorage");
    } catch (e) {
      console.error("❌ Error saving threshold:", e);
    }
  } else {
    console.log("✅ Console threshold is already appropriate:", currentThreshold);
  }
  
  console.log("=== CONSOLE THRESHOLD FIX COMPLETE ===");
};

// Function to set manual threshold override (overrides ALL other thresholds)
window.setManualOverride = function(threshold) {
  console.log("=== SETTING MANUAL THRESHOLD OVERRIDE ===");
  
  if (threshold === null || threshold === undefined) {
    window.clearManualThresholdOverride();
    console.log("✅ Manual threshold override disabled");
  } else {
    const num = parseFloat(threshold);
    if (!isNaN(num) && num >= 0) {
      window.setManualThresholdOverride(num);
      console.log("✅ Manual threshold override set to:", num);
      console.log("ℹ️ This will override ALL other threshold settings");
    } else {
      console.error("❌ Invalid threshold value:", threshold);
    }
  }
  
  console.log("=== MANUAL OVERRIDE SETTING COMPLETE ===");
};

// Remove all banner-related code. Only keep silent, debounced recovery logic on visibility change.
let _catchupDebounce = false;
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    if (_catchupDebounce) return;
    _catchupDebounce = true;
    // Only trigger recovery logic as before (existing logic will run)
    setTimeout(() => { _catchupDebounce = false; }, 3000);
  }
});

// export default WebSocketManager;
