/**
 * DOM Cache
 * Caches and manages DOM element lookups for performance
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  "use strict";

  const logger = window.logger
    ? window.logger.createLogger("DOMCache")
    : console;

  /**
   * DOM Cache class for efficient element lookups
   */
  class DOMCache {
    /**
     * Create a new DOM cache
     */
    constructor() {
      this.elements = new Map();
      this.selectors = new Map();
      this.mutationObserver = null;
      this.setupMutationObserver();
    }

    /**
     * Get an element by ID
     * @param {string} id - Element ID
     * @returns {HTMLElement|null} - The element or null if not found
     */
    getElementById(id) {
      if (!id) return null;

      // Return from cache if exists
      if (this.elements.has(id)) {
        const element = this.elements.get(id);

        // Verify element is still in DOM
        if (element && document.contains(element)) {
          return element;
        }

        // Element no longer in DOM, remove from cache
        this.elements.delete(id);
      }

      // Look up element
      const element = document.getElementById(id);

      // Cache if found
      if (element) {
        this.elements.set(id, element);
      }

      return element;
    }

    /**
     * Get elements by selector
     * @param {string} selector - CSS selector
     * @param {boolean} [forceRefresh=false] - Whether to force a refresh
     * @returns {Array<HTMLElement>} - Array of elements
     */
    querySelectorAll(selector, forceRefresh = false) {
      if (!selector) return [];

      // Return from cache if exists and not forcing refresh
      if (!forceRefresh && this.selectors.has(selector)) {
        const elements = this.selectors.get(selector);

        // Verify all elements are still in DOM
        if (elements.every((el) => document.contains(el))) {
          return [...elements]; // Return copy to prevent mutation
        }

        // Some elements no longer in DOM, remove from cache
        this.selectors.delete(selector);
      }

      // Look up elements
      const elements = Array.from(document.querySelectorAll(selector));

      // Cache result
      this.selectors.set(selector, elements);

      return [...elements]; // Return copy to prevent mutation
    }

    /**
     * Get first element matching selector
     * @param {string} selector - CSS selector
     * @param {boolean} [forceRefresh=false] - Whether to force a refresh
     * @returns {HTMLElement|null} - The element or null if not found
     */
    querySelector(selector, forceRefresh = false) {
      if (!selector) return null;

      // Use cached selector results if available
      const elements = this.querySelectorAll(selector, forceRefresh);
      return elements.length > 0 ? elements[0] : null;
    }

    /**
     * Create element with attributes and properties
     * @param {string} tagName - HTML tag name
     * @param {Object} [options={}] - Element options
     * @param {Object} [options.attributes={}] - HTML attributes
     * @param {Object} [options.properties={}] - Element properties
     * @param {Object} [options.dataset={}] - Dataset attributes
     * @param {Object} [options.style={}] - Style properties
     * @param {Array|HTMLElement|string} [options.children] - Child elements
     * @param {Object} [options.events={}] - Event listeners
     * @returns {HTMLElement} - The created element
     */
    createElement(tagName, options = {}) {
      const element = document.createElement(tagName);

      // Set attributes
      if (options.attributes) {
        Object.entries(options.attributes).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element.setAttribute(key, value);
          }
        });
      }

      // Set properties
      if (options.properties) {
        Object.entries(options.properties).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element[key] = value;
          }
        });
      }

      // Set dataset
      if (options.dataset) {
        Object.entries(options.dataset).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element.dataset[key] = value;
          }
        });
      }

      // Set style
      if (options.style) {
        Object.entries(options.style).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            element.style[key] = value;
          }
        });
      }

      // Add event listeners
      if (options.events) {
        Object.entries(options.events).forEach(([event, handler]) => {
          if (typeof handler === "function") {
            element.addEventListener(event, handler);
          }
        });
      }

      // Add children
      if (options.children) {
        if (Array.isArray(options.children)) {
          options.children.forEach((child) => {
            if (child) {
              if (typeof child === "string") {
                element.appendChild(document.createTextNode(child));
              } else if (child instanceof Node) {
                element.appendChild(child);
              }
            }
          });
        } else if (typeof options.children === "string") {
          element.textContent = options.children;
        } else if (options.children instanceof Node) {
          element.appendChild(options.children);
        }
      }

      return element;
    }

    /**
     * Set up mutation observer to invalidate cache when DOM changes
     * @private
     */
    setupMutationObserver() {
      // Skip if not supported
      if (!window.MutationObserver) return;

      this.mutationObserver = new MutationObserver((mutations) => {
        let shouldInvalidateSelectors = false;

        for (const mutation of mutations) {
          // For removed nodes
          if (mutation.removedNodes.length > 0) {
            for (const node of mutation.removedNodes) {
              if (node.id && this.elements.has(node.id)) {
                this.elements.delete(node.id);
              }

              // If structural change, invalidate selectors
              if (node.nodeType === Node.ELEMENT_NODE) {
                shouldInvalidateSelectors = true;
              }
            }
          }

          // For added nodes with IDs
          if (mutation.addedNodes.length > 0) {
            shouldInvalidateSelectors = true;
          }

          // For attribute changes on ID
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "id"
          ) {
            const target = mutation.target;
            const oldId = mutation.oldValue;
            const newId = target.id;

            if (oldId && this.elements.has(oldId)) {
              this.elements.delete(oldId);
              if (newId) {
                this.elements.set(newId, target);
              }
            }

            shouldInvalidateSelectors = true;
          }
        }

        // Clear selector cache if needed
        if (shouldInvalidateSelectors) {
          this.selectors.clear();
        }
      });

      // Start observing
      this.mutationObserver.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["id"],
        attributeOldValue: true,
      });
    }

    /**
     * Clear the cache
     */
    clear() {
      this.elements.clear();
      this.selectors.clear();
    }

    /**
     * Clean up resources
     */
    destroy() {
      if (this.mutationObserver) {
        this.mutationObserver.disconnect();
        this.mutationObserver = null;
      }

      this.clear();
    }
  }

  // Create global instance
  window.domCache = new DOMCache();

  // Add shorthand methods to window
  window.$id = (id) => window.domCache.getElementById(id);
  window.$query = (selector, forceRefresh) =>
    window.domCache.querySelector(selector, forceRefresh);
  window.$queryAll = (selector, forceRefresh) =>
    window.domCache.querySelectorAll(selector, forceRefresh);
  window.$create = (tagName, options) =>
    window.domCache.createElement(tagName, options);
})();
