/**
 * Exchange WebSocket Manager
 * Extends BaseWebSocketManager with exchange-specific functionality
 */

(function() {
  'use strict';

  /**
   * Exchange WebSocket Manager with channel subscription capabilities
   * @extends BaseWebSocketManager
   */
  class ExchangeWebSocketManager extends BaseWebSocketManager {
    /**
     * Creates a new exchange WebSocket manager
     * @param {Object} options - Configuration options
     * @param {string} options.exchange - Exchange name
     * @param {Object} options.subscribeFormat - Message format for subscriptions
     * @param {Object} options.unsubscribeFormat - Message format for unsubscriptions
     * @param {Function} options.channelExtractor - Function to extract channel from message
     */
    constructor(options) {
      const defaults = {
        exchange: 'unknown',
        subscribeFormat: { event: 'subscribe', data: { channel: '{channel}' } },
        unsubscribeFormat: { event: 'unsubscribe', data: { channel: '{channel}' } },
        channelExtractor: (msg) => msg.channel || null
      };

      super(Object.assign({}, defaults, options, {
        name: options.name || `${options.exchange || defaults.exchange}Manager`
      }));

      // Channel subscriptions
      this.channelHandlers = {};
      this.pendingSubscriptions = new Set();
      this.activeChannels = new Set();

      // Pair-specific subscriptions
      this.pairSubscriptions = new Map();
    }

    /**
     * Format a message for the specific exchange
     * @param {Object} template - Message template
     * @param {Object} params - Parameters to substitute
     * @returns {Object} - Formatted message
     * @private
     */
    formatMessage(template, params) {
      const result = JSON.parse(JSON.stringify(template)); // Deep clone

      // Replace placeholders in string values
      const replacePlaceholders = (obj) => {
        for (const key in obj) {
          if (typeof obj[key] === 'string') {
            // Replace {placeholder} with actual values
            obj[key] = obj[key].replace(/\{([^}]+)\}/g, (match, placeholder) => {
              return params[placeholder] !== undefined ? params[placeholder] : match;
            });
          } else if (typeof obj[key] === 'object' && obj[key] !== null) {
            replacePlaceholders(obj[key]);
          }
        }
      };

      replacePlaceholders(result);
      return result;
    }

    /**
     * Called after connection is established
     * @param {Event} event - WebSocket open event
     * @private
     */
    onConnected(event) {
      // Resubscribe to all active channels
      this.resubscribeAll();
    }

    /**
     * Subscribe to a channel
     * @param {string} channel - Channel name
     * @param {Function} handler - Message handler for this channel
     * @param {string} [pair] - Optional trading pair for this subscription
     * @returns {boolean} - Whether the subscription was successful
     */
    subscribe(channel, handler, pair = null) {
      // Add to handlers
      if (!this.channelHandlers[channel]) {
        this.channelHandlers[channel] = new Set();
      }

      if (handler && typeof handler === 'function') {
        this.channelHandlers[channel].add(handler);
      }

      // Track pair-specific subscriptions
      if (pair) {
        if (!this.pairSubscriptions.has(pair)) {
          this.pairSubscriptions.set(pair, new Set());
        }
        this.pairSubscriptions.get(pair).add(channel);
      }

      // Add to active channels
      this.activeChannels.add(channel);

      // Send subscription message if connected
      if (this.connected) {
        this.logger.info(`Adding subscription for ${channel}`);
        this.pendingSubscriptions.add(channel);

        const subscribeMsg = this.formatMessage(this.options.subscribeFormat, { channel });
        const success = this.send(subscribeMsg);

        if (!success) {
          this.pendingSubscriptions.delete(channel);
        }

        return success;
      } else {
        this.logger.info(`Queuing subscription for ${channel} until connected`);
        return true;
      }
    }

    /**
     * Unsubscribe from a channel
     * @param {string} channel - Channel name
     * @param {Function} [handler] - Optional specific handler to remove
     * @returns {boolean} - Whether the unsubscription was successful
     */
    unsubscribe(channel, handler = null) {
      // Remove specific handler if provided
      if (handler && this.channelHandlers[channel]) {
        this.channelHandlers[channel].delete(handler);

        // If handlers remain, don't unsubscribe from the channel
        if (this.channelHandlers[channel].size > 0) {
          return true;
        }
      }

      // Remove from active channels
      this.activeChannels.delete(channel);

      // Remove from handlers
      delete this.channelHandlers[channel];

      // Remove from pending subscriptions if it's there
      this.pendingSubscriptions.delete(channel);

      // Remove from all pair subscriptions
      for (const [pair, channels] of this.pairSubscriptions.entries()) {
        channels.delete(channel);
        if (channels.size === 0) {
          this.pairSubscriptions.delete(pair);
        }
      }

      // Send unsubscription message if connected
      if (this.connected) {
        this.logger.info(`Unsubscribing from ${channel}`);
        const unsubscribeMsg = this.formatMessage(this.options.unsubscribeFormat, { channel });
        return this.send(unsubscribeMsg);
      }

      return true;
    }

    /**
     * Unsubscribe from all channels for a specific pair
     * @param {string} pair - Trading pair
     * @returns {Array} - Channels that were unsubscribed
     */
    unsubscribeAllForPair(pair) {
      if (!this.pairSubscriptions.has(pair)) {
        return [];
      }

      const channels = Array.from(this.pairSubscriptions.get(pair));
      this.logger.info(`Unsubscribing from ${channels.length} channels for pair ${pair}`);

      channels.forEach(channel => this.unsubscribe(channel));
      this.pairSubscriptions.delete(pair);

      return channels;
    }

    /**
     * Resubscribe to all active channels
     * @returns {boolean} - Whether the resubscription was successful
     */
    resubscribeAll() {
      if (!this.connected) {
        this.logger.warn('Cannot resubscribe: not connected');
        return false;
      }

      const channels = Array.from(this.activeChannels);
      this.logger.info(`Resubscribing to ${channels.length} channels`);

      let success = true;
      channels.forEach(channel => {
        const subscribeMsg = this.formatMessage(this.options.subscribeFormat, { channel });
        this.pendingSubscriptions.add(channel);
        if (!this.send(subscribeMsg)) {
          success = false;
          this.pendingSubscriptions.delete(channel);
        }
      });

      return success;
    }

    /**
     * Resubscribe to all channels for a specific pair
     * @param {string} pair - Trading pair
     * @returns {boolean} - Whether the resubscription was successful
     */
    resubscribeAllForPair(pair) {
      if (!this.pairSubscriptions.has(pair)) {
        this.logger.info(`No subscriptions found for pair ${pair}, skipping resubscription`);
        return false;
      }

      if (!this.connected) {
        this.logger.warn('Cannot resubscribe: not connected');
        return false;
      }

      const channels = Array.from(this.pairSubscriptions.get(pair));
      this.logger.info(`Resubscribing to ${channels.length} channels for pair ${pair}`);

      let success = true;
      channels.forEach(channel => {
        const subscribeMsg = this.formatMessage(this.options.subscribeFormat, { channel });
        this.pendingSubscriptions.add(channel);
        if (!this.send(subscribeMsg)) {
          success = false;
          this.pendingSubscriptions.delete(channel);
        }
      });

      return success;
    }

    /**
     * Process incoming messages
     * @param {Object} data - Parsed message data
     * @param {MessageEvent} event - Original WebSocket message event
     * @returns {Object} - Processed message data or null to stop processing
     * @protected
     */
    processMessage(data, event) {
      // Extract channel using the configured extractor
      const channel = this.options.channelExtractor(data);

      // Handle subscription confirmations
      if (data.event === 'subscribed' || data.event === 'bts:subscription_succeeded') {
        const subscribedChannel = data.channel || (data.subscription ? data.subscription.name : null);
        if (subscribedChannel) {
          this.logger.info(`Successfully subscribed to ${subscribedChannel}`);
          this.pendingSubscriptions.delete(subscribedChannel);
        }
      }

      // Handle unsubscription confirmations
      if (data.event === 'unsubscribed' || data.event === 'bts:unsubscription_succeeded') {
        const unsubscribedChannel = data.channel || (data.subscription ? data.subscription.name : null);
        if (unsubscribedChannel) {
          this.logger.info(`Successfully unsubscribed from ${unsubscribedChannel}`);
        }
      }

      // If no channel is found, just pass the message through
      if (!channel) {
        return data;
      }

      // If we have handlers for this channel, call them
      if (this.channelHandlers[channel]) {
        this.channelHandlers[channel].forEach(handler => {
          try {
            handler(data, this);
          } catch (error) {
            this.logger.error(`Error in channel handler for ${channel}`, error);
          }
        });
      }

      // Continue normal processing
      return data;
    }

    /**
     * Check if a channel is active
     * @param {string} channel - Channel name
     * @returns {boolean} - Whether the channel is active
     */
    isSubscribed(channel) {
      return this.activeChannels.has(channel);
    }

    /**
     * Get all active channels
     * @returns {Array} - List of active channels
     */
    getActiveChannels() {
      return Array.from(this.activeChannels);
    }

    /**
     * Get all pending subscriptions
     * @returns {Array} - List of pending channels
     */
    getPendingSubscriptions() {
      return Array.from(this.pendingSubscriptions);
    }

    /**
     * Clean up all resources used by this manager
     */
    destroy() {
      // Unsubscribe from all channels
      const channels = Array.from(this.activeChannels);
      channels.forEach(channel => this.unsubscribe(channel));

      // Clear pair subscriptions
      this.pairSubscriptions.clear();

      // Call parent destroy method
      super.destroy();
    }
  }

  // Expose the class
  window.ExchangeWebSocketManager = ExchangeWebSocketManager;
})();
