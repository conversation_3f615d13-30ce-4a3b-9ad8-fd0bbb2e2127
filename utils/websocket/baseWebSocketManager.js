/**
 * Base WebSocket Manager
 * Provides unified connection management, error handling, and reconnection logic
 */

(function() {
  'use strict';

  class BaseWebSocketManager {
    /**
     * Creates a new WebSocket manager
     * @param {Object} options - Configuration options
     * @param {string} options.url - WebSocket URL
     * @param {string} options.name - Manager name for logging
     * @param {Function} options.onMessage - Message handler function
     * @param {number} options.reconnectBaseDelay - Base delay for reconnection in ms (default: 1000)
     * @param {number} options.reconnectMaxDelay - Maximum delay for reconnection in ms (default: 30000)
     * @param {number} options.maxReconnectAttempts - Maximum number of reconnection attempts (default: Infinity)
     * @param {number} options.pingInterval - Interval for sending ping messages in ms (default: 30000)
     * @param {string} options.pingMessage - Ping message to send (default: 'ping')
     * @param {number} options.connectionTimeout - Connection timeout in ms (default: 10000)
     */
    constructor(options) {
      this.options = Object.assign({
        url: null,
        name: 'WebSocket',
        onMessage: null,
        reconnectBaseDelay: 1000,
        reconnectMaxDelay: 30000,
        maxReconnectAttempts: Infinity,
        pingInterval: 30000,
        pingMessage: 'ping',
        connectionTimeout: 10000
      }, options);

      // Validate required options
      if (!this.options.url) {
        throw new Error('WebSocket URL is required');
      }

      // Create logger for this instance
      this.logger = window.logger ? window.logger.createLogger(this.options.name) : {
        debug: console.debug.bind(console),
        info: console.log.bind(console),
        warn: console.warn.bind(console),
        error: console.error.bind(console)
      };

      // Connection state
      this.ws = null;
      this.connected = false;
      this.connecting = false;
      this.reconnectAttempts = 0;
      this.reconnectDelay = this.options.reconnectBaseDelay;
      this.reconnectTimer = null;
      this.pingTimer = null;
      this.lastMessageTime = 0;
      this.lastPongTime = 0;
      this.connectionTimeoutTimer = null;

      // Handlers storage
      this.subscriptions = {};
      this.messageHandlers = new Set();

      // Visibility change tracking
      this.wasConnectedBeforeHidden = false;

      // Bind methods to preserve 'this' context
      this.connect = this.connect.bind(this);
      this.disconnect = this.disconnect.bind(this);
      this.onOpen = this.onOpen.bind(this);
      this.onClose = this.onClose.bind(this);
      this.onError = this.onError.bind(this);
      this.onMessage = this.onMessage.bind(this);
      this.reconnect = this.reconnect.bind(this);
      this.ping = this.ping.bind(this);
      this.handleVisibilityChange = this.handleVisibilityChange.bind(this);

      // Set up visibility change handler
      document.addEventListener('visibilitychange', this.handleVisibilityChange);

      // Register with connection manager if available
      if (window.connectionManager && typeof window.connectionManager.registerManager === 'function') {
        window.connectionManager.registerManager(this.options.name, this);
      }
    }

    /**
     * Connect to the WebSocket server
     * @returns {Promise} - Resolves when connected, rejects on failure
     */
    connect() {
      return new Promise((resolve, reject) => {
        if (this.connected) {
          resolve(this.ws);
          return;
        }

        if (this.connecting) {
          reject(new Error('Connection already in progress'));
          return;
        }

        this.connecting = true;
        this.logger.info(`Connecting to ${this.options.url}`);

        try {
          this.ws = new WebSocket(this.options.url);
          this.ws.binaryType = 'arraybuffer';

          // Set up connection timeout
          this.connectionTimeoutTimer = setTimeout(() => {
            if (!this.connected && this.connecting) {
              this.logger.warn(`Connection timeout after ${this.options.connectionTimeout}ms`);
              this.ws.close();
              this.connecting = false;
              reject(new Error('Connection timeout'));
            }
          }, this.options.connectionTimeout);

          this.ws.onopen = (event) => {
            clearTimeout(this.connectionTimeoutTimer);
            this.onOpen(event);
            resolve(this.ws);
          };

          this.ws.onclose = this.onClose;
          this.ws.onerror = (error) => {
            clearTimeout(this.connectionTimeoutTimer);
            this.onError(error);
            reject(error);
          };

          this.ws.onmessage = this.onMessage;
        } catch (error) {
          clearTimeout(this.connectionTimeoutTimer);
          this.connecting = false;
          this.logger.error('Failed to create WebSocket connection', error);
          reject(error);
        }
      });
    }

    /**
     * Disconnect from the WebSocket server
     * @param {number} code - Close code
     * @param {string} reason - Close reason
     */
    disconnect(code = 1000, reason = 'Normal closure') {
      this.logger.info(`Disconnecting with code ${code}: ${reason}`);

      // Clear timers
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      if (this.pingTimer) {
        clearInterval(this.pingTimer);
        this.pingTimer = null;
      }

      if (this.connectionTimeoutTimer) {
        clearTimeout(this.connectionTimeoutTimer);
        this.connectionTimeoutTimer = null;
      }

      // Close connection if it exists
      if (this.ws) {
        try {
          this.ws.onopen = null;
          this.ws.onclose = null;
          this.ws.onerror = null;
          this.ws.onmessage = null;

          if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close(code, reason);
          }
        } catch (error) {
          this.logger.error('Error closing WebSocket', error);
        }

        this.ws = null;
      }

      this.connected = false;
      this.connecting = false;
    }

    /**
     * Reconnect to the WebSocket server with exponential backoff
     */
    reconnect() {
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
        this.logger.error(`Maximum reconnection attempts (${this.options.maxReconnectAttempts}) reached`);
        return;
      }

      this.reconnectAttempts++;
      this.logger.info(`Reconnecting (attempt ${this.reconnectAttempts})... Waiting ${this.reconnectDelay}ms`);

      // Clean up existing connection
      this.disconnect(1000, 'Reconnecting');

      // Schedule reconnection
      this.reconnectTimer = setTimeout(() => {
        this.connect().catch(error => {
          this.logger.error('Reconnection attempt failed', error);

          // Calculate next reconnect delay with exponential backoff
          this.reconnectDelay = Math.min(
            this.reconnectDelay * 1.5, 
            this.options.reconnectMaxDelay
          );

          this.reconnect();
        });
      }, this.reconnectDelay);
    }

    /**
     * Send a ping message to keep the connection alive
     */
    ping() {
      if (!this.connected || !this.ws) return;

      try {
        this.send(this.options.pingMessage);
        this.logger.debug('Ping sent');
      } catch (error) {
        this.logger.error('Error sending ping', error);
      }
    }

    /**
     * Send data through the WebSocket
     * @param {string|Object} data - Data to send
     * @returns {boolean} - Whether the send was successful
     */
    send(data) {
      if (!this.connected || !this.ws) {
        this.logger.warn('Cannot send message: not connected');
        return false;
      }

      try {
        const message = typeof data === 'object' ? JSON.stringify(data) : data;
        this.ws.send(message);
        return true;
      } catch (error) {
        this.logger.error('Error sending message', error);
        return false;
      }
    }

    /**
     * Add a message handler
     * @param {Function} handler - Message handler function
     */
    addMessageHandler(handler) {
      if (typeof handler !== 'function') {
        throw new Error('Message handler must be a function');
      }

      this.messageHandlers.add(handler);
    }

    /**
     * Remove a message handler
     * @param {Function} handler - Message handler function to remove
     */
    removeMessageHandler(handler) {
      this.messageHandlers.delete(handler);
    }

    /**
     * Handle WebSocket open event
     * @param {Event} event - WebSocket open event
     * @private
     */
    onOpen(event) {
      this.connected = true;
      this.connecting = false;
      this.reconnectAttempts = 0;
      this.reconnectDelay = this.options.reconnectBaseDelay;
      this.lastMessageTime = Date.now();
      this.lastPongTime = Date.now();

      this.logger.info('Connected successfully');

      // Start ping interval
      if (this.options.pingInterval > 0) {
        this.pingTimer = setInterval(this.ping, this.options.pingInterval);
      }

      // Dispatch event
      if (window.eventBus) {
        window.eventBus.emit(`ws-connected-${this.options.name}`, { 
          manager: this.options.name,
          timestamp: Date.now() 
        });
      }

      // Implement in subclasses: onConnected()
      if (typeof this.onConnected === 'function') {
        this.onConnected(event);
      }
    }

    /**
     * Handle WebSocket close event
     * @param {CloseEvent} event - WebSocket close event
     * @private
     */
    onClose(event) {
      const wasConnected = this.connected;
      this.connected = false;
      this.connecting = false;

      if (this.pingTimer) {
        clearInterval(this.pingTimer);
        this.pingTimer = null;
      }

      this.logger.warn(`Connection closed: ${event.code} - ${event.reason}`);

      // Dispatch event
      if (window.eventBus) {
        window.eventBus.emit(`ws-disconnected-${this.options.name}`, {
          manager: this.options.name,
          code: event.code,
          reason: event.reason,
          wasConnected,
          timestamp: Date.now()
        });
      }

      // Implement in subclasses: onDisconnected()
      if (typeof this.onDisconnected === 'function') {
        this.onDisconnected(event);
      }

      // Reconnect if this wasn't a normal closure
      if (wasConnected && event.code !== 1000) {
        this.reconnect();
      }
    }

    /**
     * Handle WebSocket error event
     * @param {Event} event - WebSocket error event
     * @private
     */
    onError(event) {
      this.logger.error('WebSocket error', event);

      // Dispatch event
      if (window.eventBus) {
        window.eventBus.emit(`ws-error-${this.options.name}`, {
          manager: this.options.name,
          error: event,
          timestamp: Date.now()
        });
      }
    }

    /**
     * Handle WebSocket message event
     * @param {MessageEvent} event - WebSocket message event
     * @private
     */
    onMessage(event) {
      this.lastMessageTime = Date.now();

      let parsedData;
      try {
        // Try to parse as JSON if it's a string
        if (typeof event.data === 'string') {
          parsedData = JSON.parse(event.data);
        } else {
          parsedData = event.data; // Binary data
        }
      } catch (error) {
        // Not JSON or parsing error, use raw data
        parsedData = event.data;
        this.logger.debug('Non-JSON message received');
      }

      // Handle pong message
      if (parsedData === 'pong' || 
          (parsedData && parsedData.type === 'pong') ||
          (parsedData && parsedData.op === 'pong')) {
        this.lastPongTime = Date.now();
        this.logger.debug('Pong received');
        return;
      }

      // Handle custom message processing in subclasses
      if (typeof this.processMessage === 'function') {
        parsedData = this.processMessage(parsedData, event);

        // If processMessage returns null/undefined, stop processing
        if (parsedData === null || parsedData === undefined) {
          return;
        }
      }

      // Use custom onMessage handler if provided
      if (typeof this.options.onMessage === 'function') {
        this.options.onMessage(parsedData, this);
      }

      // Call all registered message handlers
      this.messageHandlers.forEach(handler => {
        try {
          handler(parsedData, this);
        } catch (error) {
          this.logger.error('Error in message handler', error);
        }
      });
    }

    /**
     * Handle visibility change events to manage reconnection
     * @private
     */
    handleVisibilityChange() {
      if (document.visibilityState === 'hidden') {
        // Save current connection state
        this.wasConnectedBeforeHidden = this.connected;
        this.logger.debug('Tab hidden, connection state saved');
      } else if (document.visibilityState === 'visible') {
        // Check if we need to reconnect
        if (this.wasConnectedBeforeHidden && !this.connected && !this.connecting) {
          this.logger.info('Tab visible again, reconnecting...');
          this.reconnect();
        } else if (this.connected) {
          // We're still connected, just verify the connection with a ping
          this.ping();

          // Dispatch visibility restored event
          if (window.eventBus) {
            window.eventBus.emit(`ws-visibility-restored-${this.options.name}`, {
              manager: this.options.name,
              connectionState: this.getConnectionState(),
              timestamp: Date.now()
            });
          }
        }
      }
    }

    /**
     * Check if the connection is healthy
     * @returns {boolean} - Whether the connection is healthy
     */
    isHealthy() {
      if (!this.connected) return false;

      // Check if we've received any messages recently
      const now = Date.now();
      const lastMessageAge = now - this.lastMessageTime;

      // If we haven't received any messages in 2x ping interval, the connection may be stale
      return lastMessageAge < (this.options.pingInterval * 2);
    }

    /**
     * Get the current connection state
     * @returns {Object} - Connection state information
     */
    getConnectionState() {
      return {
        connected: this.connected,
        connecting: this.connecting,
        readyState: this.ws ? this.ws.readyState : -1,
        lastMessageTime: this.lastMessageTime,
        lastPongTime: this.lastPongTime,
        reconnectAttempts: this.reconnectAttempts,
        reconnectDelay: this.reconnectDelay,
        healthy: this.isHealthy(),
        handlerCount: this.messageHandlers.size,
        subscriptions: Object.keys(this.subscriptions)
      };
    }

    /**
     * Clean up all resources used by this manager
     */
    destroy() {
      this.logger.info('Destroying WebSocket manager');

      // Remove event listeners
      document.removeEventListener('visibilitychange', this.handleVisibilityChange);

      // Disconnect WebSocket
      this.disconnect(1000, 'Manager destroyed');

      // Clear all handlers
      this.messageHandlers.clear();
      this.subscriptions = {};

      // Unregister from connection manager
      if (window.connectionManager && typeof window.connectionManager.unregisterManager === 'function') {
        window.connectionManager.unregisterManager(this.options.name);
      }
    }
  }

  // Expose the class
  window.BaseWebSocketManager = BaseWebSocketManager;
})();
