// Bybit Open Interest REST Poller
// Polls Bybit REST API for open interest and updates global state

const BYBIT_OI_URL = 'https://api.bybit.com/v5/market/open-interest?category=linear&symbol=BTCUSDT&period=5';
const POLL_INTERVAL_MS = 10000; // 10 seconds

function fetchBybitOpenInterest() {
  fetch(BYBIT_OI_URL)
    .then(res => res.json())
    .then(data => {
      if (data && data.result && Array.isArray(data.result.list)) {
        // Each entry: { openInterest, timestamp }
        const oiBars = data.result.list.map(bar => ({
          time: Math.floor(Number(bar.timestamp) / 1000),
          openInterest: parseFloat(bar.openInterest)
        }));
        if (oiBars.length === 0) {
          // No OI data, do not update global state or log
          return;
        }
        // Update global state
        window.PS = window.PS || {};
        window.PS.bybitOpenInterestBars = oiBars;
        // Optionally, log only if data is present
        // console.log('[Bybit OI REST] Updated', oiBars.slice(-3));
      } else {
        console.warn('[Bybit OI REST] Unexpected response', data);
      }
    })
    .catch(err => console.error('[Bybit OI REST] Fetch error', err));
}

// Start polling
document.addEventListener('DOMContentLoaded', () => {
  fetchBybitOpenInterest();
  setInterval(fetchBybitOpenInterest, POLL_INTERVAL_MS);
});

// Optionally, expose for manual refresh
document.fetchBybitOpenInterest = fetchBybitOpenInterest;
