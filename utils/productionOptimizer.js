/**
 * Production Optimizer
 * Automatically optimizes performance and removes debug code for production deployment
 * Implements the quick wins from the optimization analysis
 */

(function() {
  'use strict';

  // Detect production environment
  const IS_PRODUCTION = window.location.hostname !== 'localhost' &&
                       window.location.hostname !== '127.0.0.1' &&
                       !window.location.hostname.includes('dev');

  const IS_DEBUG_MODE = localStorage.getItem('debugMode') === 'true' ||
                       window.location.search.includes('debug=true');

  // Production logger that no-ops debug statements
  const createProductionLogger = (moduleName) => {
    const noop = () => {};

    return {
      debug: IS_PRODUCTION && !IS_DEBUG_MODE ? noop : console.debug.bind(console, `[${moduleName}]`),
      info: IS_PRODUCTION && !IS_DEBUG_MODE ? noop : console.info.bind(console, `[${moduleName}]`),
      warn: console.warn.bind(console, `[${moduleName}]`),
      error: console.error.bind(console, `[${moduleName}]`),
      time: IS_PRODUCTION && !IS_DEBUG_MODE ? noop : console.time.bind(console),
      timeEnd: IS_PRODUCTION && !IS_DEBUG_MODE ? noop : console.timeEnd.bind(console)
    };
  };

  // Override console methods globally for production
  if (IS_PRODUCTION && !IS_DEBUG_MODE) {
    const originalConsole = { ...console };

    console.debug = () => {};
    console.info = () => {};
    console.time = () => {};
    console.timeEnd = () => {};

    // Keep warn and error for production issues
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
    console.log = originalConsole.log;
  }

  // Memory management utilities
  class ProductionMemoryManager {
    constructor() {
      this.intervals = new Set();
      this.timeouts = new Set();
      this.eventListeners = new Map();
      this.observables = new Set();
      this.isCleaningUp = false;
    }

    // Wrap setInterval to track for cleanup
    setInterval(callback, delay) {
      const id = setInterval(callback, delay);
      this.intervals.add(id);
      return id;
    }

    // Wrap setTimeout to track for cleanup
    setTimeout(callback, delay) {
      const id = setTimeout(() => {
        callback();
        this.timeouts.delete(id);
      }, delay);
      this.timeouts.add(id);
      return id;
    }

    // Enhanced addEventListener with automatic cleanup tracking
    addEventListener(element, event, handler, options = {}) {
      const key = `${element.constructor.name}_${event}_${Date.now()}`;

      element.addEventListener(event, handler, options);

      this.eventListeners.set(key, {
        element,
        event,
        handler,
        options
      });

      // Return cleanup function
      return () => {
        element.removeEventListener(event, handler, options);
        this.eventListeners.delete(key);
      };
    }

    // Register observable for cleanup
    addObservable(observable) {
      if (observable && typeof observable.disconnect === 'function') {
        this.observables.add(observable);
      }
    }

    // Force garbage collection if available
    forceGC() {
      if (window.gc && typeof window.gc === 'function') {
        try {
          window.gc();
        } catch (e) {
          // gc() not available in this environment
        }
      }
    }

    // Comprehensive cleanup
    cleanup() {
      if (this.isCleaningUp) return;
      this.isCleaningUp = true;

      // Clear intervals
      this.intervals.forEach(id => {
        clearInterval(id);
      });
      this.intervals.clear();

      // Clear timeouts
      this.timeouts.forEach(id => {
        clearTimeout(id);
      });
      this.timeouts.clear();

      // Remove event listeners
      this.eventListeners.forEach(({ element, event, handler, options }) => {
        try {
          element.removeEventListener(event, handler, options);
        } catch (e) {
          // Element might have been removed from DOM
        }
      });
      this.eventListeners.clear();

      // Disconnect observables
      this.observables.forEach(observable => {
        try {
          observable.disconnect();
        } catch (e) {
          // Observable might already be disconnected
        }
      });
      this.observables.clear();

      // Force garbage collection
      setTimeout(() => {
        this.forceGC();
        this.isCleaningUp = false;
      }, 100);
    }

    // Get memory statistics
    getStats() {
      return {
        intervals: this.intervals.size,
        timeouts: this.timeouts.size,
        eventListeners: this.eventListeners.size,
        observables: this.observables.size,
        heapUsed: performance.memory ?
          Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : null,
        heapTotal: performance.memory ?
          Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) : null
      };
    }
  }

  // Performance monitoring for production
  class ProductionPerformanceMonitor {
    constructor() {
      this.metrics = {
        frameRates: [],
        memoryUsage: [],
        errorCount: 0,
        startTime: Date.now()
      };

      this.initializeMonitoring();
    }

    initializeMonitoring() {
      // Frame rate monitoring
      let frameCount = 0;
      let lastTime = Date.now();

      const measureFrameRate = () => {
        frameCount++;
        const now = Date.now();

        if (now - lastTime >= 1000) {
          const fps = frameCount * 1000 / (now - lastTime);
          this.metrics.frameRates.push({
            fps: Math.round(fps * 10) / 10,
            timestamp: now
          });

          // Keep only last 60 measurements (1 minute)
          if (this.metrics.frameRates.length > 60) {
            this.metrics.frameRates.shift();
          }

          // Alert on consistently low FPS
          if (this.metrics.frameRates.length >= 5) {
            const recentFPS = this.metrics.frameRates.slice(-5);
            const avgFPS = recentFPS.reduce((sum, f) => sum + f.fps, 0) / 5;

            if (avgFPS < 20) {
              this.reportPerformanceIssue('low-fps', { averageFPS: avgFPS });
            }
          }

          frameCount = 0;
          lastTime = now;
        }

        requestAnimationFrame(measureFrameRate);
      };

      measureFrameRate();

      // Memory monitoring
      if (performance.memory) {
        setInterval(() => {
          const memInfo = {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            timestamp: Date.now()
          };

          this.metrics.memoryUsage.push(memInfo);

          // Keep only last 120 measurements (1 hour at 30s intervals)
          if (this.metrics.memoryUsage.length > 120) {
            this.metrics.memoryUsage.shift();
          }

          // Alert on high memory usage
          if (memInfo.used > 200) {
            this.reportPerformanceIssue('high-memory', memInfo);
          }

        }, 30000); // Every 30 seconds
      }

      // Error monitoring
      window.addEventListener('error', () => {
        this.metrics.errorCount++;
      });

      window.addEventListener('unhandledrejection', () => {
        this.metrics.errorCount++;
      });
    }

    reportPerformanceIssue(type, data) {
      console.warn(`[Performance Issue] ${type}:`, data);

      // Could send to analytics service in production
      if (window.productionAnalytics) {
        window.productionAnalytics.track('performance_issue', {
          type,
          data,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href
        });
      }
    }

    getReport() {
      const now = Date.now();
      const uptime = now - this.metrics.startTime;

      const currentFPS = this.metrics.frameRates.length > 0 ?
        this.metrics.frameRates[this.metrics.frameRates.length - 1].fps : 0;

      const avgFPS = this.metrics.frameRates.length > 0 ?
        this.metrics.frameRates.reduce((sum, f) => sum + f.fps, 0) / this.metrics.frameRates.length : 0;

      const currentMemory = this.metrics.memoryUsage.length > 0 ?
        this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1] : null;

      return {
        uptime: Math.round(uptime / 1000),
        performance: {
          currentFPS: Math.round(currentFPS * 10) / 10,
          averageFPS: Math.round(avgFPS * 10) / 10,
          memory: currentMemory
        },
        errors: this.metrics.errorCount,
        status: this.getHealthStatus()
      };
    }

    getHealthStatus() {
      const report = this.getReport();

      if (report.performance.currentFPS < 15) return 'critical';
      if (report.performance.currentFPS < 25) return 'warning';
      if (report.performance.memory && report.performance.memory.used > 150) return 'warning';
      if (report.errors > 10) return 'warning';

      return 'healthy';
    }
  }

  // CSS Performance enhancements
  const applyCSSOptimizations = () => {
    // Add CSS containment for better performance
    const style = document.createElement('style');
    style.textContent = `
      /* Production CSS optimizations */
      .crypto-container {
        contain: layout style paint;
      }

      .chart-container {
        contain: layout paint;
      }

      .orderbook-canvas {
        contain: strict;
      }

      .liquidation-message {
        will-change: transform, opacity;
        contain: layout;
      }

      .loading-progress-fill {
        will-change: width;
      }

      /* Optimize expensive selectors */
      .orderbook-canvas,
      .chart-container,
      .crypto-container {
        transform: translateZ(0); /* Force GPU acceleration */
      }

      /* Reduce layout thrashing */
      * {
        box-sizing: border-box;
      }

      /* Performance mode for low-end devices */
      @media (max-width: 768px) {
        .crypto-container {
          contain: strict;
        }

        .chart-container {
          contain: strict;
        }
      }
    `;

    document.head.appendChild(style);
  };

  // Initialize production optimizations
  const initializeProductionOptimizations = () => {
    // Create global memory manager
    window.productionMemoryManager = new ProductionMemoryManager();

    // Create performance monitor
    if (IS_PRODUCTION) {
      window.productionPerformanceMonitor = new ProductionPerformanceMonitor();
    }

    // Override global timer functions
    const originalSetInterval = window.setInterval;
    const originalSetTimeout = window.setTimeout;

    window.setInterval = function(callback, delay) {
      return window.productionMemoryManager.setInterval(callback, delay);
    };

    window.setTimeout = function(callback, delay) {
      return window.productionMemoryManager.setTimeout(callback, delay);
    };

    // Enhanced addEventListener
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(event, handler, options) {
      const cleanup = window.productionMemoryManager.addEventListener(
        this, event, handler, options
      );

      // Call original
      originalAddEventListener.call(this, event, handler, options);

      return cleanup;
    };

    // Apply CSS optimizations
    applyCSSOptimizations();

    // Setup cleanup on page unload
    window.addEventListener('beforeunload', () => {
      if (window.productionMemoryManager) {
        window.productionMemoryManager.cleanup();
      }
    });

    // Setup periodic cleanup for long-running sessions
    setInterval(() => {
      if (window.productionMemoryManager) {
        const stats = window.productionMemoryManager.getStats();

        // Cleanup if memory usage is high
        if (stats.heapUsed && stats.heapUsed > 150) {
          window.productionMemoryManager.forceGC();
        }
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  };

  // Export utilities
  window.productionOptimizer = {
    IS_PRODUCTION,
    IS_DEBUG_MODE,
    createLogger: createProductionLogger,

    // Quick health check
    getHealthReport: () => {
      const memManager = window.productionMemoryManager;
      const perfMonitor = window.productionPerformanceMonitor;

      return {
        environment: IS_PRODUCTION ? 'production' : 'development',
        debugMode: IS_DEBUG_MODE,
        memory: memManager ? memManager.getStats() : null,
        performance: perfMonitor ? perfMonitor.getReport() : null,
        timestamp: new Date().toISOString()
      };
    },

    // Force cleanup
    cleanup: () => {
      if (window.productionMemoryManager) {
        window.productionMemoryManager.cleanup();
      }
    }
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProductionOptimizations);
  } else {
    initializeProductionOptimizations();
  }

  // Debug helper for development
  if (!IS_PRODUCTION || IS_DEBUG_MODE) {
    window.debugProductionOptimizer = () => {
      console.log('Production Optimizer Status:', window.productionOptimizer.getHealthReport());
    };
  }

  console.info('Production optimizer initialized:', {
    environment: IS_PRODUCTION ? 'production' : 'development',
    debugMode: IS_DEBUG_MODE,
    memoryManagement: true,
    performanceMonitoring: IS_PRODUCTION
  });

})();
