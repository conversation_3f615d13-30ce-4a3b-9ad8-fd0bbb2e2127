/**
 * Centralized Logger Module
 * Provides standardized logging across all modules with configurable verbosity
 */

(function() {
  'use strict';

  const LOG_LEVELS = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    NONE: 4
  };

  // Production-optimized configuration
  const config = {
    minLevel: window.DEBUG_MODE ? LOG_LEVELS.DEBUG : LOG_LEVELS.WARN,  // Production: only warnings and errors
    useConsoleFilter: true,         // Whether to use consoleFilter.js
    includeTimestamps: window.DEBUG_MODE || false,  // Timestamps only in debug mode
    includeStackTrace: true,        // Whether to include stack traces for errors
    errorReporting: null,           // Optional error reporting service
    productionMode: !window.DEBUG_MODE && !window.location.hostname.includes('localhost')  // Auto-detect production
  };

  /**
   * Creates a logger instance for a specific module
   * @param {string} moduleName - The name of the module
   * @returns {Object} - Logger instance with debug, info, warn, and error methods
   */
  function createLogger(moduleName) {
    const formatMessage = (message) => {
      if (typeof message !== 'string') {
        try {
          return JSON.stringify(message);
        } catch (e) {
          return String(message);
        }
      }
      return message;
    };

    const getTimestamp = () => {
      if (!config.includeTimestamps) return '';
      return `${new Date().toISOString()} `;
    };

    const formatPrefix = () => {
      return `${getTimestamp()}[${moduleName}]`;
    };

    // Check if consoleFilter is available
    const logFiltered = (level, message, ...args) => {
      const prefix = formatPrefix();
      const formattedMessage = `${prefix} ${formatMessage(message)}`;

      if (config.useConsoleFilter && window.consoleFilter && typeof window.consoleFilter.log === 'function') {
        window.consoleFilter.log(formattedMessage, ...args);
      } else {
        console[level](formattedMessage, ...args);
      }
    };

    const reportError = (message, error, extraData) => {
      if (config.errorReporting && typeof config.errorReporting.captureException === 'function') {
        try {
          config.errorReporting.captureException(error, {
            extra: {
              module: moduleName,
              message,
              ...extraData
            }
          });
        } catch (e) {
          console.error('Error reporting failed:', e);
        }
      }
    };

    // Production-optimized logger methods
    return {
      debug: config.productionMode ?
        function() {} : // No-op in production for maximum performance
        function(message, ...args) {
          if (config.minLevel <= LOG_LEVELS.DEBUG) {
            logFiltered('debug', message, ...args);
          }
        },

      info: config.productionMode && config.minLevel > LOG_LEVELS.INFO ?
        function() {} : // No-op in production if level too high
        function(message, ...args) {
          if (config.minLevel <= LOG_LEVELS.INFO) {
            logFiltered('log', message, ...args);
          }
        },

      warn: function(message, ...args) {
        if (config.minLevel <= LOG_LEVELS.WARN) {
          logFiltered('warn', message, ...args);
        }
      },

      error: function(message, error, ...args) {
        if (config.minLevel <= LOG_LEVELS.ERROR) {
          const errorObj = error instanceof Error ? error : new Error(String(error));

          if (config.includeStackTrace && errorObj.stack) {
            logFiltered('error', `${message}\n${errorObj.stack}`, ...args);
          } else {
            logFiltered('error', message, errorObj, ...args);
          }

          reportError(message, errorObj, args.length > 0 ? args[0] : undefined);
        }
      },

      group: function(label) {
        if (config.minLevel <= LOG_LEVELS.DEBUG) {
          console.group(`${formatPrefix()} ${label}`);
        }
      },

      groupEnd: function() {
        if (config.minLevel <= LOG_LEVELS.DEBUG) {
          console.groupEnd();
        }
      },

      // Allow changing level for specific module
      setLevel: function(level) {
        if (LOG_LEVELS[level] !== undefined) {
          config.minLevel = LOG_LEVELS[level];
        }
      }
    };
  }

  // Global configuration function
  function configure(options) {
    Object.assign(config, options);
  }

  // Expose the logger factory and utilities
  window.logger = {
    createLogger,
    configure,
    LOG_LEVELS
  };

  // Create a default system logger
  window.logger.system = createLogger('System');

  // Override console.error to capture unhandled exceptions
  const originalConsoleError = console.error;
  console.error = function(...args) {
    originalConsoleError.apply(console, args);

    // Check if this is an unhandled exception
    if (args.length > 0 && args[0] && 
        (typeof args[0] === 'string' && 
         (args[0].includes('Uncaught') || args[0].includes('unhandled')))) {
      const error = args.find(arg => arg instanceof Error) || new Error(args[0]);
      if (config.errorReporting) {
        window.logger.system.error('Unhandled exception', error, { args });
      }
    }
  };
})();
