/**
 * Global configuration system for the crypto dashboard
 * Centralizes all configuration values to reduce duplication and improve maintainability
 */
window.CONFIG = {
    // WebSocket configurations
    websockets: {
        bitstamp: {
            url: 'wss://ws.bitstamp.net',
            pingInterval: 30000,
            reconnectDelay: 2000
        },
        bybit: {
            url: 'wss://stream.bybit.com/v5/public/linear',
            pingInterval: 20000,
            reconnectDelay: 2000,
            maxReconnectAttempts: 10
        }
    },



    // Order book specific configurations
    orderbook: {
        barWidth: 1,                // Default width for order book bars
        rangePercentage: 0.01,      // Default percentage for price range size (e.g., 1%)
        extremesResetThreshold: 0.05, // Default percentage for resetting persistent low/high blocks (e.g., 5%)
        miniChartIntervalSeconds: 300, // 5-minute interval for the small chart
        miniChartBarLimit: 288,       // 24 hours of 5-minute candles
        colors: {
            strokeStyle: "#1c2526",
            positiveDynamic: "rgba(0, 255, 255, 0.75)", // Aqua for positive bias
            negativeDynamic: "rgba(255, 85, 85, 0.75)",  // Red for negative bias
            midPriceText: "#BBBBBB",
            barFill: "rgba(170, 170, 170, 0.6)"
        }
    },
    
    // Time intervals mapping
    intervals: {
        '1m': { seconds: 60, apiValue: '60', label: '1 Min' },
        '5m': { seconds: 300, apiValue: '300', label: '5 Min' },
        '15m': { seconds: 900, apiValue: '900', label: '15 Min' },
        '30m': { seconds: 1800, apiValue: '1800', label: '30 Min' },
        '1h': { seconds: 3600, apiValue: '60', label: '1 Hour' },
        '4h': { seconds: 14400, apiValue: '240', label: '4 Hour' },
        '1d': { seconds: 86400, apiValue: 'D', label: '1 Day' }
    },
    
    // Supported cryptocurrencies
    cryptocurrencies: [
        { symbol: 'BTC', name: 'Bitcoin', color: '#F7931A' },
        { symbol: 'ETH', name: 'Ethereum', color: '#627EEA' },
        { symbol: 'LTC', name: 'Litecoin', color: '#BFBBBB' },
        { symbol: 'SOL', name: 'Solana', color: '#00FFA3' }
    ],
    
    // Chart configurations
    chart: {
        defaultColors: {
            background: '#0f141a',
            text: '#D3D3D3',
            grid: '#2A2A2A',
            upColor: '#26a69a',
            downColor: '#ef5350',
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350'
        },
        candlestick: {
            upColor: '#AAAAAA',
            downColor: '#AAAAAA',
            borderColor: '#AAAAAA',
            wickUpColor: '#AAAAAA',
            wickDownColor: '#AAAAAA'
        },
        upDownMarkers: { // Configuration for up/down markers in charts.js
            threshold: 0.005,
            upColor: 'rgba(0, 255, 255, 0.7)', // Aqua
            downColor: 'rgba(255, 85, 85, 0.7)', // Red
            size: 0.5
        }
    },
    
    // Drawing primitives configurations
    primitives: {
        line: {
            defaultColor: 'rgba(255, 255, 255, 0.8)',
            defaultWidth: 2,
            styles: {
                solid: 0,
                dotted: 1,
                dashed: 2
            }
        },
        rectangle: {
            defaultColor: 'rgba(255, 255, 255, 0.8)',
            defaultFillColor: 'rgba(255, 255, 255, 0.1)',
            defaultWidth: 2
        }
    },
    
    // API endpoints and settings
    api: {
        bitstamp: {
            baseUrl: 'https://www.bitstamp.net/api/v2',
            endpoints: {
                ohlc: '/ohlc/{symbol}/?step={interval}&limit=1000'
            }
        },
        bybit: {
            baseUrl: 'https://api.bybit.com/v5',
            endpoints: {
                kline: '/market/kline?category=linear&symbol={symbol}USDT&interval={interval}&limit=1000'
            }
        }
    },
    
    // CORS proxies for API fallbacks were removed as they were unused.
    
    // UI settings
    ui: {
        loadingTimeout: 7000,
        errorDisplayTime: 3000,
        throttleDelay: 100,
        resizeThrottleDelay: 100,
        defaultLiquidationThreshold: 100000,
        defaultWhaleAlertThreshold: 100000
    },

    // VWAP specific configurations
    vwap: {
        rollingWindow: 20,
        stdDevMultiplier: 2.5
    },

    // EMA Bands specific configurations
    emaBands: {
        period: 20,
        stdDevMultiplier: 2
    },

    // Popup Chart specific configurations
    popupChart: {
        defaultInterval: '60', // Default to 1 hour (matches '60' key in intervals map)
        defaultWidth: 400,     // Default width if DOM element has no size
        defaultHeight: 300     // Default height if DOM element has no size
    },
    
    // CVD (Cumulative Volume Delta) configurations
    cvd: {
        updateIntervalMs: 1000, // Update interval for the data store
        volumeMAPeriod: 90,
        volumeAdjustment: {
            enabled: true,
            buyMultiplier: 1.0,
            sellMultiplier: 1.0,
            useWicks: true,
            useBodySize: true,
            useCloseRelative: true
        },
        renderOnCandleCloseOnly: true,
        lookbackPeriod: 1440,
        normalize: true,
        smoothing: true,
        sensitivityMultiplier: 1.2,
        normalizationBuffer: 0,
        minSmoothingPeriod: 5,
        maxSmoothingPeriod: 20,
        adaptiveSmoothingFactor: 0.5,
        volumeWeighting: {
            enabled: true,
            weightFactor: 0.5
        },
        colors: {
            positive: '#00FF7F', // Spring Green for positive CVD
            negative: '#FF6B6B', // Light Coral for negative CVD
            neutral: '#888888'   // Grey for neutral/zero CVD
        }
    },
    
    // Perpetual CVD configurations (separate from spot CVD)
    perpCvd: {
        updateIntervalMs: 1000, // Update interval for the data store
        volumeMAPeriod: 90,
        volumeAdjustment: {
            enabled: true,
            buyMultiplier: 1.0,
            sellMultiplier: 1.0,
            useWicks: true,
            useBodySize: true,
            useCloseRelative: true
        },
        renderOnCandleCloseOnly: true,
        lookbackPeriod: 1440,
        normalize: true,
        smoothing: true,
        sensitivityMultiplier: 1.2,
        normalizationBuffer: 0,
        minSmoothingPeriod: 5,
        maxSmoothingPeriod: 20,
        adaptiveSmoothingFactor: 0.5,
        volumeWeighting: {
            enabled: true,
            weightFactor: 0.5
        },
        colors: {
            positive: '#00BFFF', // Deep Sky Blue for positive Perp CVD
            negative: '#FF4500', // OrangeRed for negative Perp CVD
            neutral: '#777777'   // Grey for neutral/zero Perp CVD
        }
    },
    
    // General indicator configurations
    indicators: {
        maxCachedItems: 1000,
        emaPeriod: 180,
        sdPeriod: 1440,
        minLookbackBars: 1440,
        barInterval: 300,
        defaultColors: { // Generic colors for indicators from utils.js
            positive: 'rgba(255, 0, 0, 0.4)', // Red for positive
            negative: 'rgba(0, 255, 255, 0.4)', // Aqua for negative
            neutral: 'rgba(170, 170, 170, 0.8)'  // Grey for neutral
        },
        defaultThresholds: { // Thresholds for getIndicatorColor
            positive: 0.5,
            negative: -0.5
        }
    }
};
