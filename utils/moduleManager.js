/**
 * Module Manager
 * Centralized module management with dependency resolution and lifecycle handling
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  "use strict";

  const logger = window.logger
    ? window.logger.createLogger("ModuleManager")
    : console;

  /**
   * Module state constants
   * @enum {string}
   */
  const ModuleState = {
    UNREGISTERED: "unregistered",
    REGISTERED: "registered",
    LOADING: "loading",
    READY: "ready",
    ERROR: "error",
    DESTROYED: "destroyed",
  };

  /**
   * Module lifecycle events
   * @enum {string}
   */
  const ModuleEvent = {
    REGISTERED: "module-registered",
    LOADING: "module-loading",
    READY: "module-ready",
    ERROR: "module-error",
    DESTROYED: "module-destroyed",
  };

  /**
   * Module registration interface
   * @typedef {Object} ModuleDefinition
   * @property {string} name - Module name
   * @property {Array<string>} [dependencies=[]] - Module dependencies
   * @property {function} [init] - Initialization function
   * @property {function} [destroy] - Cleanup function
   * @property {Object} [exports={}] - Module exports
   * @property {Object} [api=null] - Public API
   * @property {boolean} [autoInit=false] - Whether to initialize automatically
   */

  /**
   * Module Manager class
   */
  class ModuleManager {
    /**
     * Create a new ModuleManager
     */
    constructor() {
      this.modules = new Map();
      this.readyPromises = new Map();
      this.loadingQueue = [];
      this.loadingInProgress = false;
      this.debug = false;
    }

    /**
     * Register a module
     * @param {ModuleDefinition} moduleDefinition - Module definition
     * @returns {Promise} - Promise that resolves when the module is registered
     * @throws {Error} - If the module is invalid or already registered
     */
    register(moduleDefinition) {
      // Validate module definition
      if (!moduleDefinition || !moduleDefinition.name) {
        throw new Error("Invalid module definition: missing name");
      }

      const name = moduleDefinition.name;

      // Check if already registered
      if (this.modules.has(name)) {
        throw new Error(`Module '${name}' is already registered`);
      }

      // Create module instance
      const module = {
        name,
        dependencies: moduleDefinition.dependencies || [],
        init: moduleDefinition.init || (() => Promise.resolve()),
        destroy: moduleDefinition.destroy || (() => Promise.resolve()),
        exports: moduleDefinition.exports || {},
        api: moduleDefinition.api || null,
        state: ModuleState.REGISTERED,
        error: null,
        instance: null,
        autoInit: !!moduleDefinition.autoInit,
      };

      // Store module
      this.modules.set(name, module);

      // Create ready promise
      this.readyPromises.set(
        name,
        new Promise((resolve, reject) => {
          module.resolveReady = resolve;
          module.rejectReady = reject;
        }),
      );

      // Emit event
      this._emitEvent(ModuleEvent.REGISTERED, { name });

      logger.info(`Module '${name}' registered`);

      // Auto-initialize if configured
      if (module.autoInit) {
        return this.initModule(name);
      }

      return Promise.resolve();
    }

    /**
     * Initialize a module and its dependencies
     * @param {string} name - Module name
     * @returns {Promise} - Promise that resolves when the module is initialized
     * @throws {Error} - If the module is not registered
     */
    async initModule(name) {
      // Check if module exists
      if (!this.modules.has(name)) {
        throw new Error(`Module '${name}' is not registered`);
      }

      const module = this.modules.get(name);

      // Skip if already ready
      if (module.state === ModuleState.READY) {
        return module.instance;
      }

      // If already loading, return ready promise
      if (module.state === ModuleState.LOADING) {
        return this.readyPromises.get(name);
      }

      // Mark as loading
      module.state = ModuleState.LOADING;
      this._emitEvent(ModuleEvent.LOADING, { name });

      try {
        // Initialize dependencies first
        for (const depName of module.dependencies) {
          if (!this.modules.has(depName)) {
            throw new Error(
              `Module '${name}' depends on '${depName}', which is not registered`,
            );
          }

          const dep = this.modules.get(depName);
          if (dep.state === ModuleState.ERROR) {
            throw new Error(
              `Module '${name}' depends on '${depName}', which failed to initialize`,
            );
          }

          // Initialize dependency
          await this.initModule(depName);
        }

        // Get dependency instances
        const dependencies = {};
        for (const depName of module.dependencies) {
          dependencies[depName] = this.modules.get(depName).instance;
        }

        // Initialize module
        logger.info(`Initializing module '${name}'`);
        module.instance = await module.init(dependencies);

        // Mark as ready
        module.state = ModuleState.READY;
        module.resolveReady(module.instance);
        this._emitEvent(ModuleEvent.READY, { name, instance: module.instance });

        logger.info(`Module '${name}' initialized successfully`);
        return module.instance;
      } catch (error) {
        // Mark as error
        module.state = ModuleState.ERROR;
        module.error = error;
        module.rejectReady(error);
        this._emitEvent(ModuleEvent.ERROR, { name, error });

        logger.error(`Failed to initialize module '${name}'`, error);
        throw error;
      }
    }

    /**
     * Get a module instance
     * @param {string} name - Module name
     * @returns {Promise} - Promise that resolves to the module instance
     * @throws {Error} - If the module is not registered
     */
    async getModule(name) {
      // Check if module exists
      if (!this.modules.has(name)) {
        throw new Error(`Module '${name}' is not registered`);
      }

      const module = this.modules.get(name);

      // Initialize if not ready
      if (module.state !== ModuleState.READY) {
        await this.initModule(name);
      }

      return module.instance;
    }

    /**
     * Get a module instance synchronously
     * @param {string} name - Module name
     * @returns {Object|null} - Module instance or null if not ready
     */
    getModuleSync(name) {
      // Check if module exists
      if (!this.modules.has(name)) {
        return null;
      }

      const module = this.modules.get(name);
      return module.state === ModuleState.READY ? module.instance : null;
    }

    /**
     * Destroy a module
     * @param {string} name - Module name
     * @returns {Promise} - Promise that resolves when the module is destroyed
     * @throws {Error} - If the module is not registered
     */
    async destroyModule(name) {
      // Check if module exists
      if (!this.modules.has(name)) {
        throw new Error(`Module '${name}' is not registered`);
      }

      const module = this.modules.get(name);

      // Skip if already destroyed
      if (module.state === ModuleState.DESTROYED) {
        return;
      }

      // Check if other modules depend on this one
      const dependents = Array.from(this.modules.values()).filter(
        (m) => m.dependencies.includes(name) && m.state === ModuleState.READY,
      );

      if (dependents.length > 0) {
        const dependentNames = dependents.map((m) => m.name).join(", ");
        throw new Error(
          `Cannot destroy module '${name}' because it is used by: ${dependentNames}`,
        );
      }

      try {
        // Destroy module
        logger.info(`Destroying module '${name}'`);
        await module.destroy();

        // Mark as destroyed
        module.state = ModuleState.DESTROYED;
        module.instance = null;
        this._emitEvent(ModuleEvent.DESTROYED, { name });

        logger.info(`Module '${name}' destroyed successfully`);
      } catch (error) {
        logger.error(`Failed to destroy module '${name}'`, error);
        throw error;
      }
    }

    /**
     * Get the state of a module
     * @param {string} name - Module name
     * @returns {string} - Module state
     * @throws {Error} - If the module is not registered
     */
    getModuleState(name) {
      // Check if module exists
      if (!this.modules.has(name)) {
        throw new Error(`Module '${name}' is not registered`);
      }

      return this.modules.get(name).state;
    }

    /**
     * Get all registered modules
     * @returns {Array} - Array of module information
     */
    getAllModules() {
      return Array.from(this.modules.entries()).map(([name, module]) => ({
        name,
        state: module.state,
        dependencies: module.dependencies,
        hasError: module.state === ModuleState.ERROR,
      }));
    }

    /**
     * Initialize all registered modules
     * @returns {Promise} - Promise that resolves when all modules are initialized
     */
    async initAll() {
      const moduleNames = Array.from(this.modules.keys());
      const promises = moduleNames.map((name) =>
        this.initModule(name).catch((error) => {
          logger.error(`Failed to initialize module '${name}'`, error);
          return null; // Continue with other modules even if some fail
        }),
      );

      return Promise.all(promises);
    }

    /**
     * Destroy all modules
     * @returns {Promise} - Promise that resolves when all modules are destroyed
     */
    async destroyAll() {
      // Sort modules by dependencies (destroy dependents first)
      const sortedModules = this._sortModulesForDestruction();

      // Destroy modules in order
      for (const name of sortedModules) {
        try {
          await this.destroyModule(name);
        } catch (error) {
          logger.error(`Failed to destroy module '${name}'`, error);
          // Continue with other modules
        }
      }
    }

    /**
     * Sort modules for destruction (dependents first)
     * @returns {Array} - Sorted array of module names
     * @private
     */
    _sortModulesForDestruction() {
      const result = [];
      const visited = new Set();

      // Build dependency graph
      const graph = {};
      for (const [name, module] of this.modules.entries()) {
        graph[name] = module.dependencies;
      }

      // Topological sort (reversed for destruction)
      const visit = (name) => {
        if (visited.has(name)) return;
        visited.add(name);

        // Find modules that depend on this one
        const dependents = Array.from(this.modules.entries())
          .filter(([_, module]) => module.dependencies.includes(name))
          .map(([depName]) => depName);

        for (const depName of dependents) {
          visit(depName);
        }

        result.push(name);
      };

      for (const name of this.modules.keys()) {
        visit(name);
      }

      return result;
    }

    /**
     * Reset a module to its initial state
     * @param {string} name - Module name
     * @returns {Promise} - Promise that resolves when the module is reset
     */
    async resetModule(name) {
      try {
        await this.destroyModule(name);
      } catch (error) {
        // If it fails because of dependencies, just log it
        logger.warn(`Could not destroy module '${name}' cleanly`, error);
      }

      // Reset module state
      const module = this.modules.get(name);
      if (module) {
        module.state = ModuleState.REGISTERED;
        module.error = null;
        module.instance = null;

        // Reset ready promise
        this.readyPromises.set(
          name,
          new Promise((resolve, reject) => {
            module.resolveReady = resolve;
            module.rejectReady = reject;
          }),
        );

        // Re-initialize
        return this.initModule(name);
      }
    }

    /**
     * Emit a module event
     * @param {string} eventType - Event type
     * @param {Object} data - Event data
     * @private
     */
    _emitEvent(eventType, data) {
      if (this.debug) {
        logger.debug(`Emitting event ${eventType}`, data);
      }

      // Use EventBus if available
      if (window.eventBus && typeof window.eventBus.emit === "function") {
        window.eventBus.emit(eventType, data);
      }

      // Also dispatch DOM event for compatibility
      const event = new CustomEvent(eventType, { detail: data });
      document.dispatchEvent(event);
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether to enable debug mode
     */
    setDebug(enabled) {
      this.debug = enabled;
    }
  }

  // Create global instance
  window.moduleManager = new ModuleManager();

  // Expose constants
  window.moduleManager.ModuleState = ModuleState;
  window.moduleManager.ModuleEvent = ModuleEvent;

  // Signal that moduleManager is ready
  if (typeof window.signalModuleReady === "function") {
    window.signalModuleReady("moduleManager");
  }
})();
