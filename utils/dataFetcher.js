(function() {
    'use strict';

    // Helper function to get the timestamp of the start of the ISO week (Monday)
    function getStartOfWeek(timestampSeconds) {
        const date = new Date(timestampSeconds * 1000);
        const day = date.getUTCDay();
        // Calculate how many days to subtract to get to Monday
        const diff = (day + 6) % 7; // 0 (Sun) -> 6, 1 (Mon) -> 0, ..., 6 (Sat) -> 5
        date.setUTCDate(date.getUTCDate() - diff);
        date.setUTCHours(0, 0, 0, 0);
        return Math.floor(date.getTime() / 1000);
    }

    // Helper function to get the timestamp of the start of the month
    function getStartOfMonth(timestampSeconds) {
        const date = new Date(timestampSeconds * 1000);
        date.setUTCDate(1);
        date.setUTCHours(0, 0, 0, 0);
        return Math.floor(date.getTime() / 1000);
    }

    const cache = new Map();
    const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

    async function fetchWithProxies(originalUrl, signal) {
        const proxies = [
            originalUrl, // Direct fetch
            `https://corsproxy.io/?${encodeURIComponent(originalUrl)}`,
            `https://api.allorigins.win/raw?url=${encodeURIComponent(originalUrl)}`
        ];

        for (const proxyUrl of proxies) {
            try {
                const fetchOptions = signal ? { signal } : {};
                const response = await fetch(proxyUrl, fetchOptions);
                if (response.ok) {
                    return await response.json();
                }
                console.warn(`Fetch attempt failed for ${proxyUrl}, status: ${response.status}`);
            } catch (error) {
                if (error.name === 'AbortError') {
                    console.log('Fetch aborted:', originalUrl);
                    throw error;
                }
                console.warn(`Fetch attempt failed for ${proxyUrl}: ${error.message}`);
            }
        }
        throw new Error(`All fetch attempts failed for ${originalUrl}`);
    }

    async function fetchBitstampData(symbolPair, intervalSeconds, signal) {
        const WEEK_SECONDS = 604800;
        const MONTH_SECONDS = 2592000; // Approximation: 30 days
        const DAY_SECONDS = 86400;

        const requestedIntervalSeconds = intervalSeconds;
        let fetchApiInterval = intervalSeconds;

        // Bitstamp uses symbol like 'btcusd', 'ethusd'
        // intervalSeconds: 60, 180, 300, 900, 1800, 3600, 7200, 14400, 21600, 43200, 86400
        const supportedDirectIntervals = [60, 180, 300, 900, 1800, 3600, 7200, 14400, 21600, DAY_SECONDS];

        if (requestedIntervalSeconds === WEEK_SECONDS || requestedIntervalSeconds === MONTH_SECONDS) {
            fetchApiInterval = DAY_SECONDS; // Fetch daily data for aggregation
        } else if (!supportedDirectIntervals.includes(intervalSeconds)) {
            console.warn(`Bitstamp: Interval ${intervalSeconds}s not directly supported, defaulting to 3600s.`);
            fetchApiInterval = 3600; // Default to 1 hour if not perfectly matching
        }

        // Bitstamp expects lowercase pair
        const formattedSymbolPair = symbolPair.toLowerCase();
        const url = `https://www.bitstamp.net/api/v2/ohlc/${formattedSymbolPair}/?step=${fetchApiInterval}&limit=1000`;

        const rawFetchedData = await fetchWithProxies(url, signal);
        if (!rawFetchedData || !rawFetchedData.data || !rawFetchedData.data.ohlc) {
            throw new Error(`Invalid data structure from Bitstamp API for ${formattedSymbolPair}`);
        }

        let processedData = rawFetchedData.data.ohlc.map(bar => ({
            time: parseInt(bar.timestamp),
            open: parseFloat(bar.open),
            high: parseFloat(bar.high),
            low: parseFloat(bar.low),
            close: parseFloat(bar.close),
            volume: parseFloat(bar.volume)
        })).sort((a, b) => a.time - b.time);

        if (requestedIntervalSeconds === WEEK_SECONDS && fetchApiInterval === DAY_SECONDS) {
            processedData = aggregateDailyToWeekly(processedData);
        } else if (requestedIntervalSeconds === MONTH_SECONDS && fetchApiInterval === DAY_SECONDS) {
            processedData = aggregateDailyToMonthly(processedData);
        }

        return processedData;
    }

    function aggregateDailyToWeekly(dailyData) {
        if (!dailyData || dailyData.length === 0) return [];

        const weeklyData = [];
        let currentWeekStart = -1;
        let weeklyCandle = null;

        for (const dailyCandle of dailyData) {
            const weekStartForCandle = getStartOfWeek(dailyCandle.time);

            if (weekStartForCandle !== currentWeekStart) {
                if (weeklyCandle) {
                    weeklyData.push(weeklyCandle);
                }
                currentWeekStart = weekStartForCandle;
                weeklyCandle = {
                    time: currentWeekStart,
                    open: dailyCandle.open,
                    high: dailyCandle.high,
                    low: dailyCandle.low,
                    close: dailyCandle.close,
                    volume: dailyCandle.volume
                };
            } else {
                weeklyCandle.high = Math.max(weeklyCandle.high, dailyCandle.high);
                weeklyCandle.low = Math.min(weeklyCandle.low, dailyCandle.low);
                weeklyCandle.close = dailyCandle.close;
                weeklyCandle.volume += dailyCandle.volume;
            }
        }
        if (weeklyCandle) { // Push the last aggregated candle
            weeklyData.push(weeklyCandle);
        }
        return weeklyData;
    }

    function aggregateDailyToMonthly(dailyData) {
        if (!dailyData || dailyData.length === 0) return [];

        const monthlyData = [];
        let currentMonthStart = -1;
        let monthlyCandle = null;

        for (const dailyCandle of dailyData) {
            const monthStartForCandle = getStartOfMonth(dailyCandle.time);

            if (monthStartForCandle !== currentMonthStart) {
                if (monthlyCandle) {
                    monthlyData.push(monthlyCandle);
                }
                currentMonthStart = monthStartForCandle;
                monthlyCandle = {
                    time: currentMonthStart,
                    open: dailyCandle.open,
                    high: dailyCandle.high,
                    low: dailyCandle.low,
                    close: dailyCandle.close,
                    volume: dailyCandle.volume
                };
            } else {
                monthlyCandle.high = Math.max(monthlyCandle.high, dailyCandle.high);
                monthlyCandle.low = Math.min(monthlyCandle.low, dailyCandle.low);
                monthlyCandle.close = dailyCandle.close;
                monthlyCandle.volume += dailyCandle.volume;
            }
        }
        if (monthlyCandle) { // Push the last aggregated candle
            monthlyData.push(monthlyCandle);
        }
        return monthlyData;
    }

    async function fetchBybitData(symbol, apiIntervalString, signal) {
        // Bybit uses symbol like 'BTC', 'ETH' (uppercase) and interval string '1', '5', '15', '60', 'D', 'W', 'M'
        // We expect apiIntervalString to be like '1', '5', '60', 'D', 'W', 'M' etc.
        const url = `https://api.bybit.com/v5/market/kline?category=linear&symbol=${symbol.toUpperCase()}USDT&interval=${apiIntervalString}&limit=1000`;

        const rawData = await fetchWithProxies(url, signal);
        if (!rawData || rawData.retCode !== 0 || !rawData.result || !rawData.result.list) {
            throw new Error(`Invalid data structure or error from Bybit API for ${symbol}: ${rawData?.retMsg}`);
        }
        return rawData.result.list.map(bar => ({
            time: parseInt(bar[0]) / 1000,
            open: parseFloat(bar[1]),
            high: parseFloat(bar[2]),
            low: parseFloat(bar[3]),
            close: parseFloat(bar[4]),
            volume: parseFloat(bar[5])
        })).sort((a, b) => a.time - b.time);
    }

    async function getHistoricalData(exchange, symbol, interval, signal) {
        const cacheKey = `${exchange}_${symbol}_${interval}`;
        const cachedEntry = cache.get(cacheKey);

        if (cachedEntry && (Date.now() - cachedEntry.timestamp < CACHE_TTL)) {
            console.log(`[DataFetcher] Using cached data for ${cacheKey}`);
            return JSON.parse(JSON.stringify(cachedEntry.data)); // Return a copy
        }
        console.log(`[DataFetcher] Fetching fresh data for ${cacheKey}`);

        let data;
        if (exchange.toLowerCase() === 'bitstamp') {
            // Expect interval to be in seconds for Bitstamp
            const intervalSec = typeof interval === 'string' ? mapIntervalToSeconds(interval) : interval;
            data = await fetchBitstampData(symbol, intervalSec, signal);
        } else if (exchange.toLowerCase() === 'bybit') {
            // Expect interval to be API string like '60', 'D' for Bybit
        // Ensure interval is a string for Bybit
        const intervalStr = typeof interval === 'number' ? mapSecondsToBybitInterval(interval) : String(interval);
            data = await fetchBybitData(symbol, intervalStr, signal);
        } else {
            throw new Error(`Unsupported exchange: ${exchange}`);
        }

        if (data && data.length > 0) {
            cache.set(cacheKey, { timestamp: Date.now(), data: JSON.parse(JSON.stringify(data)) });
        }
        return data;
    }

    // Helper for interval mapping
    function mapIntervalToSeconds(intervalStr) {
        // PopupChart.js has a more comprehensive one, this is simplified
        const value = parseInt(intervalStr);
        if (intervalStr.endsWith('m')) return value * 60;
        if (intervalStr.endsWith('h')) return value * 3600;
        if (intervalStr.endsWith('D')) return value * 86400;
        if (intervalStr.endsWith('W')) return value * 604800;
        if (intervalStr.endsWith('M')) return value * 2592000; // Approx 30 days
        if (!isNaN(value)) return value; // Assume seconds if just a number
        return 3600; // Default to 1 hour in seconds
    }

    function mapSecondsToBybitInterval(seconds) {
        if (seconds === 60) return '1';
        if (seconds === 180) return '3';
        if (seconds === 300) return '5';
        if (seconds === 900) return '15';
        if (seconds === 1800) return '30';
        if (seconds === 3600) return '60';
        if (seconds === 7200) return '120';
        if (seconds === 14400) return '240';
        if (seconds === 21600) return '360';
        if (seconds === 43200) return '720';
        if (seconds === 86400) return 'D';
        if (seconds === 604800) return 'W';
        if (seconds === 2592000) return 'M'; // Approx 30 days
        console.warn(`Bybit: Interval ${seconds}s not directly mapped, defaulting to '60'.`);
        return '60'; // Default to 1 hour
    }


    window.dataFetcher = {
        getHistoricalData,
        fetchBitstampData,
        fetchBybitData,
        fetchWithProxies,
        // Expose aggregation functions if they might be useful externally, or for testing
        _aggregateDailyToWeekly: aggregateDailyToWeekly,
        _aggregateDailyToMonthly: aggregateDailyToMonthly
    };

})();
