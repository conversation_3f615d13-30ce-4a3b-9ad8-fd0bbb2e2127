/**
 * Resource Manager
 * Manages and cleans up resources to prevent memory leaks
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('ResourceManager') : console;

  /**
   * Resource Manager class
   * Tracks resources and provides proper cleanup
   */
  class ResourceManager {
    /**
     * Create a new resource manager
     * @param {string} [name='ResourceManager'] - Name for logging
     */
    constructor(name = 'ResourceManager') {
      this.name = name;
      this.resources = new Map();
      this.cleanupFunctions = new Map();
      this.groups = new Map();

      // Get logger if available
      this.logger = window.logger ? window.logger.createLogger(name) : {
        debug: console.debug.bind(console),
        info: console.log.bind(console),
        warn: console.warn.bind(console),
        error: console.error.bind(console)
      };
    }

    /**
     * Register a resource for cleanup
     * @param {string} id - Resource identifier
     * @param {*} resource - The resource to register
     * @param {Function|string} [cleanup] - Cleanup function or method name
     * @param {string} [group] - Optional group for batch cleanup
     * @returns {Function} - Function to unregister this resource
     */
    register(id, resource, cleanup, group) {
      if (!id) {
        throw new Error('Resource ID is required');
      }

      if (this.resources.has(id)) {
        this.logger.warn(`Resource '${id}' is already registered, replacing`);
        this.unregister(id);
      }

      this.resources.set(id, resource);

      // Determine cleanup function
      let cleanupFunction;

      if (typeof cleanup === 'function') {
        // Direct function
        cleanupFunction = () => cleanup(resource);
      } else if (typeof cleanup === 'string' && resource && typeof resource[cleanup] === 'function') {
        // Method name on resource
        cleanupFunction = () => resource[cleanup]();
      } else if (resource) {
        // Auto-detect cleanup method
        if (typeof resource.destroy === 'function') {
          cleanupFunction = () => resource.destroy();
        } else if (typeof resource.close === 'function') {
          cleanupFunction = () => resource.close();
        } else if (typeof resource.remove === 'function') {
          cleanupFunction = () => resource.remove();
        } else if (typeof resource.dispose === 'function') {
          cleanupFunction = () => resource.dispose();
        } else if (typeof resource.cleanup === 'function') {
          cleanupFunction = () => resource.cleanup();
        } else if (resource instanceof EventTarget && resource.removeEventListener) {
          // For event listeners, store but don't auto-generate cleanup
          cleanupFunction = null;
        } else {
          this.logger.warn(`No cleanup method found for resource '${id}'`);
          cleanupFunction = null;
        }
      }

      if (cleanupFunction) {
        this.cleanupFunctions.set(id, cleanupFunction);
      }

      // Add to group if specified
      if (group) {
        if (!this.groups.has(group)) {
          this.groups.set(group, new Set());
        }
        this.groups.get(group).add(id);
      }

      this.logger.debug(`Registered resource '${id}'${group ? ` in group '${group}'` : ''}`);

      // Return unregister function
      return () => this.unregister(id);
    }

    /**
     * Register an event listener
     * @param {string} id - Resource identifier
     * @param {EventTarget} target - Event target
     * @param {string} event - Event name
     * @param {Function} listener - Event listener
     * @param {Object} [options] - Event listener options
     * @param {string} [group] - Optional group for batch cleanup
     * @returns {Function} - Function to unregister this resource
     */
    registerEventListener(id, target, event, listener, options, group) {
      if (!target || !event || !listener) {
        throw new Error('Target, event and listener are required');
      }

      // Attach the listener
      target.addEventListener(event, listener, options);

      // Create cleanup function
      const cleanup = () => target.removeEventListener(event, listener, options);

      // Register resource with cleanup
      return this.register(id, { target, event, listener, options }, cleanup, group);
    }

    /**
     * Register a timeout
     * @param {string} id - Resource identifier
     * @param {Function} callback - Timeout callback
     * @param {number} delay - Delay in milliseconds
     * @param {string} [group] - Optional group for batch cleanup
     * @returns {number} - Timeout ID
     */
    registerTimeout(id, callback, delay, group) {
      const timeoutId = setTimeout(() => {
        // Auto-unregister when timeout fires
        this.unregister(id);
        callback();
      }, delay);

      // Register with cleanup
      this.register(id, timeoutId, () => clearTimeout(timeoutId), group);

      return timeoutId;
    }

    /**
     * Register an interval
     * @param {string} id - Resource identifier
     * @param {Function} callback - Interval callback
     * @param {number} delay - Delay in milliseconds
     * @param {string} [group] - Optional group for batch cleanup
     * @returns {number} - Interval ID
     */
    registerInterval(id, callback, delay, group) {
      const intervalId = setInterval(callback, delay);

      // Register with cleanup
      this.register(id, intervalId, () => clearInterval(intervalId), group);

      return intervalId;
    }

    /**
     * Register an animation frame
     * @param {string} id - Resource identifier
     * @param {Function} callback - Animation frame callback
     * @param {string} [group] - Optional group for batch cleanup
     * @returns {number} - Request ID
     */
    registerAnimationFrame(id, callback, group) {
      const requestId = requestAnimationFrame((timestamp) => {
        // Auto-unregister when frame fires
        this.unregister(id);
        callback(timestamp);
      });

      // Register with cleanup
      this.register(id, requestId, () => cancelAnimationFrame(requestId), group);

      return requestId;
    }

    /**
     * Unregister a resource
     * @param {string} id - Resource identifier
     * @returns {boolean} - Whether the resource was found and unregistered
     */
    unregister(id) {
      if (!this.resources.has(id)) {
        return false;
      }

      // Run cleanup function if exists
      if (this.cleanupFunctions.has(id)) {
        try {
          this.cleanupFunctions.get(id)();
        } catch (error) {
          this.logger.error(`Error cleaning up resource '${id}'`, error);
        }
        this.cleanupFunctions.delete(id);
      }

      // Remove from resources
      this.resources.delete(id);

      // Remove from all groups
      for (const [groupName, group] of this.groups.entries()) {
        if (group.has(id)) {
          group.delete(id);
          if (group.size === 0) {
            this.groups.delete(groupName);
          }
        }
      }

      this.logger.debug(`Unregistered resource '${id}'`);
      return true;
    }

    /**
     * Clean up a group of resources
     * @param {string} groupName - Group name
     * @returns {number} - Number of resources cleaned up
     */
    cleanupGroup(groupName) {
      if (!this.groups.has(groupName)) {
        return 0;
      }

      const group = this.groups.get(groupName);
      const resources = Array.from(group);
      let count = 0;

      this.logger.info(`Cleaning up group '${groupName}' with ${resources.length} resources`);

      resources.forEach(id => {
        if (this.unregister(id)) {
          count++;
        }
      });

      // Group should be automatically removed when empty
      return count;
    }

    /**
     * Clean up all resources
     * @returns {number} - Number of resources cleaned up
     */
    cleanupAll() {
      const resources = Array.from(this.resources.keys());
      let count = 0;

      this.logger.info(`Cleaning up all resources (${resources.length} total)`);

      resources.forEach(id => {
        if (this.unregister(id)) {
          count++;
        }
      });

      return count;
    }

    /**
     * Get resource by ID
     * @param {string} id - Resource identifier
     * @returns {*} - The resource or undefined if not found
     */
    getResource(id) {
      return this.resources.get(id);
    }

    /**
     * Get all resources in a group
     * @param {string} groupName - Group name
     * @returns {Array} - Array of resources in the group
     */
    getGroupResources(groupName) {
      if (!this.groups.has(groupName)) {
        return [];
      }

      return Array.from(this.groups.get(groupName))
        .map(id => ({ id, resource: this.resources.get(id) }))
        .filter(item => item.resource !== undefined);
    }

    /**
     * Get status information
     * @returns {Object} - Status information
     */
    getStatus() {
      const groupStats = {};
      for (const [groupName, group] of this.groups.entries()) {
        groupStats[groupName] = group.size;
      }

      return {
        totalResources: this.resources.size,
        cleanupFunctions: this.cleanupFunctions.size,
        groups: groupStats
      };
    }
  }

  // Create global instance
  window.resourceManager = new ResourceManager('GlobalResourceManager');
})();
