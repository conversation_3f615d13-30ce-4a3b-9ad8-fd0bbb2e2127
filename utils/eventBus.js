/**
 * Event Bus
 * Centralized event system for module communication
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  "use strict";

  const logger = window.logger
    ? window.logger.createLogger("EventBus")
    : console;

  class EventBus {
    constructor() {
      this.events = {};
      this.pendingEvents = new Map();
      this.debug = false;

      // Track subscribers for better coordination
      this.subscribers = new Map();

      // Simple deduplication of events
      this.recentEvents = new Map();
      this.deduplicationWindow = 500; // ms
    }

    /**
     * Subscribe to an event
     * @param {string} eventName - Name of the event
     * @param {Function} callback - Callback function
     * @param {Object} options - Options object
     * @param {string} options.id - Subscriber ID for tracking
     * @param {boolean} options.once - Whether to remove after first trigger
     */
    on(eventName, callback, options = {}) {
      if (!this.events[eventName]) {
        this.events[eventName] = [];
      }

      const subscriberId =
        options.id || `sub_${Math.random().toString(36).substr(2, 9)}`;

      this.events[eventName].push({
        callback,
        id: subscriberId,
        once: !!options.once,
      });

      // Track subscriber
      this.subscribers.set(subscriberId, {
        eventName,
        active: true,
        lastCalled: null,
      });

      if (this.debug) {
        logger.log(`[EventBus] Subscribed to ${eventName}`, {
          id: subscriberId,
        });
      }

      return subscriberId;
    }

    /**
     * Subscribe to an event once
     * @param {string} eventName - Name of the event
     * @param {Function} callback - Callback function
     */
    once(eventName, callback, options = {}) {
      return this.on(eventName, callback, { ...options, once: true });
    }

    /**
     * Unsubscribe from an event
     * @param {string} eventName - Name of the event
     * @param {string|Function} idOrCallback - Subscriber ID or callback function
     */
    off(eventName, idOrCallback) {
      if (!this.events[eventName]) return;

      if (typeof idOrCallback === "function") {
        // Remove by callback reference
        this.events[eventName] = this.events[eventName].filter(
          (sub) => sub.callback !== idOrCallback,
        );
      } else if (typeof idOrCallback === "string") {
        // Remove by ID
        this.events[eventName] = this.events[eventName].filter(
          (sub) => sub.id !== idOrCallback,
        );
        // Update subscriber tracking
        if (this.subscribers.has(idOrCallback)) {
          this.subscribers.delete(idOrCallback);
        }
      }

      if (this.debug) {
        logger.log(`[EventBus] Unsubscribed from ${eventName}`);
      }
    }

    /**
     * Emit an event with data
     * @param {string} eventName - Name of the event
     * @param {*} data - Data to pass to subscribers
     * @param {Object} options - Options object
     * @param {boolean} options.deduplicate - Whether to deduplicate events
     */
    emit(eventName, data = {}, options = {}) {
      // Skip if no subscribers
      if (!this.events[eventName] || this.events[eventName].length === 0) {
        return false;
      }

      // Simple deduplication
      if (options.deduplicate) {
        const eventKey = `${eventName}:${JSON.stringify(data)}`;
        const now = Date.now();

        if (this.recentEvents.has(eventKey)) {
          const lastTime = this.recentEvents.get(eventKey);
          if (now - lastTime < this.deduplicationWindow) {
            if (this.debug) {
              logger.log(`[EventBus] Deduplicated event: ${eventName}`);
            }
            return false;
          }
        }

        this.recentEvents.set(eventKey, now);
      }

      // Create a token for this event emission to prevent cascading
      const emitToken = Math.random().toString(36).substr(2, 9);

      if (this.debug) {
        logger.log(`[EventBus] Emitting ${eventName}`, { token: emitToken });
      }

      // Execute subscribers
      const subscribers = [...this.events[eventName]];
      const onceSubs = [];

      subscribers.forEach((sub) => {
        try {
          sub.callback(data, { token: emitToken });

          // Track last called time
          if (this.subscribers.has(sub.id)) {
            const subInfo = this.subscribers.get(sub.id);
            subInfo.lastCalled = Date.now();
            this.subscribers.set(sub.id, subInfo);
          }

          // Track once subscribers for removal
          if (sub.once) {
            onceSubs.push(sub.id);
          }
        } catch (error) {
          window.errorHandler.handleError(
            error,
            `Error in ${eventName} subscriber`,
          );
        }
      });

      // Remove once subscribers
      onceSubs.forEach((id) => {
        this.off(eventName, id);
      });

      return true;
    }

    /**
     * Queue an event to be emitted after a delay
     * @param {string} eventName - Name of the event
     * @param {*} data - Data to pass to subscribers
     * @param {number} delay - Delay in milliseconds
     */
    emitDelayed(eventName, data = {}, delay = 0) {
      const timeoutId = setTimeout(() => {
        this.emit(eventName, data);
        this.pendingEvents.delete(timeoutId);
      }, delay);

      this.pendingEvents.set(timeoutId, { eventName, data });
      return timeoutId;
    }

    /**
     * Cancel a delayed event
     * @param {number} timeoutId - ID returned from emitDelayed
     */
    cancelDelayed(timeoutId) {
      if (this.pendingEvents.has(timeoutId)) {
        clearTimeout(timeoutId);
        this.pendingEvents.delete(timeoutId);
        return true;
      }
      return false;
    }

    /**
     * Clear all subscribers for an event
     * @param {string} eventName - Name of the event to clear
     */
    clear(eventName) {
      if (eventName) {
        // Clear specific event
        if (this.events[eventName]) {
          // Update subscriber tracking
          this.events[eventName].forEach((sub) => {
            if (this.subscribers.has(sub.id)) {
              this.subscribers.delete(sub.id);
            }
          });

          delete this.events[eventName];
        }
      } else {
        // Clear all events
        this.events = {};
        this.subscribers.clear();
      }

      if (this.debug) {
        logger.log(`[EventBus] Cleared ${eventName || "all"} events`);
      }
    }

    /**
     * Get information about active subscribers
     * @returns {Object} - Information about subscribers
     */
    getStatus() {
      const result = {
        events: {},
        pendingEvents: this.pendingEvents.size,
        totalSubscribers: this.subscribers.size,
      };

      // Count subscribers per event
      Object.keys(this.events).forEach((eventName) => {
        result.events[eventName] = this.events[eventName].length;
      });

      return result;
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether to enable debug mode
     */
    setDebug(enabled) {
      this.debug = enabled;
    }
  }

  // Create global instance
  window.eventBus = new EventBus();

  // Simple shorthand for DOM events
  document.on = (eventName, selector, callback) => {
    const element =
      typeof selector === "string"
        ? document.querySelector(selector)
        : selector;

    if (!element) {
      logger.warn(`[EventBus] Element not found for selector: ${selector}`);
      return null;
    }

    element.addEventListener(eventName, callback);
    return { element, eventName, callback };
  };

  document.off = (handle) => {
    if (handle && handle.element && handle.eventName && handle.callback) {
      handle.element.removeEventListener(handle.eventName, handle.callback);
      return true;
    }
    return false;
  };
})();
