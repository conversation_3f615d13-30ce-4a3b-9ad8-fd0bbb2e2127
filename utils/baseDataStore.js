/**
 * Base Data Store
 * Provides standardized data handling with throttling, validation, and subscriber management
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  "use strict";

  const logger = window.logger
    ? window.logger.createLogger("BaseDataStore")
    : console;

  /**
   * Base class for data stores with throttled updates and subscriber management
   */
  class BaseDataStore {
    /**
     * Create a new data store
     * @param {Object} options - Configuration options
     * @param {string} options.name - Store name for logging
     * @param {number} options.throttleMs - Throttle delay in milliseconds
     * @param {number} options.maxItems - Maximum number of items to store
     * @param {boolean} options.validateData - Whether to validate data
     * @param {Function} options.validator - Custom data validator function
     * @param {Function} options.transformer - Custom data transformer function
     * @param {Function} options.merger - Custom data merge function for updates
     */
    constructor(options = {}) {
      this.options = Object.assign(
        {
          name: "DataStore",
          throttleMs: 200,
          maxItems: 10000,
          validateData: true,
          validator: null,
          transformer: null,
          merger: null,
        },
        options,
      );

      // Create logger
      this.logger = window.logger
        ? window.logger.createLogger(this.options.name)
        : {
            debug: console.debug.bind(console),
            info: console.log.bind(console),
            warn: console.warn.bind(console),
            error: console.error.bind(console),
          };

      // Data storage
      this.data = [];
      this.subscribers = new Set();
      this.pendingUpdates = [];
      this.isUpdating = false;
      this.lastUpdateTime = 0;
      this.updateToken = null;

      // Create throttled update function
      this.throttledUpdate = this._createThrottledFunction(
        this._processUpdates.bind(this),
        this.options.throttleMs,
      );
    }

    /**
     * Subscribe to data updates
     * @param {Function} callback - Callback function to receive updates
     * @returns {Function} - Unsubscribe function
     */
    subscribe(callback) {
      if (typeof callback !== "function") {
        throw new Error("Subscriber must be a function");
      }

      this.subscribers.add(callback);
      this.logger.debug(
        `New subscriber added, total: ${this.subscribers.size}`,
      );

      // Return unsubscribe function
      return () => {
        this.subscribers.delete(callback);
        this.logger.debug(
          `Subscriber removed, remaining: ${this.subscribers.size}`,
        );
      };
    }

    /**
     * Add data to the store
     * @param {*} newData - Data to add
     * @param {Object} options - Update options
     * @param {boolean} options.immediate - Whether to update immediately
     * @param {string} options.source - Source of the update
     * @returns {boolean} - Whether the data was valid and queued
     */
    addData(newData, options = {}) {
      // Validate data if enabled
      if (this.options.validateData) {
        const valid = this._validateData(newData);
        if (!valid) {
          this.logger.warn("Invalid data rejected", newData);
          return false;
        }
      }

      // Transform data if transformer exists
      const transformedData = this.options.transformer
        ? this.options.transformer(newData)
        : newData;

      // Add to pending updates
      this.pendingUpdates.push({
        data: transformedData,
        timestamp: Date.now(),
        source: options.source || "unknown",
      });

      // Process immediately or schedule update
      if (options.immediate) {
        this._processUpdates();
      } else {
        this.throttledUpdate();
      }

      return true;
    }

    /**
     * Clear all data in the store
     * @param {Object} options - Clear options
     * @param {string} options.reason - Reason for clearing
     */
    clearData(options = {}) {
      this.logger.info(
        `Clearing data store${options.reason ? ` (${options.reason})` : ""}`,
      );

      // Generate new update token to invalidate any pending updates
      this.updateToken = Math.random().toString(36).substring(2, 15);

      // Clear data and pending updates
      this.data = [];
      this.pendingUpdates = [];
      this.isUpdating = false;

      // Notify subscribers of the clear
      this._notifySubscribers([], {
        type: "clear",
        reason: options.reason || "manual",
        token: this.updateToken,
      });
    }

    /**
     * Get all data in the store
     * @returns {Array} - Current data
     */
    getData() {
      return [...this.data];
    }

    /**
     * Get store status information
     * @returns {Object} - Status information
     */
    getStatus() {
      return {
        itemCount: this.data.length,
        subscriberCount: this.subscribers.size,
        pendingUpdates: this.pendingUpdates.length,
        lastUpdateTime: this.lastUpdateTime,
        isUpdating: this.isUpdating,
      };
    }

    /**
     * Process pending updates
     * @private
     */
    _processUpdates() {
      if (this.isUpdating || this.pendingUpdates.length === 0) {
        return;
      }

      this.isUpdating = true;
      const currentToken = (this.updateToken = Math.random()
        .toString(36)
        .substring(2, 15));
      const updates = [...this.pendingUpdates];
      this.pendingUpdates = [];

      try {
        // Process all pending updates
        updates.forEach((update) => {
          this._integrateUpdate(update.data, update.source);
        });

        // Enforce max items limit
        if (this.options.maxItems && this.data.length > this.options.maxItems) {
          this.data = this.data.slice(-this.options.maxItems);
        }

        this.lastUpdateTime = Date.now();

        // Notify subscribers if the token is still valid
        if (currentToken === this.updateToken) {
          this._notifySubscribers(this.data, {
            type: "update",
            count: updates.length,
            token: currentToken,
          });
        } else {
          this.logger.debug("Update skipped: token changed during processing");
        }
      } catch (error) {
        this.logger.error("Error processing updates", error);
      } finally {
        this.isUpdating = false;

        // If new updates arrived during processing, schedule another update
        if (this.pendingUpdates.length > 0) {
          this.throttledUpdate();
        }
      }
    }

    /**
     * Integrate an update into the data store
     * @param {*} updateData - Data to integrate
     * @param {string} source - Source of the update
     * @private
     */
    _integrateUpdate(updateData, source) {
      // Default implementation: append to data array
      // Override this method in subclasses for custom update behavior

      if (Array.isArray(updateData)) {
        // For arrays, append all items
        this.data.push(...updateData);
      } else {
        // For single items, append the item
        this.data.push(updateData);
      }
    }

    /**
     * Validate incoming data
     * @param {*} data - Data to validate
     * @returns {boolean} - Whether the data is valid
     * @private
     */
    _validateData(data) {
      // Use custom validator if provided
      if (typeof this.options.validator === "function") {
        return this.options.validator(data);
      }

      // Default validation: ensure data is not null or undefined
      return data !== null && data !== undefined;
    }

    /**
     * Notify subscribers of a data update
     * @param {Array} data - Current data
     * @param {Object} meta - Metadata about the update
     * @private
     */
    _notifySubscribers(data, meta = {}) {
      // Make a copy to prevent modification
      const immutableData = Object.freeze([...data]);

      this.subscribers.forEach((subscriber) => {
        try {
          subscriber(immutableData, meta);
        } catch (error) {
          this.logger.error("Error in subscriber", error);
          // Continue notifying other subscribers
        }
      });
    }

    /**
     * Create a throttled function
     * @param {Function} func - Function to throttle
     * @param {number} limit - Throttle limit in milliseconds
     * @returns {Function} - Throttled function
     * @private
     */
    _createThrottledFunction(func, limit) {
      let lastCall = 0;
      let timeout = null;

      return function (...args) {
        const now = Date.now();
        const remaining = limit - (now - lastCall);

        // Clear any existing timeout
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }

        if (remaining <= 0) {
          // If enough time has elapsed, call immediately
          lastCall = now;
          func.apply(this, args);
        } else {
          // Otherwise, schedule for the remaining time
          timeout = setTimeout(() => {
            lastCall = Date.now();
            timeout = null;
            func.apply(this, args);
          }, remaining);
        }
      };
    }

    /**
     * Clean up the data store
     */
    destroy() {
      this.logger.info("Destroying data store");

      // Clear data and subscribers
      this.data = [];
      this.subscribers.clear();
      this.pendingUpdates = [];

      // Cancel any pending updates
      if (typeof this.throttledUpdate.cancel === "function") {
        this.throttledUpdate.cancel();
      }
    }
  }

  // Specialized Bar Data Store for time-series data
  class BarDataStore extends BaseDataStore {
    /**
     * Create a new bar data store
     * @param {Object} options - Configuration options
     */
    constructor(options = {}) {
      super(
        Object.assign(
          {
            name: "BarDataStore",
            validateData: true,
            // Default validator for bar data
            validator: (bar) => {
              if (!bar) return false;

              // Check for essential bar properties
              if (typeof bar.time !== "number" || !isFinite(bar.time)) {
                console.warn("Invalid bar time:", bar.time, bar);
                return false;
              }

              // Check for OHLC data
              const hasOHLC = [
                typeof bar.open === "number",
                typeof bar.high === "number",
                typeof bar.low === "number",
                typeof bar.close === "number",
              ].every(Boolean);

              return hasOHLC;
            },
          },
          options,
        ),
      );

      // Bar-specific properties
      this.barMap = new Map(); // Map of time -> bar for quick lookups
      this.lastBarTime = 0;
    }

    /**
     * Override to handle bar data integration
     * @param {Object|Array} updateData - Bar data to integrate
     * @param {string} source - Source of the update
     * @private
     */
    _integrateUpdate(updateData, source) {
      // Handle array of bars
      if (Array.isArray(updateData)) {
        updateData.forEach((bar) => this._integrateBar(bar, source));

        // Sort bars by time if we have new data
        if (updateData.length > 0) {
          this._sortBars();
        }
      }
      // Handle single bar
      else if (updateData && typeof updateData === "object") {
        this._integrateBar(updateData, source);
        this._sortBars();
      }
    }

    /**
     * Integrate a single bar into the data
     * @param {Object} bar - Bar data
     * @param {string} source - Source of the update
     * @private
     */
    _integrateBar(bar, source) {
      if (!bar || typeof bar.time !== "number") return;

      const time = bar.time;
      const existingBar = this.barMap.get(time);

      if (existingBar) {
        // Update existing bar
        if (this.options.merger) {
          // Use custom merger if provided
          const merged = this.options.merger(existingBar, bar, source);
          Object.assign(existingBar, merged);
        } else {
          // Default merge strategy: update all properties
          Object.assign(existingBar, bar);
        }
      } else {
        // Add new bar
        this.data.push(bar);
        this.barMap.set(time, bar);

        // Update last bar time
        if (time > this.lastBarTime) {
          this.lastBarTime = time;
        }
      }
    }

    /**
     * Sort bars by time
     * @private
     */
    _sortBars() {
      this.data.sort((a, b) => a.time - b.time);
    }

    /**
     * Get a specific bar by time
     * @param {number} time - Bar time
     * @returns {Object|null} - Bar or null if not found
     */
    getBar(time) {
      return this.barMap.get(time) || null;
    }

    /**
     * Get the last bar
     * @returns {Object|null} - Last bar or null if no bars
     */
    getLastBar() {
      if (this.data.length === 0) return null;
      return this.data[this.data.length - 1];
    }

    /**
     * Get bars within a time range
     * @param {number} startTime - Start time
     * @param {number} endTime - End time
     * @returns {Array} - Bars within the range
     */
    getBarRange(startTime, endTime) {
      return this.data.filter(
        (bar) => bar.time >= startTime && bar.time <= endTime,
      );
    }

    /**
     * Override clear to also reset bar map
     * @param {Object} options - Clear options
     */
    clearData(options = {}) {
      super.clearData(options);
      this.barMap.clear();
      this.lastBarTime = 0;
    }
  }

  // Expose the classes
  window.BaseDataStore = BaseDataStore;
  window.BarDataStore = BarDataStore;
})();
