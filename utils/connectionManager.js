/**
 * Connection Manager - Centralized connection stability management
 * Coordinates WebSocket connections, handles tab switching, and manages pair switching
 */

(function() {
  'use strict';

  class ConnectionManager {
    constructor() {
      this.managers = new Map();
      this.connectionStates = new Map();
      this.visibilityHandlers = new Set();
      this.pairSwitchHandlers = new Set();
      this.isInitialized = false;
      
      // Simple, unified state tracking
      this.lastVisibilityChange = Date.now();
      this.lastPairSwitch = Date.now();
      this.isTabVisible = !document.hidden;
      this.visibilityDebounceTimeout = null;
      
        // Enhanced Sleep/wake detection with state coordination
      this.lastActiveTime = Date.now();
      this.sleepDetectionInterval = null;
      this.isSleepDetected = false;
      this.sleepThreshold = 60000; // 1 minute - adjusted for better responsiveness
      this.wakeRecoveryInProgress = false;
      this.wakeRecoveryTimeout = null;
      this.wakeRecoveryAttempts = 0;
      this.maxWakeRecoveryAttempts = 3;
      this.sleepDetectionEnabled = true;
      
      // Ready event coordination system
      this.pendingReadyCheck = null;
      this.expectedModules = new Set(['orderbook', 'charts', 'indicators', 'popupchart']);
      this.readyModules = new Set();
      this.currentPair = 'BTC';
      
      // Simple performance tracking
      this.performanceMetrics = {
        visibilityChanges: 0,
        pairSwitches: 0,
        reconnections: 0,
        sleepDetections: 0,
        lastHealthCheck: Date.now()
      };
      
      this.init();
    }

    init() {
      console.log('[ConnectionManager] Initializing connection manager');
      
      // Check if eventBus is available and use it for better coordination
      if (window.eventBus) {
        this.useEventBus = true;
        window.eventBus.on('visibility-change', (data) => {
          if (data.state === 'visible') {
            this.handleVisibilityRestore();
          } else {
            this.handleVisibilityHide();
          }
        }, { id: 'connection-manager' });

        window.eventBus.on('pair-switch', (data) => {
          const { from, to } = data;
          if (!from || !to) return;

          const now = Date.now();
          this.lastPairSwitch = now;
          this.performanceMetrics.pairSwitches++;
          this.currentPair = to;

          console.log(`[ConnectionManager] Handling pair switch: ${from} -> ${to}`);

          // Reset ready modules for new pair
          this.readyModules.clear();

          // Start ready check with timeout
          this.startReadyCheck(to);
        }, { id: 'connection-manager-pair' });
      } else {
        this.useEventBus = false;
        // Fall back to standard event listeners
        this.setupGlobalVisibilityHandler();
        this.setupPairSwitchHandler();
      }

      this.setupSleepDetection();
      
      this.isInitialized = true;
      console.log('[ConnectionManager] Connection manager initialized');
    }

    // Register a WebSocket manager
    registerManager(name, manager) {
      // Prevent duplicate managers for the same exchange
      if (this.managers.has(name)) {
        console.warn(`[ConnectionManager] Manager already registered: ${name}, skipping duplicate`); 
        return this.managers.get(name);
      }

      this.managers.set(name, manager);
      this.connectionStates.set(name, {
        lastHiddenTime: null,
        lastDataTime: Date.now(),
        reconnectAttempts: 0,
        endpoint: manager.endpoint || 'unknown'
      });
      console.log(`[ConnectionManager] Registered manager: ${name}`);
      return manager;
    }

    // Improved visibility change handler with transition locking
    setupGlobalVisibilityHandler() {
      // Add a transition lock to prevent duplicate visibility handling
      this.isHandlingVisibilityChange = false;
      this.pendingVisibilityState = null;

      const handleVisibilityChange = () => {
        const now = Date.now();
        const newVisibilityState = document.visibilityState;

        // Skip if we're already handling a visibility change
        if (this.isHandlingVisibilityChange) {
          this.pendingVisibilityState = newVisibilityState;
          return;
        }

        // Simple debouncing - prevent rapid changes
        if (this.visibilityDebounceTimeout) {
          clearTimeout(this.visibilityDebounceTimeout);
        }

        this.isHandlingVisibilityChange = true;
        this.visibilityDebounceTimeout = setTimeout(() => {
          this.lastVisibilityChange = now;
          this.isTabVisible = newVisibilityState === 'visible';
          this.performanceMetrics.visibilityChanges++;

          console.log(`[ConnectionManager] Global visibility change: ${newVisibilityState}`);

          if (newVisibilityState === 'visible') {
            this.handleVisibilityRestore();
          } else {
            this.handleVisibilityHide();
          }

          // Release the lock after a short delay to ensure completion
          setTimeout(() => {
            this.isHandlingVisibilityChange = false;
            // Handle any pending visibility change
            if (this.pendingVisibilityState !== null && this.pendingVisibilityState !== newVisibilityState) {
              const pendingState = this.pendingVisibilityState;
              this.pendingVisibilityState = null;
              // Simulate a visibility change event
              document.dispatchEvent(new Event('visibilitychange'));
            }
          }, 500);
        }, 200); // Increased to 200ms for better stability
      };
      
      document.addEventListener('visibilitychange', handleVisibilityChange);
      this.visibilityHandlers.add(handleVisibilityChange);
    }

    // Enhanced resubscription with transition state awareness
    handleVisibilityRestore() {
      // CRITICAL FIX: Check transition state before resubscribing
      if (window.chartTransitionState && window.chartTransitionState.isTransitioning) {
        console.log('[ConnectionManager] Chart transition in progress, delaying resubscription');
        // Wait for transition to complete
        setTimeout(() => {
          this.handleVisibilityRestore();
        }, 1000);
        return;
      }
      
      // Only trigger a full refresh if the page was actually hidden
      if (this.lastHiddenTime && (Date.now() - this.lastHiddenTime > 500)) {
        if (window.chartSwitcher && typeof window.chartSwitcher.forceFullRefresh === 'function') {
          window.chartSwitcher.forceFullRefresh();
        }
        this.lastHiddenTime = null; // Reset after refresh
      }

      console.log('[ConnectionManager] Visibility restored, triggering resubscription');
      this.performanceMetrics.visibilityRestores++;
      
      // Enhanced resubscription with better coordination
      this.triggerResubscription();
    }

    // Simple visibility hide - NO COMPLEX LOGIC
    handleVisibilityHide() {
      console.log('[ConnectionManager] Handling visibility hide');
      this.lastHiddenTime = Date.now();
      // Dispatch unified visibility event for all modules
      window.dispatchVisibilityEvent('hide', {
        hiddenTime: Date.now()
      });
      
      // Simple: Just mark as hidden, don't disconnect
      for (const [name, manager] of this.managers) {
        try {
          const state = this.connectionStates.get(name);
          if (state) {
            state.lastHiddenTime = Date.now();
          }
        } catch (error) {
          console.error(`[ConnectionManager] Error handling visibility hide for ${name}:`, error);
        }
      }
    }

    // Simple reconnection trigger
    triggerReconnection(name, manager) {
      try {
        if (manager.reconnect) {
          console.log(`[ConnectionManager] Triggering reconnection for ${name}`);
          manager.reconnect(true);
          this.performanceMetrics.reconnections++;
        }
      } catch (error) {
        console.error(`[ConnectionManager] Error triggering reconnection for ${name}:`, error);
      }
    }

    // Enhanced resubscription with better coordination and locking
    triggerResubscription() {
      // Prevent multiple resubscriptions in rapid succession
      if (this.resubscriptionInProgress) {
        console.log('[ConnectionManager] Resubscription already in progress, queueing');
        this.resubscriptionQueued = true;
        return;
      }

      this.resubscriptionInProgress = true;

      // Use eventBus if available, otherwise fall back to custom event
      if (this.useEventBus && window.eventBus) {
        window.eventBus.emit('visibility-restored', {
          hiddenDuration: this.lastVisibilityChange ? Date.now() - this.lastVisibilityChange : 0
        });
      } else {
        // Dispatch unified visibility event for all modules
        window.dispatchVisibilityEvent('restore', {
          hiddenDuration: this.lastVisibilityChange ? Date.now() - this.lastVisibilityChange : 0
        });
      }

      // Reset ready modules for visibility restore
      this.readyModules.clear();

      // Start ready check for current pair
      this.startReadyCheck(this.currentPair);

      // Release lock after a delay to prevent cascading resubscriptions
      setTimeout(() => {
        this.resubscriptionInProgress = false;

        // Process any queued resubscription
        if (this.resubscriptionQueued) {
          this.resubscriptionQueued = false;
          console.log('[ConnectionManager] Processing queued resubscription');
          this.triggerResubscription();
        }
      }, 2000);
    }

    // Simple pair switching handler
    setupPairSwitchHandler() {
      const handlePairSwitch = (event) => {
        const { from, to } = event.detail || {};
        if (!from || !to) return;
        
        const now = Date.now();
        this.lastPairSwitch = now;
        this.performanceMetrics.pairSwitches++;
        this.currentPair = to;
        
        console.log(`[ConnectionManager] Handling pair switch: ${from} -> ${to}`);
        
        // Reset ready modules for new pair
        this.readyModules.clear();
        
        // Start ready check with timeout
        this.startReadyCheck(to);
      };
      
      window.addEventListener('pairChanged', handlePairSwitch);
      this.pairSwitchHandlers.add(handlePairSwitch);
    }

    // Ready event coordination system
    startReadyCheck(pair) {
      // Clear any existing ready check
      if (this.pendingReadyCheck) {
        clearTimeout(this.pendingReadyCheck);
      }
      
      // Listen for module ready events
      const handleModuleReady = (event) => {
        const { module, pair: readyPair } = event.detail || {};
        if (readyPair !== pair) return; // Only count ready events for current pair
        
        this.readyModules.add(module);
        
        // Check if all expected modules are ready
        if (this.readyModules.size >= this.expectedModules.size) {
          this.onAllModulesReady(pair);
        }
      };
      
      // Add one-time listener for module ready events
      const readyListener = (event) => {
        handleModuleReady(event);
        window.removeEventListener('module-ready', readyListener);
      };
      window.addEventListener('module-ready', readyListener);
      
      // Set timeout fallback (3 seconds for complex operations)
      this.pendingReadyCheck = setTimeout(() => {
        this.onAllModulesReady(pair);
      }, 3000);
    }
    
    onAllModulesReady(pair) {
      // Clear timeout
      if (this.pendingReadyCheck) {
        clearTimeout(this.pendingReadyCheck);
        this.pendingReadyCheck = null;
      }
      
      // Simple: Resubscribe all managers to new pair
      for (const [name, manager] of this.managers) {
        try {
          if (manager.resubscribeAllForPair) {
            manager.resubscribeAllForPair(pair);
          }
        } catch (error) {
          console.error(`[ConnectionManager] Error handling pair switch for ${name}:`, error);
        }
      }
      
      // Clear ready modules for next switch
      this.readyModules.clear();

      // Reset sleep detection after successful ready state
      if (this.isSleepDetected) {
        this.isSleepDetected = false;
        this.wakeRecoveryInProgress = false;
        this.wakeRecoveryAttempts = 0;
        console.info('[ConnectionManager] Wake recovery completed successfully');
      }
    }

    // Enhanced wake recovery with retry mechanism
    handleWakeRecovery() {
      if (this.wakeRecoveryInProgress) {
        return; // Already in progress
      }

      this.wakeRecoveryInProgress = true;
      this.wakeRecoveryAttempts++;

      // Disable sleep detection during recovery to prevent cascading detections
      this.sleepDetectionEnabled = false;

      console.warn(`[ConnectionManager] Initiating wake recovery (attempt ${this.wakeRecoveryAttempts}/${this.maxWakeRecoveryAttempts})`);

      // Forcibly close and reconnect all WebSocket connections
      for (const [name, manager] of this.managers) {
        try {
          if (manager.forceReconnect) {
            console.log(`[ConnectionManager] Force reconnecting ${name} after sleep`);
            manager.forceReconnect();
          } else if (manager.reconnect) {
            console.log(`[ConnectionManager] Reconnecting ${name} after sleep`);
            manager.reconnect(true);
          }
        } catch (error) {
          console.error(`[ConnectionManager] Error during wake recovery for ${name}:`, error);
        }
      }

      // Schedule recovery check
      this.wakeRecoveryTimeout = setTimeout(() => {
        // Check if all connections are restored
        const allConnected = Array.from(this.managers.entries()).every(([name, manager]) => {
          return !manager.isConnected || manager.isConnected();
        });

        if (allConnected) {
          console.log('[ConnectionManager] All connections restored after sleep');
          this.isSleepDetected = false;
          this.wakeRecoveryInProgress = false;
          this.wakeRecoveryAttempts = 0;
          this.sleepDetectionEnabled = true;
        } else if (this.wakeRecoveryAttempts < this.maxWakeRecoveryAttempts) {
          console.warn(`[ConnectionManager] Wake recovery incomplete, retrying...`);
          this.wakeRecoveryInProgress = false;
          this.handleWakeRecovery(); // Retry
        } else {
          console.error('[ConnectionManager] Wake recovery failed after maximum attempts');
          this.wakeRecoveryInProgress = false;
          this.sleepDetectionEnabled = true;
          // Final attempt - trigger visibility restore which often helps
          this.handleVisibilityRestore();
        }
      }, 5000); // Give connections 5 seconds to establish
    }

    // Simple performance monitoring
    setupPerformanceMonitoring() {
      // Use resourceManager to track the interval
      window.resourceManager.registerInterval(
        'connectionManager-performance-monitoring',
        () => {
          const now = Date.now();
          const timeSinceLastHealthCheck = now - this.performanceMetrics.lastHealthCheck;

          if (timeSinceLastHealthCheck > 600000) { // Log every 10 minutes
            // Use the logger if available
            const logger = window.logger ? window.logger.createLogger('ConnectionManager') : {
              info: console.log.bind(console)
            };

            logger.info('Performance metrics:', {
              visibilityChanges: this.performanceMetrics.visibilityChanges,
              pairSwitches: this.performanceMetrics.pairSwitches,
              reconnections: this.performanceMetrics.reconnections,
              activeConnections: Array.from(this.managers.keys()).filter(name => {
                const manager = this.managers.get(name);
                return manager && manager.isConnected();
              }).length,
              lastHealthCheck: new Date(this.performanceMetrics.lastHealthCheck).toISOString()
            });

            this.performanceMetrics.lastHealthCheck = now;

            // Emit event for performance metrics
            if (window.eventBus) {
              window.eventBus.emit('connection-performance-metrics', {
                timestamp: now,
                metrics: this.performanceMetrics
              });
            }
          }
        },
        300000, // Check every 5 minutes
        'connectionManager'
      );
    }

    // Simple cleanup
    destroy() {
      console.log('[ConnectionManager] Destroying connection manager');
      
      if (this.visibilityDebounceTimeout) {
        clearTimeout(this.visibilityDebounceTimeout);
        this.visibilityDebounceTimeout = null;
      }
      
      // CRITICAL FIX: Clean up sleep detection
      if (this.sleepDetectionInterval) {
        clearInterval(this.sleepDetectionInterval);
        this.sleepDetectionInterval = null;
      }
      
      this.visibilityHandlers.forEach(handler => {
        document.removeEventListener('visibilitychange', handler);
      });
      this.visibilityHandlers.clear();
      
      this.managers.clear();
      this.connectionStates.clear();
      this.pairSwitchHandlers.clear();
      
      this.isInitialized = false;
    }

    // Simple getter for external use
    getVisibilityState() {
      return {
        isTabVisible: this.isTabVisible,
        lastVisibilityChange: this.lastVisibilityChange,
        visibilityChanges: this.performanceMetrics.visibilityChanges
      };
    }

    // CRITICAL FIX: Sleep/wake detection system
    setupSleepDetection() {
      // Track user activity to detect sleep
      const updateActivity = () => {
        this.lastActiveTime = Date.now();
        if (this.isSleepDetected) {
          this.handleWakeFromSleep();
        }
      };
      
      // Monitor for user activity
      ['mousedown', 'mousemove', 'keydown', 'touchstart', 'scroll'].forEach(event => {
        document.addEventListener(event, updateActivity, { passive: true });
      });
      
      // Monitor for visibility changes (tab switching)
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          updateActivity();
        }
      });
      
      // Start sleep detection interval
      this.sleepDetectionInterval = setInterval(() => {
        const now = Date.now();
        const timeSinceActivity = now - this.lastActiveTime;
        
        if (timeSinceActivity > this.sleepThreshold && !this.isSleepDetected) {
          this.isSleepDetected = true;
          this.performanceMetrics.sleepDetections++;
          this.handleSleep();
        }
      }, 10000); // Check every 10 seconds
    }
    
    // Handle computer going to sleep
    handleSleep() {
      // Mark all connections as potentially stale
      for (const [name, manager] of this.managers) {
        try {
          const state = this.connectionStates.get(name);
          if (state) {
            state.lastSleepTime = Date.now();
            state.needsWakeRecovery = true;
          }
        } catch (error) {
          console.error(`[ConnectionManager] Error handling sleep for ${name}:`, error);
        }
      }
      
      // Dispatch sleep event for modules
      window.dispatchEvent(new CustomEvent('computer-sleep', {
        detail: { timestamp: Date.now() }
      }));
    }
    
    // Handle computer waking from sleep
    handleWakeFromSleep() {
      // Prevent multiple simultaneous wake recoveries
      if (this.wakeRecoveryInProgress) {
        return;
      }
      
      this.isSleepDetected = false;
      this.wakeRecoveryInProgress = true;
      
      // Clear any existing wake recovery timeout
      if (this.wakeRecoveryTimeout) {
        clearTimeout(this.wakeRecoveryTimeout);
      }
      
      // Wait a moment for system to stabilize
      this.wakeRecoveryTimeout = setTimeout(() => {
        this.performWakeRecovery();
      }, 2000);
    }
    
    // Perform comprehensive wake recovery
    performWakeRecovery() {
      console.log('[ConnectionManager] Performing wake recovery');
      
      // Check all connection states
      for (const [name, manager] of this.managers) {
        try {
          const state = this.connectionStates.get(name);
          if (state && state.needsWakeRecovery) {
            console.log(`[ConnectionManager] Recovering ${name} after wake`);
            
            // Force reconnection for all managers
            if (manager.reconnect) {
              manager.reconnect(true); // Force reconnection
            }
            
            state.needsWakeRecovery = false;
            state.lastWakeTime = Date.now();
          }
        } catch (error) {
          console.error(`[ConnectionManager] Error during wake recovery for ${name}:`, error);
        }
      }
      
      // Dispatch wake event for modules
      window.dispatchEvent(new CustomEvent('computer-wake', {
        detail: { 
          timestamp: Date.now(),
          sleepDuration: Date.now() - this.lastActiveTime
        }
      }));
      
      // Trigger visibility restore logic
      this.handleVisibilityRestore();
      
      // Reset wake recovery state
      this.wakeRecoveryInProgress = false;
      this.wakeRecoveryTimeout = null;
    }
  }

  // Create global instance
  window.connectionManager = new ConnectionManager();

  // Simple global functions for other modules to use
  window.isTabVisible = () => window.connectionManager?.isTabVisible || !document.hidden;
  window.getVisibilityState = () => window.connectionManager?.getVisibilityState();
  
  // Ready event coordination for modules
  window.signalModuleReady = (module, pair) => {
    window.dispatchEvent(new CustomEvent('module-ready', {
      detail: { module, pair, timestamp: Date.now() }
    }));
  };
  
  // Simple event dispatcher for modules that need visibility events
  window.dispatchVisibilityEvent = (type, data = {}) => {
    window.dispatchEvent(new CustomEvent(`visibility-${type}`, {
      detail: {
        timestamp: Date.now(),
        isTabVisible: window.isTabVisible(),
        ...data
      }
    }));
  };

})(); 