/**
 * Config Validator
 * Validates configuration values and applies defaults
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function() {
  'use strict';

  const logger = window.logger ? window.logger.createLogger('ConfigValidator') : console;

  /**
   * Configuration validator class
   */
  class ConfigValidator {
    /**
     * Create a new config validator
     */
    constructor() {
      this.schemas = new Map();
      this.validatedConfigs = new Map();
    }

    /**
     * Register a configuration schema
     * @param {string} name - Schema name
     * @param {Object} schema - Configuration schema
     */
    registerSchema(name, schema) {
      if (!name || typeof name !== 'string') {
        throw new Error('Schema name is required');
      }

      if (!schema || typeof schema !== 'object') {
        throw new Error('Schema must be an object');
      }

      this.schemas.set(name, schema);
      logger.info(`Registered schema: ${name}`);
    }

    /**
     * Validate a configuration against a schema
     * @param {string} schemaName - Schema name
     * @param {Object} config - Configuration to validate
     * @param {boolean} [strict=false] - Whether to throw on validation errors
     * @returns {Object} - Validated configuration with defaults
     */
    validate(schemaName, config, strict = false) {
      // Check if schema exists
      if (!this.schemas.has(schemaName)) {
        const error = new Error(`Schema '${schemaName}' not found`);
        if (strict) throw error;
        logger.error(error.message);
        return config;
      }

      const schema = this.schemas.get(schemaName);
      const result = {};
      const errors = [];

      // Process each property in schema
      for (const [key, propSchema] of Object.entries(schema)) {
        const value = config && config[key];
        const isRequired = propSchema.required === true;

        // Check if required property is missing
        if (isRequired && (value === undefined || value === null)) {
          errors.push(`Required property '${key}' is missing`);
          // Use default if available, otherwise skip
          if ('default' in propSchema) {
            result[key] = this._cloneValue(propSchema.default);
          }
          continue;
        }

        // If property is not provided and not required, use default
        if (value === undefined) {
          if ('default' in propSchema) {
            result[key] = this._cloneValue(propSchema.default);
          }
          continue;
        }

        // Validate type
        if (propSchema.type && !this._checkType(value, propSchema.type)) {
          errors.push(`Property '${key}' should be of type ${propSchema.type}`);
          // Use default if available, otherwise skip
          if ('default' in propSchema) {
            result[key] = this._cloneValue(propSchema.default);
          }
          continue;
        }

        // Validate enum
        if (propSchema.enum && !propSchema.enum.includes(value)) {
          errors.push(`Property '${key}' should be one of [${propSchema.enum.join(', ')}]`);
          // Use default if available, otherwise skip
          if ('default' in propSchema) {
            result[key] = this._cloneValue(propSchema.default);
          }
          continue;
        }

        // Validate minimum/maximum for numbers
        if (propSchema.type === 'number' || propSchema.type === 'integer') {
          if ('minimum' in propSchema && value < propSchema.minimum) {
            errors.push(`Property '${key}' should be >= ${propSchema.minimum}`);
            result[key] = propSchema.minimum;
            continue;
          }

          if ('maximum' in propSchema && value > propSchema.maximum) {
            errors.push(`Property '${key}' should be <= ${propSchema.maximum}`);
            result[key] = propSchema.maximum;
            continue;
          }
        }

        // Validate minLength/maxLength for strings
        if (propSchema.type === 'string') {
          if ('minLength' in propSchema && value.length < propSchema.minLength) {
            errors.push(`Property '${key}' should have length >= ${propSchema.minLength}`);
            // Use default if available, otherwise skip
            if ('default' in propSchema) {
              result[key] = this._cloneValue(propSchema.default);
            }
            continue;
          }

          if ('maxLength' in propSchema && value.length > propSchema.maxLength) {
            // Truncate string
            result[key] = value.substring(0, propSchema.maxLength);
            errors.push(`Property '${key}' truncated to length ${propSchema.maxLength}`);
            continue;
          }

          // Validate pattern
          if (propSchema.pattern) {
            const regex = new RegExp(propSchema.pattern);
            if (!regex.test(value)) {
              errors.push(`Property '${key}' does not match pattern ${propSchema.pattern}`);
              // Use default if available, otherwise skip
              if ('default' in propSchema) {
                result[key] = this._cloneValue(propSchema.default);
              }
              continue;
            }
          }
        }

        // Validate minItems/maxItems for arrays
        if (propSchema.type === 'array') {
          if ('minItems' in propSchema && value.length < propSchema.minItems) {
            errors.push(`Property '${key}' should have >= ${propSchema.minItems} items`);
            // Use default if available, otherwise skip
            if ('default' in propSchema) {
              result[key] = this._cloneValue(propSchema.default);
            }
            continue;
          }

          if ('maxItems' in propSchema && value.length > propSchema.maxItems) {
            // Truncate array
            result[key] = value.slice(0, propSchema.maxItems);
            errors.push(`Property '${key}' truncated to ${propSchema.maxItems} items`);
            continue;
          }

          // Validate items if specified
          if (propSchema.items && propSchema.items.type) {
            const invalidItems = value.filter(item => !this._checkType(item, propSchema.items.type));
            if (invalidItems.length > 0) {
              errors.push(`Property '${key}' contains ${invalidItems.length} items of invalid type`);
              // Filter out invalid items
              result[key] = value.filter(item => this._checkType(item, propSchema.items.type));
              continue;
            }
          }
        }

        // If we got here, the value is valid
        result[key] = value;
      }

      // Log validation errors
      if (errors.length > 0) {
        const errorMessage = `Configuration validation errors for '${schemaName}':\n- ${errors.join('\n- ')}`;
        if (strict) {
          throw new Error(errorMessage);
        } else {
          logger.warn(errorMessage);
        }
      }

      // Store validated config
      this.validatedConfigs.set(schemaName, result);

      return result;
    }

    /**
     * Get a validated configuration
     * @param {string} schemaName - Schema name
     * @returns {Object|null} - Validated configuration or null if not found
     */
    getValidatedConfig(schemaName) {
      return this.validatedConfigs.get(schemaName) || null;
    }

    /**
     * Check if a value matches a type
     * @param {*} value - Value to check
     * @param {string} type - Type to check against
     * @returns {boolean} - Whether the value matches the type
     * @private
     */
    _checkType(value, type) {
      switch (type) {
        case 'string':
          return typeof value === 'string';
        case 'number':
          return typeof value === 'number' && !isNaN(value);
        case 'integer':
          return typeof value === 'number' && !isNaN(value) && Number.isInteger(value);
        case 'boolean':
          return typeof value === 'boolean';
        case 'array':
          return Array.isArray(value);
        case 'object':
          return typeof value === 'object' && value !== null && !Array.isArray(value);
        case 'null':
          return value === null;
        case 'any':
          return true;
        default:
          return false;
      }
    }

    /**
     * Clone a value to avoid reference issues
     * @param {*} value - Value to clone
     * @returns {*} - Cloned value
     * @private
     */
    _cloneValue(value) {
      if (value === null || value === undefined) {
        return value;
      }

      if (Array.isArray(value)) {
        return [...value];
      }

      if (typeof value === 'object') {
        return {...value};
      }

      return value;
    }

    /**
     * Get all registered schemas
     * @returns {Array} - List of schema names
     */
    getRegisteredSchemas() {
      return Array.from(this.schemas.keys());
    }
  }

  // Create global instance
  window.configValidator = new ConfigValidator();

  // Register common schemas
  window.configValidator.registerSchema('chartColors', {
    background: {
      type: 'string',
      default: '#0f141a'
    },
    text: {
      type: 'string',
      default: '#D3D3D3'
    },
    grid: {
      type: 'string',
      default: '#2A2A2A'
    },
    upColor: {
      type: 'string',
      default: '#26a69a'
    },
    downColor: {
      type: 'string',
      default: '#ef5350'
    },
    wickUpColor: {
      type: 'string',
      default: '#26a69a'
    },
    wickDownColor: {
      type: 'string',
      default: '#ef5350'
    }
  });

  window.configValidator.registerSchema('webSocketConfig', {
    reconnectBaseDelay: {
      type: 'integer',
      minimum: 100,
      maximum: 10000,
      default: 1000
    },
    reconnectMaxDelay: {
      type: 'integer',
      minimum: 5000,
      maximum: 60000,
      default: 30000
    },
    maxReconnectAttempts: {
      type: 'integer',
      minimum: 1,
      maximum: 100,
      default: 10
    },
    pingInterval: {
      type: 'integer',
      minimum: 5000,
      maximum: 60000,
      default: 30000
    }
  });

  logger.info('Configuration validator initialized');
})();
