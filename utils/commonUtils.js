// commonUtils.js - Centralized common utility functions

(function() {
    'use strict';

    const commonUtils = {
        // Throttle function - ensures function is called at most once per specified period
        throttle: function(func, limit) {
            let inThrottle, lastFunc, lastRan;
            const wrapper = function() {
                const context = this;
                const args = arguments;
                const now = Date.now();
                
                if (!inThrottle) {
                    func.apply(context, args);
                    lastRan = now;
                    inThrottle = true;
                } else {
                    clearTimeout(lastFunc);
                    const timeSinceLastRun = now - lastRan;
                    
                    if (timeSinceLastRun >= limit) {
                        // Execute immediately if enough time has passed
                        func.apply(context, args);
                        lastRan = now;
                    } else {
                        // Use requestAnimationFrame for short delays, setTimeout for longer ones
                        const remainingTime = limit - timeSinceLastRun;
                        
                        if (remainingTime < 16) { // Less than one frame
                            // Use requestAnimationFrame for micro-timing
                            lastFunc = requestAnimationFrame(() => {
                                func.apply(context, args);
                                lastRan = Date.now();
                                inThrottle = false;
                            });
                        } else {
                            // Use setTimeout for longer delays
                            lastFunc = setTimeout(() => {
                                func.apply(context, args);
                                lastRan = Date.now();
                                inThrottle = false;
                            }, remainingTime);
                        }
                    }
                }
            };
            
            wrapper.cancel = function() {
                if (lastFunc) {
                    if (typeof lastFunc === 'number') {
                        cancelAnimationFrame(lastFunc);
                    } else {
                        clearTimeout(lastFunc);
                    }
                }
                inThrottle = false;
            };

            return wrapper;
        },

        // Debounce function - delays function execution until after specified wait period
        debounce: function(func, wait, immediate = false) {
            let timeout;
            const debounced = function() {
                const context = this;
                const args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };

            debounced.cancel = function() {
                clearTimeout(timeout);
                timeout = null;
            };

            return debounced;
        },

        // Format large numbers for display
        formatLargeNumber: function(value) {
            if (value === null || value === undefined || !isFinite(value)) return '0';
            const absValue = Math.abs(value);
            const sign = value < 0 ? '-' : '';
            
            if (absValue >= 1e9) {
                return sign + (absValue / 1e9).toFixed(1) + 'B';
            } else if (absValue >= 1e6) {
                return sign + (absValue / 1e6).toFixed(1) + 'M';
            } else if (absValue >= 1e3) {
                return sign + (absValue / 1e3).toFixed(1) + 'K';
            } else {
                return sign + absValue.toFixed(0);
            }
        },

        // Format dollar values consistently
        formatDollarValue: function(value) {
            if (value === null || value === undefined || !isFinite(value)) return '$0';
            return '$' + this.formatLargeNumber(value);
        },

        // Clamp value between min and max
        clamp: function(value, min, max) {
            return Math.max(min, Math.min(max, value));
        },

        // Check if element is visible in viewport
        isElementVisible: function(element) {
            if (!element || !element.getBoundingClientRect) return false;
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
    };

    // Expose to global scope
    if (typeof window !== 'undefined') {
        window.commonUtils = commonUtils;
        
        // For backward compatibility, also expose individual functions
        if (!window.utils) window.utils = {};
        window.utils.throttle = commonUtils.throttle;
        window.utils.debounce = commonUtils.debounce;
        window.utils.formatLargeNumber = commonUtils.formatLargeNumber;
        window.utils.formatDollarValue = commonUtils.formatDollarValue;
    }

    // For Node.js environments
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = commonUtils;
    }
})();