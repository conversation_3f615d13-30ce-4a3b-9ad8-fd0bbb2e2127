/**
 * Error Handling System
 * Centralized error handling, reporting, and recovery
 */

(function() {
  'use strict';

  // Create logger if available
  const logger = window.logger ? window.logger.createLogger('ErrorHandler') : {
    debug: console.debug.bind(console),
    info: console.log.bind(console),
    warn: console.warn.bind(console),
    error: console.error.bind(console)
  };

  /**
   * Error Handler class
   * Provides centralized error handling with recovery strategies
   */
  class ErrorHandler {
    /**
     * Create a new error handler
     */
    constructor() {
      this.errorTypes = new Map();
      this.errorCount = new Map();
      this.lastErrors = new Map();
      this.recoveryStrategies = new Map();
      this.globalHandlers = [];
      this.reportingServices = [];

      // Set up unhandled error listeners
      this.setupGlobalListeners();
    }

    /**
     * Set up global error listeners
     * @private
     */
    setupGlobalListeners() {
      // Handle unhandled promise rejections
      window.addEventListener('unhandledrejection', event => {
        this.handleError(event.reason || new Error('Unhandled Promise rejection'), {
          source: 'promise',
          unhandled: true
        });

        // Prevent default to avoid console error
        event.preventDefault();
      });

      // Handle uncaught exceptions
      window.addEventListener('error', event => {
        this.handleError(event.error || new Error(event.message || 'Unknown error'), {
          source: 'window',
          unhandled: true,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        });

        // Prevent default to avoid console error
        event.preventDefault();
      });
    }

    /**
     * Register an error type with recovery strategy
     * @param {string} errorType - Error type identifier
     * @param {Object} options - Error type options
     * @param {Function} options.test - Function to test if an error matches this type
     * @param {Function} options.recover - Function to recover from this error type
     * @param {number} options.threshold - Number of errors before triggering recovery
     * @param {number} options.timeout - Time window in ms for counting errors
     */
    registerErrorType(errorType, options) {
      this.errorTypes.set(errorType, {
        test: options.test,
        recover: options.recover,
        threshold: options.threshold || 1,
        timeout: options.timeout || 60000, // 1 minute default
        description: options.description || errorType
      });

      logger.info(`Registered error type: ${errorType}`);
    }

    /**
     * Register a global error handler
     * @param {Function} handler - Error handler function
     * @returns {Function} - Function to unregister the handler
     */
    registerGlobalHandler(handler) {
      if (typeof handler !== 'function') {
        throw new Error('Handler must be a function');
      }

      this.globalHandlers.push(handler);

      return () => {
        const index = this.globalHandlers.indexOf(handler);
        if (index !== -1) {
          this.globalHandlers.splice(index, 1);
        }
      };
    }

    /**
     * Register an error reporting service
     * @param {Object} service - Error reporting service
     * @param {Function} service.reportError - Function to report an error
     * @returns {Function} - Function to unregister the service
     */
    registerReportingService(service) {
      if (!service || typeof service.reportError !== 'function') {
        throw new Error('Reporting service must have a reportError method');
      }

      this.reportingServices.push(service);

      return () => {
        const index = this.reportingServices.indexOf(service);
        if (index !== -1) {
          this.reportingServices.splice(index, 1);
        }
      };
    }

    /**
     * Handle an error
     * @param {Error} error - The error object
     * @param {Object} context - Error context
     * @returns {Object} - Result of error handling
     */
    handleError(error, context = {}) {
      // Ensure error is an Error object
      if (!(error instanceof Error)) {
        error = new Error(String(error));
      }

      // Default context
      const fullContext = Object.assign({
        timestamp: Date.now(),
        source: context.source || 'application',
        unhandled: !!context.unhandled
      }, context);

      // Log the error
      logger.error(`Error in ${fullContext.source}:`, error, fullContext);

      // Call global handlers
      for (const handler of this.globalHandlers) {
        try {
          handler(error, fullContext);
        } catch (handlerError) {
          logger.error('Error in error handler:', handlerError);
        }
      }

      // Find matching error type
      let matchedType = null;
      for (const [errorType, config] of this.errorTypes.entries()) {
        if (config.test(error, fullContext)) {
          matchedType = errorType;
          break;
        }
      }

      // Track error occurrence
      if (matchedType) {
        this.trackError(matchedType, error, fullContext);
      }

      // Report error to reporting services
      this.reportError(error, fullContext, matchedType);

      return {
        handled: !!matchedType,
        errorType: matchedType,
        recovered: false // Will be updated if recovery is attempted
      };
    }

    /**
     * Track error occurrence and trigger recovery if needed
     * @param {string} errorType - Error type identifier
     * @param {Error} error - The error object
     * @param {Object} context - Error context
     * @private
     */
    trackError(errorType, error, context) {
      const config = this.errorTypes.get(errorType);
      if (!config) return;

      const now = Date.now();

      // Initialize error count if needed
      if (!this.errorCount.has(errorType)) {
        this.errorCount.set(errorType, {
          count: 0,
          firstError: now,
          lastError: now
        });
      }

      const countData = this.errorCount.get(errorType);

      // Reset count if outside timeout window
      if (now - countData.firstError > config.timeout) {
        countData.count = 0;
        countData.firstError = now;
      }

      // Increment count and update last error time
      countData.count++;
      countData.lastError = now;

      // Store last error
      this.lastErrors.set(errorType, { error, context });

      // Check if threshold is reached
      if (countData.count >= config.threshold) {
        logger.warn(`Error threshold reached for type ${errorType}, attempting recovery`);

        // Reset count to prevent multiple recovery attempts
        countData.count = 0;

        // Attempt recovery
        try {
          config.recover(error, context, this.getErrorHistory(errorType));

          // Emit recovery event
          if (window.eventBus) {
            window.eventBus.emit('error-recovery-attempted', {
              errorType,
              timestamp: now,
              error: error.message,
              context
            });
          }
        } catch (recoveryError) {
          logger.error(`Recovery for ${errorType} failed:`, recoveryError);
        }
      }
    }

    /**
     * Report error to all reporting services
     * @param {Error} error - The error object
     * @param {Object} context - Error context
     * @param {string} errorType - Error type if matched
     * @private
     */
    reportError(error, context, errorType) {
      for (const service of this.reportingServices) {
        try {
          service.reportError(error, Object.assign({}, context, { errorType }));
        } catch (reportingError) {
          logger.error('Error reporting service failed:', reportingError);
        }
      }
    }

    /**
     * Get error history for an error type
     * @param {string} errorType - Error type identifier
     * @returns {Object} - Error history
     */
    getErrorHistory(errorType) {
      const countData = this.errorCount.get(errorType) || { count: 0, firstError: 0, lastError: 0 };
      const lastError = this.lastErrors.get(errorType);

      return {
        count: countData.count,
        firstError: countData.firstError,
        lastError: countData.lastError,
        lastErrorObject: lastError ? lastError.error : null,
        lastErrorContext: lastError ? lastError.context : null
      };
    }

    /**
     * Get all registered error types and their current state
     * @returns {Array} - Error types and their state
     */
    getRegisteredErrorTypes() {
      return Array.from(this.errorTypes.entries()).map(([type, config]) => {
        const history = this.getErrorHistory(type);

        return {
          type,
          description: config.description,
          threshold: config.threshold,
          timeout: config.timeout,
          currentCount: history.count,
          lastErrorTime: history.lastError
        };
      });
    }

    /**
     * Create a wrapped function that catches errors
     * @param {Function} fn - Function to wrap
     * @param {Object} context - Error context
     * @returns {Function} - Wrapped function
     */
    createErrorWrapper(fn, context = {}) {
      return (...args) => {
        try {
          const result = fn(...args);

          // Handle promises
          if (result && typeof result.then === 'function') {
            return result.catch(error => {
              this.handleError(error, context);
              throw error; // Re-throw to preserve Promise rejection
            });
          }

          return result;
        } catch (error) {
          this.handleError(error, context);
          throw error; // Re-throw to preserve normal error flow
        }
      };
    }
  }

  // Create global instance
  window.errorHandler = new ErrorHandler();

  // Register common error types
  window.errorHandler.registerErrorType('websocket-connection-error', {
    description: 'WebSocket Connection Error',
    test: (error, context) => {
      return context.source === 'websocket' && 
             (error.message.includes('connection') || error.message.includes('connect'));
    },
    recover: (error, context) => {
      // Try to reconnect the affected WebSocket
      if (context.manager && typeof context.manager.reconnect === 'function') {
        logger.info(`Attempting to reconnect WebSocket manager: ${context.manager.name || 'unknown'}`);
        context.manager.reconnect();
      } else if (window.connectionManager) {
        logger.info('Attempting to reconnect all WebSockets via connection manager');
        window.connectionManager.reconnectAll();
      }
    },
    threshold: 3, // Need 3 errors to trigger recovery
    timeout: 30000 // Within 30 seconds
  });

  window.errorHandler.registerErrorType('chart-rendering-error', {
    description: 'Chart Rendering Error',
    test: (error, context) => {
      return (context.source === 'chart' || context.component === 'chart') && 
             (error.message.includes('render') || error.message.includes('draw'));
    },
    recover: (error, context) => {
      // Try to reset the affected chart
      if (context.chartId && window.charts) {
        logger.info(`Attempting to reset chart: ${context.chartId}`);
        window.charts.resetChart(context.chartId);
      }
    },
    threshold: 2,
    timeout: 10000
  });

  window.errorHandler.registerErrorType('data-parsing-error', {
    description: 'Data Parsing Error',
    test: (error, context) => {
      return error.message.includes('JSON') || 
             error.message.includes('parse') || 
             error.message.includes('unexpected token');
    },
    recover: () => {
      // No automatic recovery, just log the issue
      logger.info('Data parsing error detected, no automatic recovery available');
    },
    threshold: 5,
    timeout: 60000
  });

  // Wrap setTimeout to catch errors in callbacks
  const originalSetTimeout = window.setTimeout;
  window.setTimeout = function(callback, delay, ...args) {
    if (typeof callback !== 'function') {
      return originalSetTimeout(callback, delay, ...args);
    }

    const wrappedCallback = function() {
      try {
        return callback.apply(this, arguments);
      } catch (error) {
        window.errorHandler.handleError(error, {
          source: 'setTimeout',
          delay
        });
        throw error; // Re-throw to preserve normal behavior
      }
    };

    return originalSetTimeout(wrappedCallback, delay, ...args);
  };

  // Wrap setInterval to catch errors in callbacks
  const originalSetInterval = window.setInterval;
  window.setInterval = function(callback, delay, ...args) {
    if (typeof callback !== 'function') {
      return originalSetInterval(callback, delay, ...args);
    }

    const wrappedCallback = function() {
      try {
        return callback.apply(this, arguments);
      } catch (error) {
        window.errorHandler.handleError(error, {
          source: 'setInterval',
          delay
        });
        // Don't re-throw to keep interval running
      }
    };

    return originalSetInterval(wrappedCallback, delay, ...args);
  };

  // Wrap requestAnimationFrame to catch errors in callbacks
  const originalRequestAnimationFrame = window.requestAnimationFrame;
  window.requestAnimationFrame = function(callback) {
    if (typeof callback !== 'function') {
      return originalRequestAnimationFrame(callback);
    }

    const wrappedCallback = function(timestamp) {
      try {
        return callback(timestamp);
      } catch (error) {
        window.errorHandler.handleError(error, {
          source: 'requestAnimationFrame',
          timestamp
        });
        throw error; // Re-throw to preserve normal behavior
      }
    };

    return originalRequestAnimationFrame(wrappedCallback);
  };

  // Expose wrapped createErrorWrapper globally for convenience
  window.wrapWithErrorHandler = (fn, context = {}) => {
    return window.errorHandler.createErrorWrapper(fn, context);
  };

  logger.info('Error handling system initialized');
})();
