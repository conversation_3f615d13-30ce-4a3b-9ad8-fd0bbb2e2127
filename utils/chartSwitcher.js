/**
 * Advanced Chart Switching System
 * Provides smooth transitions, state caching, and optimized chart switching
 */

(function () {
  "use strict";

  const CONFIG = {
    transitions: {
      fadeOut: 150,
      fadeIn: 250,
      slideOffset: 20,
      scaleStart: 0.95,
    },
    cache: {
      maxStates: 10,
      persistanceTimeout: 300000, // 5 minutes
      preloadDelay: 100,
    },
    performance: {
      debounceDelay: 50,
      animationFrame: true,
      lazyLoad: true,
    },
  };

  class ChartSwitcher {
    constructor() {
      this.currentPair = "BTC";
      this.switchingInProgress = false;
      this.chartCache = new Map();
      this.stateCache = new Map();
      this.preloadQueue = new Set();
      this.transitionElement = null;
      this.isInitialized = false;
      this.lastFullRefresh = 0; // Track last full refresh timestamp
      this.init();
    }

    init() {
      this.createTransitionLayer();
      this.bindEventListeners();
      this.setupPreloadSystem();
      this.isInitialized = true;
    }

    createTransitionLayer() {
      this.transitionElement = document.createElement("div");
      this.transitionElement.id = "chart-transition-layer";
      this.transitionElement.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, rgba(15, 20, 26, 0.95), rgba(10, 15, 20, 0.98));
                backdrop-filter: blur(2px);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                opacity: 0;
                transition: opacity ${CONFIG.transitions.fadeIn}ms ease-out;
            `;

      const spinner = document.createElement("div");
      spinner.style.cssText = `
                width: 40px;
                height: 40px;
                border: 3px solid rgba(38, 166, 154, 0.3);
                border-top: 3px solid #26a69a;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            `;

      const container = document.createElement("div");
      container.style.cssText = `
                display: flex;
                flex-direction: column;
                align-items: center;
                transform: scale(${CONFIG.transitions.scaleStart});
                transition: transform ${CONFIG.transitions.fadeIn}ms ease-out;
            `;

      container.appendChild(spinner);
      this.transitionElement.appendChild(container);

      // Add CSS animation
      if (!document.getElementById("chart-switcher-styles")) {
        const style = document.createElement("style");
        style.id = "chart-switcher-styles";
        style.textContent = `
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    .chart-switching {
                        pointer-events: none;
                        user-select: none;
                    }
                    .chart-fade-out {
                        opacity: 0;
                        transform: translateY(${CONFIG.transitions.slideOffset}px);
                        transition: opacity ${CONFIG.transitions.fadeOut}ms ease-in,
                                    transform ${CONFIG.transitions.fadeOut}ms ease-in;
                    }
                    .chart-fade-in {
                        opacity: 1;
                        transform: translateY(0);
                        transition: opacity ${CONFIG.transitions.fadeIn}ms ease-out,
                                    transform ${CONFIG.transitions.fadeIn}ms ease-out;
                    }
                `;
        document.head.appendChild(style);
      }
    }

    bindEventListeners() {
      // Debounced switch function
      this.debouncedSwitch = this.debounce(
        this.performSwitch.bind(this),
        CONFIG.performance.debounceDelay,
      );

      // Preload on hover with browser compatibility
      document.addEventListener(
        "mouseenter",
        (event) => {
          const target = event.target;
          const button = this.findClosest(target, ".pair-button");
          if (
            button &&
            button.dataset.pair &&
            button.dataset.pair !== this.currentPair
          ) {
            this.preloadPair(button.dataset.pair);
          }
        },
        true,
      );

      // Handle visibility changes
      document.addEventListener("visibilitychange", () => {
        if (!document.hidden) {
          this.forceFullRefresh();
        }
      });
    }

    setupPreloadSystem() {
      // Preload adjacent pairs
      const pairs = ["BTC", "ETH", "LTC", "SOL"];
      const currentIndex = pairs.indexOf(this.currentPair);

      if (currentIndex !== -1) {
        const nextPair = pairs[(currentIndex + 1) % pairs.length];
        const prevPair =
          pairs[(currentIndex - 1 + pairs.length) % pairs.length];

        setTimeout(() => {
          this.preloadPair(nextPair);
          this.preloadPair(prevPair);
        }, CONFIG.cache.preloadDelay);
      }
    }

    async preloadPair(pair) {
      if (this.preloadQueue.has(pair) || this.chartCache.has(pair)) {
        return;
      }

      this.preloadQueue.add(pair);

      try {
        // Preload chart data
        const chartState = await this.createChartState(pair);
        this.chartCache.set(pair, chartState);

        // Cache additional resources
        if (window.apiClient) {
          window.apiClient.preloadData(pair).catch(console.warn);
        }
      } catch (error) {
        console.warn(`Preload failed for ${pair}:`, error);
      } finally {
        this.preloadQueue.delete(pair);
      }
    }

    async createChartState(pair) {
      return {
        pair: pair,
        initialized: false,
        data: null,
        indicators: new Map(),
        profiles: new Map(),
        lastUpdate: Date.now(),
        settings: this.getDefaultSettings(pair),
      };
    }

    getDefaultSettings(pair) {
      return {
        timeframe: "1h",
        indicators: ["volume", "rsi"],
        overlays: ["bollinger"],
        theme: "dark",
        autoScale: true,
      };
    }

    async switchToPair(newPair) {
      if (this.switchingInProgress || newPair === this.currentPair) {
        return false;
      }

      this.switchingInProgress = true;

      try {
        await this.debouncedSwitch(newPair);
        return true;
      } catch (error) {
        console.error("Chart switch failed:", error);
        return false;
      } finally {
        this.switchingInProgress = false;
      }
    }

    async performSwitch(newPair) {
      const chartContainer = document.querySelector(".chart-container");
      const priceChartContainer = document.querySelector(
        ".price-chart-container",
      );

      if (!chartContainer || !priceChartContainer) {
        throw new Error("Chart containers not found");
      }

      // Step 1: Show transition overlay
      this.showTransitionOverlay(priceChartContainer);

      // Step 2: Fade out current chart
      await this.fadeOutChart(priceChartContainer);

      // Step 3: Save current state
      this.saveCurrentState();

      // Step 4: Update UI state
      this.updateButtonStates(newPair);
      this.updateContainerState(chartContainer, newPair);

      // Step 5: Load/restore new chart
      await this.loadNewChart(newPair, priceChartContainer);

      // Step 6: Fade in new chart
      await this.fadeInChart(priceChartContainer);

      // Step 7: Hide transition overlay
      this.hideTransitionOverlay();

      // Step 8: Update current pair
      this.currentPair = newPair;
      window.currentPair = newPair;
      window.currentActivePair = newPair;

      // Step 9: Notify components
      this.notifyChartSwitch(newPair);

      // Step 10: Setup preloading for next potential switches
      this.setupPreloadSystem();
    }

    showTransitionOverlay(container) {
      if (!container.contains(this.transitionElement)) {
        container.appendChild(this.transitionElement);
      }

      this.transitionElement.style.display = "flex";

      requestAnimationFrame(() => {
        this.transitionElement.style.opacity = "1";
        this.transitionElement.firstElementChild.style.transform = "scale(1)";
      });
    }

    hideTransitionOverlay() {
      if (!this.transitionElement) return;

      this.transitionElement.style.opacity = "0";
      this.transitionElement.firstElementChild.style.transform = `scale(${CONFIG.transitions.scaleStart})`;

      setTimeout(() => {
        this.transitionElement.style.display = "none";
      }, CONFIG.transitions.fadeOut);
    }

    async fadeOutChart(container) {
      const chartElement = container.querySelector(".price-chart");
      if (chartElement) {
        chartElement.classList.add("chart-fade-out");
        chartElement.classList.add("chart-switching");

        await new Promise((resolve) =>
          setTimeout(resolve, CONFIG.transitions.fadeOut),
        );
      }
    }

    async fadeInChart(container) {
      const chartElement = container.querySelector(".price-chart");
      if (chartElement) {
        chartElement.classList.remove("chart-fade-out", "chart-switching");
        chartElement.classList.add("chart-fade-in");

        await new Promise((resolve) =>
          setTimeout(resolve, CONFIG.transitions.fadeIn),
        );

        chartElement.classList.remove("chart-fade-in");
      }
    }

    saveCurrentState() {
      if (!window.chartStates || !this.currentPair) return;

      const currentState = window.chartStates.get(this.currentPair);
      if (currentState) {
        this.stateCache.set(this.currentPair, {
          ...currentState,
          timestamp: Date.now(),
        });
      }

      // Cleanup old states
      const now = Date.now();
      for (const [pair, state] of this.stateCache.entries()) {
        if (now - state.timestamp > CONFIG.cache.persistanceTimeout) {
          this.stateCache.delete(pair);
        }
      }
    }

    updateButtonStates(newPair) {
      const buttons = document.querySelectorAll(".pair-button");
      buttons.forEach((button) => {
        const isActive = button.dataset.pair === newPair;
        button.classList.toggle("active", isActive);

        if (isActive) {
          button.style.transform = "scale(0.95)";
          setTimeout(() => {
            button.style.transform = "";
          }, 150);
        }
      });
    }

    updateContainerState(container, newPair) {
      container.dataset.pair = newPair;

      // Update loading overlay text
      const loadingOverlay = container.querySelector(".loading-overlay");
      if (loadingOverlay) {
        loadingOverlay.textContent = `Loading ${newPair} data...`;
      }
    }

    /**
     * Cleanup chart: remove DOM, clear state, disconnect feeds for a specific pair
     * @param {string} pair - The pair to clean up (defaults to currentPair)
     */
    cleanupCurrentChart(pair = this.currentPair) {
      const priceChartContainer = document.querySelector(
        ".price-chart-container",
      );
      if (priceChartContainer) {
        const chartElement = priceChartContainer.querySelector(".price-chart");
        if (chartElement) {
          chartElement.innerHTML = "";
        }
      }

      const deltaOiProfileCanvas = document.getElementById(
        "delta-oi-profile-canvas",
      );
      if (deltaOiProfileCanvas) {
        deltaOiProfileCanvas.remove();
      }

      if (window.chartStates) {
        const state = window.chartStates.get(pair);
        if (state) {
          if (state.deltaOiProfile && state.deltaOiProfile.cleanup) {
            state.deltaOiProfile.cleanup();
          }
          window.chartStates.delete(pair);
        }
      }

      this.chartCache.delete(pair);
      this.stateCache.delete(pair);

      if (
        window.bybitWsManager &&
        window.bybitWsManager.unsubscribeAllForPair
      ) {
        window.bybitWsManager.unsubscribeAllForPair(pair);
      }
      if (window.connectionManager && window.connectionManager.clearPairState) {
        window.connectionManager.clearPairState(pair);
      }
    }

    async loadNewChart(newPair, container) {
      this.showTransitionOverlay(container);

      if (this.currentPair && this.currentPair !== newPair) {
        this.cleanupCurrentChart(this.currentPair);
      }

      if (window.bybitWsManager && window.bybitWsManager.updateSubscriptions) {
        window.bybitWsManager.updateSubscriptions(newPair);
      }
      if (
        window.connectionManager &&
        window.connectionManager.triggerResubscription
      ) {
        window.connectionManager.triggerResubscription();
      }

      try {
        let chartState = this.chartCache.get(newPair);
        if (!chartState) {
          chartState = await this.createChartState(newPair);
          this.chartCache.set(newPair, chartState);
        }

        if (!window.chartStates) {
          window.chartStates = new Map();
        }

        const cachedState = this.stateCache.get(newPair);
        if (cachedState) {
          window.chartStates.set(newPair, cachedState);
        } else {
          window.chartStates.set(newPair, chartState);
        }

        if (window.switchPairInternal) {
          await window.switchPairInternal(newPair);
        } else if (window.charts && window.charts.switchTo) {
          await window.charts.switchTo(newPair);
        }

        await this.loadIndicatorsForPair(newPair);
        this.initializeDeltaOIProfile(newPair);
      } catch (error) {
        console.error(`Failed to load chart for ${newPair}:`, error);
        throw error;
      } finally {
        this.hideTransitionOverlay();
        this.switchingInProgress = false;
      }
    }

    async loadIndicatorsForPair(pair) {
      if (!window.indicatorManager) return;

      try {
        const indicators = ["cvd", "perpCvd"];
        const promises = indicators.map((indicator) =>
          window.indicatorManager.loadForPair?.(pair, indicator),
        );

        await Promise.allSettled(promises);
      } catch (error) {
        console.warn(`Indicator loading failed for ${pair}:`, error);
      }
    }

    initializeDeltaOIProfile(pair) {
      const state = window.chartStates.get(pair);
      if (state && window.deltaOiProfileManager) {
        window.deltaOiProfileManager.initializeProfiles(state);
      }
    }

    async loadProfilesForPair(pair) {
      if (!window.deltaOiProfileManager) return;

      try {
        // Check if we need to initialize profiles or just update existing ones
        const state = window.chartStates.get(pair);
        if (state) {
          // Handle chart switch (cleanup existing and initialize new)
          if (window.deltaOiProfileManager.handleChartSwitch) {
            window.deltaOiProfileManager.handleChartSwitch(state);
          } else if (window.deltaOiProfileManager.switchToPair) {
            // Fallback to old method if available
            await window.deltaOiProfileManager.switchToPair(pair);
          }
        }
      } catch (e) {
        console.warn(
          "ChartSwitcher: Failed to load profiles for pair",
          pair,
          e,
        );
      }
    }

    notifyChartSwitch(newPair) {
      // Dispatch custom event
      document.dispatchEvent(
        new CustomEvent("chartSwitched", {
          detail: {
            newPair,
            previousPair: this.currentPair,
            timestamp: Date.now(),
          },
        }),
      );

      // Update Bybit WebSocket subscriptions
      if (window.bybitWsManager) {
        window.bybitWsManager.updateSubscriptions?.(newPair);
      }

      // Update console capture
      if (window.consoleCapture) {
        window.consoleCapture.switchToPair?.(newPair);
      }

      // Note: Orderbook modules now have their own individual WebSocket connections
      // and don't need to be updated here - they handle pair changes independently
    }

    refreshCurrentChart() {
      if (!this.currentPair || this.switchingInProgress) return;
      // --- CLEANUP before refresh ---
      this.showTransitionOverlay(
        document.querySelector(".price-chart-container"),
      );
      // Only clean up the current pair
      this.cleanupCurrentChart(this.currentPair);
      // Optionally, trigger fast reconnection
      if (window.bybitWsManager && window.bybitWsManager.updateSubscriptions) {
        window.bybitWsManager.updateSubscriptions(this.currentPair);
      }
      if (
        window.connectionManager &&
        window.connectionManager.triggerResubscription
      ) {
        window.connectionManager.triggerResubscription();
      }
      const chartState = window.chartStates?.get(this.currentPair);
      if (chartState && Date.now() - chartState.lastUpdate > 30000) {
        this.loadNewChart(
          this.currentPair,
          document.querySelector(".price-chart-container"),
        ).catch(console.warn);
      }
    }

    /**
     * Force a full cleanup and reload of the current chart (for wake/visibility restore)
     * Debounced: will not run more than once every 10 seconds
     */
    async forceFullRefresh() {
      const container = document.querySelector(".price-chart-container");
      if (!container) return;

      // For tab visibility restoration, always use the last opened chart
      const lastChartPair =
        localStorage.getItem("lastChartPair") || this.currentPair || "BTC";

      this.showTransitionOverlay(container);
      if (this.currentPair) {
        this.cleanupCurrentChart(this.currentPair);
      }
      try {
        await this.loadNewChart(lastChartPair, container);
        this.currentPair = lastChartPair; // Update currentPair to match loaded pair
      } catch (e) {
        console.error("forceFullRefresh failed:", e);
      } finally {
        this.hideTransitionOverlay();
      }
    }

    findClosest(element, selector) {
      // Browser-compatible closest() implementation
      if (element.closest) {
        return element.closest(selector);
      }

      // Fallback for older browsers
      let current = element;
      while (current && current !== document) {
        if (this.matches(current, selector)) {
          return current;
        }
        current = current.parentElement;
      }
      return null;
    }

    matches(element, selector) {
      if (element.matches) {
        return element.matches(selector);
      }
      if (element.matchesSelector) {
        return element.matchesSelector(selector);
      }
      if (element.webkitMatchesSelector) {
        return element.webkitMatchesSelector(selector);
      }
      if (element.mozMatchesSelector) {
        return element.mozMatchesSelector(selector);
      }
      if (element.msMatchesSelector) {
        return element.msMatchesSelector(selector);
      }

      // Ultimate fallback
      const matches = (
        element.document || element.ownerDocument
      ).querySelectorAll(selector);
      let i = matches.length;
      while (--i >= 0 && matches.item(i) !== element) {}
      return i > -1;
    }

    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // Public API methods
    getCurrentPair() {
      return this.currentPair;
    }

    isReady() {
      return this.isInitialized && !this.switchingInProgress;
    }

    preloadAll() {
      const pairs = ["BTC", "ETH", "LTC", "SOL"];
      pairs.forEach((pair) => {
        if (pair !== this.currentPair) {
          this.preloadPair(pair);
        }
      });
    }

    clearCache() {
      this.chartCache.clear();
      this.stateCache.clear();
      this.preloadQueue.clear();
    }

    getStats() {
      return {
        currentPair: this.currentPair,
        cachedCharts: this.chartCache.size,
        cachedStates: this.stateCache.size,
        preloadQueue: this.preloadQueue.size,
        switchingInProgress: this.switchingInProgress,
      };
    }
  }

  // Initialize and expose chart switcher
  window.ChartSwitcher = ChartSwitcher;

  // Auto-initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      window.chartSwitcher = new ChartSwitcher();
    });
  } else {
    window.chartSwitcher = new ChartSwitcher();
  }
})();
