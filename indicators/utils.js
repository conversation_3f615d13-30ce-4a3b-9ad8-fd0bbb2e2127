// utils.js
window.utils = {
  normalizeValue: function (value, min, max) {
    if (!isFinite(value) || !isFinite(min) || !isFinite(max)) return 0;
    if (min === max) return value > 0 ? 0.5 : value < 0 ? -0.5 : 0;
    const normalized = (2 * (value - min)) / (max - min) - 1;
    return Math.max(Math.min(normalized, 1), -1);
  },
  getIndicatorColor: function (normalizedValue) {
    const colors = window.CONFIG?.indicators?.defaultColors || {
      positive: "rgba(255, 0, 0, 0.4)",
      negative: "rgba(0, 255, 255, 0.4)",
      neutral: "rgba(170, 170, 170, 0.8)",
    };
    const thresholds = window.CONFIG?.indicators?.defaultThresholds || {
      positive: 0.5,
      negative: -0.5,
    };

    if (normalizedValue > thresholds.positive) return colors.positive;
    if (normalizedValue < thresholds.negative) return colors.negative;
    return colors.neutral;
  },
  initStdDevCache: function () {
    return { count: 0, mean: 0, m2: 0 };
  },
  updateStdDev: function (c, v) {
    c.count++;
    const d = v - c.mean;
    c.mean += d / c.count;
    const d2 = v - c.mean;
    c.m2 += d * d2;
    return c.count > 1 ? Math.sqrt(c.m2 / (c.count - 1)) : 0;
  },


  /**
   * Filters an array of bars to ensure all required fields are finite numbers.
   * Each bar must have time, open, high, low, close, and volume.
   * Returns only valid bars.
   */
  filterValidBars: function (bars) {
    if (!Array.isArray(bars)) return [];
    return bars.filter(
      (b) =>
        b &&
        Number.isFinite(b.time) &&
        Number.isFinite(b.open) &&
        Number.isFinite(b.high) &&
        Number.isFinite(b.low) &&
        Number.isFinite(b.close) &&
        Number.isFinite(b.volume),
    );
  },
};


