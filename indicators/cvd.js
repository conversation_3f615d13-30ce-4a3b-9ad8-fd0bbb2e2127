/**
 * CVD Indicator Module
 * Now updates via API polling - WebSocket recovery logic removed
 */

(function () {
  // Initialize logger for this module
  const logger = window.logger?.createLogger('CVD') || {
    debug: () => {},
    info: () => {},
    warn: console.warn.bind(console),
    error: console.error.bind(console)
  };

  // --- Tab visibility tracking and buffered chart update logic ---
  let isTabVisible = !document.hidden;
  let pendingCVDUpdateArgs = null;
  
  // Use unified visibility system from ConnectionManager
  window.addEventListener("visibility-restore", () => {
    isTabVisible = true;
    if (typeof window.triggerCVDRedraw === "function") {
      window.triggerCVDRedraw();
    }
  });
  
  window.addEventListener("visibility-hide", () => {
    isTabVisible = false;
  });

  // Listen for chart switching to clear pending updates
  document.addEventListener("pairChanged", (event) => {
    const newPair = event.detail?.pair;
    if (newPair && window.PS?.pendingCVDUpdates) {

      window.PS.pendingCVDUpdates.lastBarTime = 0;
      window.PS.pendingCVDUpdates.lastCvdValue = 0;
      window.PS.pendingCVDUpdates.pendingValue = 0;
      window.PS.pendingCVDUpdates.hasUpdate = false;
      window.PS.pendingCVDUpdates.lastRealTimeUpdate = 0;
      window.PS.pendingCVDUpdates.lastTitleColor = null;
      window.PS.pendingCVDUpdates.lastClearTime = Date.now(); // Track when updates were cleared
    }
  });

  // CRITICAL FIX: Add proper chart switching cleanup
  const originalSwitchPair = window.switchPairInternal;
  if (originalSwitchPair) {
    window.switchPairInternal = function(newPair) {
      // Clear CVD data before switching
      if (window.PS?.setCVDPriceData) {

        window.PS.setCVDPriceData([]);
      }
      
      // Clear pending updates
      if (window.PS?.pendingCVDUpdates) {
        window.PS.pendingCVDUpdates.lastBarTime = 0;
        window.PS.pendingCVDUpdates.lastCvdValue = 0;
        window.PS.pendingCVDUpdates.pendingValue = 0;
        window.PS.pendingCVDUpdates.hasUpdate = false;
        window.PS.pendingCVDUpdates.lastRealTimeUpdate = 0;
        window.PS.pendingCVDUpdates.lastTitleColor = null;
        window.PS.pendingCVDUpdates.lastClearTime = Date.now();
      }
      
      // Call original function
      return originalSwitchPair.call(this, newPair);
    };
  }

  window.triggerCVDRedraw = function () {
    if (pendingCVDUpdateArgs) {
      updateCVD(...pendingCVDUpdateArgs);
      pendingCVDUpdateArgs = null;
    }
  };
  function updateCVDBuffered(...args) {
    if (isTabVisible) {
      updateCVD(...args);
      pendingCVDUpdateArgs = null;
    } else {
      pendingCVDUpdateArgs = args;
    }
  }
  // --------------------------------------------------------------

  if (!window.utils) {
    setTimeout(() => {}, 500);
    return;
  }
  const { getIndicatorColor } = window.utils;
  const formatLargeNumber = window.commonUtils.formatLargeNumber;
  const { ema, stdev, clamp, lerp, weightedAverage } = window.mathUtils;

  // Configuration - use centralized config with fallback
  const CVD_CONFIG = window.PS?.CVD_CONFIG ||
    window.CONFIG?.cvd || {
      volumeMAPeriod: 90,
      volumeAdjustment: {
        enabled: true,
        buyMultiplier: 1.0,
        sellMultiplier: 1.0,
        useWicks: true,
        useBodySize: true,
        useCloseRelative: true,
      },
      renderOnCandleCloseOnly: true,
      lookbackPeriod: (() => {
        const savedWindow = localStorage.getItem("normalizationWindow");
        return savedWindow ? parseInt(savedWindow) : 1440;
      })(),
      normalize: true,
      smoothing: true,
      sensitivityMultiplier: 1.2,
      normalizationBuffer: 0,
      minSmoothingPeriod: 5,
      maxSmoothingPeriod: 20,
      adaptiveSmoothingFactor: 0.5,
      volumeWeighting: {
        enabled: true,
        weightFactor: 0.5,
      },
    };

  window.PS = window.PS || {};
  window.PS.CVD_CONFIG = CVD_CONFIG;

  let historicalCVDData = [];
  let cvdNormalizationWindow = [];

  function createCVDChart(container, priceChart) {
    let cvdPane;
    try {
      const panes = priceChart.panes();
      if (panes && panes.length > 1) {
        cvdPane = panes[1];
        cvdPane.applyOptions({ visible: true });
        if (typeof cvdPane.setHeight === "function") {
          cvdPane.setHeight(150);
        }
        cvdPane.applyOptions({
          rightPriceScale: {
            visible: true,
            borderColor: "#2A2A2A",
            scaleMargins: { top: 0.1, bottom: 0.1 },
            formatter: {
              format: (price) => formatLargeNumber(price),
            },
          },
        });
      } else if (window.DEBUG_MODE) {

      }
    } catch (e) {
      if (window.DEBUG_MODE) logger.warn("Pane access error:", e);
    }

    const cvdSeries = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: {
          type: "volume",
          formatter: (price) => formatLargeNumber(price),
        },
        lineWidth: 1.5,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "SPOT CVD",
        titleColor: "rgba(170, 170, 170, 0.8)", // Default neutral color
        pointsVisible: false,
        lastPriceAnimation: 0,
        // Remove restrictive autoscale that might cause +1 jumps
        // autoscaleInfoProvider: () => ({
        //   priceRange: { minValue: -1.05, maxValue: 1.05 },
        //   margins: { above: 5, below: 5 },
        // }),
        crosshairMarkerVisible: false,
      },
      1,
    );

    const cvdMASeries = {
      update: () => {},
      setData: () => {},
      applyOptions: () => {},
      _internal_isDisposed: false,
    };

    const zeroLine = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "#444444",
        lineWidth: 1,
        lineStyle: 2, // match dashed style of other reference lines
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    // Add dashed reference lines at y=1 and y=-1
    const level1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.cvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const levelMinus1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.cvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const referenceLines = {};
    referenceLines.level1 = level1Line;
    referenceLines.levelMinus1 = levelMinus1Line;

    priceChart.applyOptions({
      layout: {
        background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        panes: {
          separatorColor: "#2A2A2A",
          separatorHoverColor: "rgba(255, 0, 0, 0.1)",
          enableResize: true,
        },
      },
    });

    try {
      const chartContainer = container.querySelector(".tv-lightweight-charts");
      if (chartContainer) {
        chartContainer.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
      }
    } catch (e) {
      logger.warn("Error styling chart container:", e);
    }

    return {
      chart: priceChart,
      pane: cvdPane,
      series: cvdSeries,
      zeroLine: zeroLine,
      referenceLines: referenceLines,
    };
  }

  // Shared color logic: red above 0.5, gray between, aqua below -0.5
  function getNormalizedColor(normalizedValue) {
    if (normalizedValue > 0.5) {
      return "rgba(255, 0, 0, 0.8)"; // red
    } else if (normalizedValue < -0.5) {
      return "rgba(0, 255, 255, 0.8)"; // aqua
    } else {
      return "rgba(170, 170, 170, 0.8)"; // gray
    }
  }

  // Spot CVD: Calculate adjusted volume for a bar (Bitstamp/spot logic)
  function calculateAdjustedVolume(bar, prevBar) {
    if (!bar || typeof bar !== "object") return 0;

    const volume =
      typeof bar.volume === "number" && isFinite(bar.volume) ? bar.volume : 0;
    if (volume === 0) return 0;

    // Validate required price fields
    if (
      !isFinite(bar.open) ||
      !isFinite(bar.close) ||
      !isFinite(bar.high) ||
      !isFinite(bar.low)
    ) {
      return 0;
    }
    // For spot, use close - open as delta direction
    let isBuyBar = true;
    if (
      window.PS &&
      window.PS.CVD_CONFIG &&
      window.PS.CVD_CONFIG.volumeAdjustment &&
      window.PS.CVD_CONFIG.volumeAdjustment.useCloseRelative &&
      prevBar &&
      prevBar.close !== undefined &&
      !isNaN(prevBar.close)
    ) {
      isBuyBar = bar.close >= prevBar.close;
    } else {
      isBuyBar = bar.close >= bar.open;
    }
    let adjustmentFactor = 1.0;
    if (
      window.PS &&
      window.PS.CVD_CONFIG &&
      window.PS.CVD_CONFIG.volumeAdjustment &&
      window.PS.CVD_CONFIG.volumeAdjustment.useBodySize
    ) {
      const bodySize = Math.abs(bar.close - bar.open);
      const range = bar.high - bar.low;
      if (range > 0 && isFinite(bodySize) && isFinite(range)) {
        const bodySizePercent = bodySize / range;
        adjustmentFactor *= 0.7 + bodySizePercent * 0.6;
      }
    }
    if (
      window.PS &&
      window.PS.CVD_CONFIG &&
      window.PS.CVD_CONFIG.volumeAdjustment &&
      window.PS.CVD_CONFIG.volumeAdjustment.useWicks
    ) {
      const totalRange = bar.high - bar.low;
      if (totalRange > 0 && isFinite(totalRange)) {
        const upperWick = bar.high - Math.max(bar.open, bar.close);
        const lowerWick = Math.min(bar.open, bar.close) - bar.low;
        if (isFinite(upperWick) && isFinite(lowerWick)) {
          if (isBuyBar) {
            const lowerWickPercent = lowerWick / totalRange;
            adjustmentFactor *= 1 + lowerWickPercent * 0.8;
          } else {
            const upperWickPercent = upperWick / totalRange;
            adjustmentFactor *= 1 + upperWickPercent * 0.8;
          }
        }
      }
    }
    adjustmentFactor = Math.max(0.5, Math.min(2.0, adjustmentFactor));
    // For spot, use buyMultiplier/sellMultiplier if present, else default to 1
    const buyMultiplier =
      (window.PS &&
        window.PS.CVD_CONFIG &&
        window.PS.CVD_CONFIG.volumeAdjustment &&
        window.PS.CVD_CONFIG.volumeAdjustment.buyMultiplier) ||
      1;
    const sellMultiplier =
      (window.PS &&
        window.PS.CVD_CONFIG &&
        window.PS.CVD_CONFIG.volumeAdjustment &&
        window.PS.CVD_CONFIG.volumeAdjustment.sellMultiplier) ||
      1;
    return isBuyBar
      ? volume * adjustmentFactor * buyMultiplier
      : -volume * adjustmentFactor * sellMultiplier;
  }

  // Spot CVD: Calculate CVD data array from price bars
  const cvdDataCache = new WeakMap();
  function calculateCVDData(priceData) {
    if (cvdDataCache.has(priceData)) return cvdDataCache.get(priceData);

    if (!priceData || priceData.length === 0) {
      logger.warn("CVD: No price data provided for calculation");
      return [];
    }

    const cvdData = [];
    let cumulativeDelta = 0;
    let skippedBars = 0;

    for (let i = 0; i < priceData.length; i++) {
      const bar = priceData[i];
      const prevBar = i > 0 ? priceData[i - 1] : null;

      // Enhanced validation
      if (!bar || !bar.time || bar.volume === undefined || isNaN(bar.volume)) {
        skippedBars++;
        continue;
      }

      const barDelta = calculateAdjustedVolume(bar, prevBar);

      // Validate calculated delta
      if (isNaN(barDelta)) {
        logger.warn(
          `CVD: Invalid delta calculated for bar at time ${bar.time}`,
        );
        skippedBars++;
        continue;
      }

      cumulativeDelta += barDelta;
      cvdData.push({ time: bar.time, value: cumulativeDelta });
    }

    if (skippedBars > 0) {
      logger.warn(
        `CVD: Skipped ${skippedBars} invalid bars during calculation`,
      );
    }

    cvdDataCache.set(priceData, cvdData);
    return cvdData;
  }

  // In initializeCVDData or equivalent, standardize to only use closed bars
  function initializeCVDData(cvdComponents, priceData) {
    if (!priceData || priceData.length === 0) {
      console.error("CVD: Cannot initialize with empty price data");
      return { cvdData: [], cvdMAData: [] };
    }

    // Validate price data integrity
    const validBars = priceData.filter(
      (bar) =>
        bar &&
        typeof bar.time === "number" &&
        isFinite(bar.time) &&
        typeof bar.volume === "number" &&
        isFinite(bar.volume) &&
        typeof bar.open === "number" &&
        isFinite(bar.open) &&
        typeof bar.close === "number" &&
        isFinite(bar.close),
    );

    if (validBars.length < priceData.length * 0.8) {
      logger.warn(
        `CVD: High invalid data ratio. Valid: ${validBars.length}/${priceData.length}`,
      );
    }

    // Ensure we only use closed bars - check current bar status
    const now = Math.floor(Date.now() / 1000);
    const barInterval = 300;
    const currentBarTime = Math.floor(now / barInterval) * barInterval;
    const lastBarTime = validBars[validBars.length - 1]?.time;

    // Only use bars that are definitely closed
    const closedBars = validBars.filter((bar) => bar.time < currentBarTime);
    if (closedBars.length === 0) {
      console.error("CVD: No valid closed bars available");
      return { cvdData: [], cvdMAData: [] };
    }

    const cvdData = calculateCVDData(closedBars);
    if (cvdData.length === 0) {
      console.error("CVD: No valid CVD data calculated");
      return { cvdData: [], cvdMAData: [] };
    }

    // Store raw CVD values for rolling normalization
    const values = cvdData.map((d) => d.value).filter((v) => isFinite(v));
    if (values.length === 0) {
      console.error("CVD: No finite values for normalization");
      return { cvdData: [], cvdMAData: [] };
    }

    cvdNormalizationWindow = values.slice(-1440);

    // Use mathUtils.rollingNormalize for efficient rolling normalization
    const normalizedValues =
      window.mathUtils &&
      typeof window.mathUtils.rollingNormalize === "function"
        ? window.mathUtils.rollingNormalize(values, 1440)
        : values.map(() => 0); // fallback to 0 if not available

    const normalizedCVDData = cvdData.map((point, i) => {
      const normalizedValue = normalizedValues[i];
      const color = getNormalizedColor(normalizedValue);
      return {
        time: point.time,
        value: isFinite(normalizedValue) ? normalizedValue : 0,
        color: color,
      };
    });

    // Store last historical bar time for websocket continuation
    const lastHistoricalBar = normalizedCVDData[normalizedCVDData.length - 1];

    const emptyMAData = cvdData.map((point) => ({
      time: point.time,
      value: 0,
    }));
    const zeroLineData = [];
    if (priceData.length > 0) {
      const firstTime = priceData[0].time;
      const lastTime = priceData[priceData.length - 1].time;
      zeroLineData.push({ time: firstTime, value: 0 });
      zeroLineData.push({ time: lastTime, value: 0 });
      const lastDataPoint = normalizedCVDData[normalizedCVDData.length - 1];
      if (lastDataPoint) {
        if (!window.PS) window.PS = {};
        if (!window.PS.pendingCVDUpdates) {
          window.PS.pendingCVDUpdates = {};
        }
        const pendingCVDUpdates = window.PS.pendingCVDUpdates;
        pendingCVDUpdates.lastBarTime = lastDataPoint.time;
        pendingCVDUpdates.pendingValue = lastDataPoint.value;
        pendingCVDUpdates.pendingEmaValue = 0;
        pendingCVDUpdates.lastCvdValue = lastDataPoint.value; // This is already normalized
        pendingCVDUpdates.hasUpdate = false;
      }
    } else {
      const now = Math.floor(Date.now() / 1000);
      zeroLineData.push({ time: now - 86400, value: 0 });
      zeroLineData.push({ time: now, value: 0 });
    }
    // Direct chart updates without RAF
    try {
      if (cvdComponents?.series && !cvdComponents.series._internal_isDisposed) {
        cvdComponents.series.setData(normalizedCVDData);
        
        // Set initial title color based on the last data point
        if (normalizedCVDData.length > 0) {
          const lastPoint = normalizedCVDData[normalizedCVDData.length - 1];
          cvdComponents.series.applyOptions({
            titleColor: lastPoint.color
          });
        }
      }
      if (
        cvdComponents?.zeroLine &&
        !cvdComponents.zeroLine._internal_isDisposed
      ) {
        cvdComponents.zeroLine.setData(zeroLineData);
      }
      if (
        cvdComponents?.referenceLines?.level1 &&
        !cvdComponents.referenceLines.level1._internal_isDisposed
      ) {
        cvdComponents.referenceLines.level1.setData(
          zeroLineData.map((d) => ({ ...d, value: 1 })),
        );
      }
      if (
        cvdComponents?.referenceLines?.levelMinus1 &&
        !cvdComponents.referenceLines.levelMinus1._internal_isDisposed
      ) {
        cvdComponents.referenceLines.levelMinus1.setData(
          zeroLineData.map((d) => ({ ...d, value: -1 })),
        );
      }
    } catch (e) {
      logger.warn("CVD: Error during chart update:", e);
    }

    // Mark historical data as loaded and ready for websocket continuation
    if (!window.PS) window.PS = {};
    window.PS.cvdHistoricalLoaded = true;

    return { cvdData: normalizedCVDData, cvdMAData: emptyMAData };
  }

  // Use window.IndicatorChartUtils for browser compatibility

  function synchronizeCharts(cvdComponents, priceChart) {
    // Use shared utility for zero/reference lines
    const syncHandle = window.IndicatorChartUtils
      ? window.IndicatorChartUtils.synchronizeCharts(
          cvdComponents,
          priceChart,
          {
            referenceLevels: {
              // Add reference levels if needed for coloredZeroLine, etc.
              // Example: level1: 1, levelMinus1: -1
            },
          },
        )
      : null;

    // Custom color update logic (specific to CVD)
    const updateIndicatorColor = () => {
      // Color update logic can be implemented here if needed
    };

    const colorUpdateInterval = setInterval(updateIndicatorColor, 1000);

    return {
      unsubscribe: () => {
        try {
          syncHandle.unsubscribe();
          clearInterval(colorUpdateInterval);
        } catch (e) {}
      },
      updateIndicatorColor: updateIndicatorColor,
    };
  }

  function normalizeCVDWithComponents(value, cvdComponents) {
    try {
      const lookbackData =
        cvdComponents.series.data && cvdComponents.series.data().length > 0
          ? cvdComponents.series
              .data()
              .slice(-window.PS.CVD_CONFIG.lookbackPeriod)
          : [];
      if (lookbackData.length === 0) {
        return value >= 0 ? 0.5 : -0.5; // Default if no data
      }
      const values = lookbackData.map((d) => d.value);
      const min = Math.min(...values);
      const max = Math.max(...values);
      return window.mathUtils?.normalize
        ? window.mathUtils.normalize(value, min, max, { range: [-1, 1] })
        : min === max
          ? 0
          : ((value - min) / (max - min)) * 2 - 1;
    } catch (e) {

      return value >= 0 ? 0.5 : -0.5;
    }
  }

  function updateCVD() {
    // CVD updates now handled entirely by subscription system
    // This function kept for compatibility but no longer used
    return 0;
  }

  function resizeCVDChart(cvdComponents, _width, height) {
    try {
      if (cvdComponents && cvdComponents.chart) {
        if (cvdComponents.pane) {
          const cvdHeight = Math.max(150, Math.floor(height * 0.2));
          if (typeof cvdComponents.pane.setHeight === "function") {
            cvdComponents.pane.setHeight(cvdHeight);
          }
        } else {
          const chartContainer = document.querySelector(
            ".price-chart-container",
          );
          if (chartContainer) {
            const chartElement = chartContainer.querySelector(
              ".tv-lightweight-charts",
            );
            if (chartElement) {
              chartElement.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
              try {
                const panes = cvdComponents.chart.panes();
                if (panes && panes.length > 1) {
                  const cvdPane = panes[1];
                  cvdComponents.pane = cvdPane;
                  const cvdHeight = Math.max(150, Math.floor(height * 0.2));
                  if (typeof cvdPane.setHeight === "function") {
                    cvdPane.setHeight(cvdHeight);
                  }
                }
              } catch (paneError) {

                const paneElements = chartElement.querySelectorAll(
                  ".tv-lightweight-charts__pane",
                );
                if (paneElements && paneElements.length > 1) {
                  const cvdPaneElement = paneElements[1];
                  if (cvdPaneElement) {
                    cvdPaneElement.style.zIndex = "3";
                    cvdPaneElement.style.borderTop = "1px solid #2A2A2A";
                    cvdPaneElement.style.boxSizing = "border-box";
                  }
                }
              }
            }
          }
        }
      }
    } catch (e) {
      logger.warn("Error resizing CVD chart:", e);
    }
  }

  function cleanupCVD(cvdComponents, syncResources) {
    // Use shared utility for cleanup if available
    if (
      window.IndicatorChartUtils &&
      window.IndicatorChartUtils.cleanupIndicator
    ) {
      window.IndicatorChartUtils.cleanupIndicator(
        cvdComponents,
        [
          /* cvdUpdateInterval removed */ typeof syncResources === "number"
            ? syncResources
            : undefined,
        ],
        {
          // pendingCVDUpdates state is removed
        },
      );
    } else {
      // Fallback cleanup
      // if (cvdUpdateInterval) { // cvdUpdateInterval removed
      //     clearInterval(cvdUpdateInterval);
      // }
    }
    // cvdUpdateInterval = null; // cvdUpdateInterval removed
  }

  function renderPendingCVDUpdates(cvdComponents) {
    // Removed - updates happen directly in subscription
  }

  // --- Subscribe to CVD data store for real-time updates ---
  let cvdUnsubscribe = null;
  function handleCVDUpdate() {
    if (window.PS && typeof window.PS.getCurrentCVD === 'function') {
      const data = window.PS.getCurrentCVD();
      if (data && window.cvdModule && window.cvdModule.initializeCVDData && window.chartStates) {
        // Get current chart state from the global chartStates map
        const currentPair = window.currentPair || 'BTC'; // Default to BTC if no current pair
        const chartState = window.chartStates.get(currentPair);
        if (chartState && chartState.chart && chartState.cvdComponents && chartState.data && chartState.data.priceData) {
          window.cvdModule.initializeCVDData(chartState.cvdComponents, chartState.data.priceData);

        }
      }
    }
  }

  function subscribeToCVDUpdates() {
    if (window.PS && typeof window.PS.subscribeCVD === 'function') {
      if (cvdUnsubscribe) cvdUnsubscribe();
      cvdUnsubscribe = window.PS.subscribeCVD(handleCVDUpdate);

    }
  }

  function unsubscribeFromCVDUpdates() {
    if (cvdUnsubscribe) {
      cvdUnsubscribe();
      cvdUnsubscribe = null;

    }
  }

  // Subscribe on load
  subscribeToCVDUpdates();

  // Unsubscribe on chart switch
  if (!window._cvdChartSwitchListener) {
    window._cvdChartSwitchListener = function() {
      unsubscribeFromCVDUpdates();
    };
    document.addEventListener('pairChanged', window._cvdChartSwitchListener);
  }

  try {
    window.cvdModule = window.cvdModule || {
      createCVDChart,
      calculateAdjustedVolume,
      calculateCVDData,
      getNormalizedColor,
      initializeCVDData,
      synchronizeCharts,
      normalizeCVDWithComponents,
      updateCVD,
      resizeCVDChart,
      cleanupCVD,
      renderPendingCVDUpdates,
      config: CVD_CONFIG,
    };
  } catch (e) {
    console.error("CVD: Error creating module:", e);
  }

  // Enhanced data processing with transition state awareness
  function processCVDData(newData) {
    // CRITICAL FIX: Check transition state before processing
    if (window.chartTransitionState && window.chartTransitionState.isTransitioning) {
      return; // Skip processing during chart transitions
    }
    
    try {
      if (!newData || !newData.time || !newData.value) {
        logger.warn('CVD: Invalid data received:', newData);
        return;
      }
      
      // Enhanced data validation
      const timestamp = newData.time * 1000;
      const now = Date.now();
      
      // Skip data that's too old (more than 5 seconds)
      if (now - timestamp > 5000) {

        return;
      }
      
      // Process the data
      updateCVDChart(newData);
      
    } catch (error) {
      logger.warn('CVD: Error processing data:', error);
    }
  }
})();
