/**
 * PerpCVD Indicator Module
 * Now updates via API polling - WebSocket recovery logic removed
 */

(function () {
  // Initialize logger for this module
  const logger = window.logger?.createLogger('PerpCVD') || {
    debug: () => {},
    info: () => {},
    warn: console.warn.bind(console),
    error: console.error.bind(console)
  };

  // --- Tab visibility tracking and buffered chart update logic ---
  let isTabVisible = !document.hidden;
  let pendingCVDUpdateArgs = null;

  // Use unified visibility system from ConnectionManager
  window.addEventListener("visibility-restore", () => {
    isTabVisible = true;
    if (typeof window.triggerPerpCVDRedraw === "function") {
      window.triggerPerpCVDRedraw();
    }
  });
  
  window.addEventListener("visibility-hide", () => {
    isTabVisible = false;
  });

  // Listen for Bybit data from Service Worker and dispatch to CVD logic
  window.addEventListener("bybit-data", (event) => {
    try {
      const msg = JSON.parse(event.detail);
      if (
        msg.topic &&
        msg.topic.startsWith("publicTrade") &&
        Array.isArray(msg.data)
      ) {
        // Loop through each trade in the data array
        for (const trade of msg.data) {
          if (typeof updateCVDBuffered === "function") {
            updateCVDBuffered(trade);
          }
        }
      }
    } catch (e) {
      // Ignore non-JSON or unrelated messages
    }
  });

  document.addEventListener("pairChanged", (event) => {
    const newPair = event.detail?.pair;
    if (newPair && window.PS?.pendingPerpCVDUpdates) {

      window.PS.pendingPerpCVDUpdates.lastBarTime = 0;
      window.PS.pendingPerpCVDUpdates.lastCvdValue = 0;
      window.PS.pendingPerpCVDUpdates.pendingValue = 0;
      window.PS.pendingPerpCVDUpdates.hasUpdate = false;
      window.PS.pendingPerpCVDUpdates.lastRealTimeUpdate = 0;
      window.PS.pendingPerpCVDUpdates.lastTitleColor = null;
      window.PS.pendingPerpCVDUpdates.lastClearTime = Date.now(); // Track when updates were cleared
    }
  });

  // CRITICAL FIX: Add proper chart switching cleanup
  const originalSwitchPair = window.switchPairInternal;
  if (originalSwitchPair) {
    window.switchPairInternal = function(newPair) {
      // Clear PerpCVD data before switching
      if (window.PS?.setPerpCVDPriceData) {
        logger.debug(`PERP CVD: Chart switched to ${newPair}, clearing data store`);
        window.PS.setPerpCVDPriceData([]);
      }
      
      // Clear pending updates
      if (window.PS?.pendingPerpCVDUpdates) {
        window.PS.pendingPerpCVDUpdates.lastBarTime = 0;
        window.PS.pendingPerpCVDUpdates.lastCvdValue = 0;
        window.PS.pendingPerpCVDUpdates.pendingValue = 0;
        window.PS.pendingPerpCVDUpdates.hasUpdate = false;
        window.PS.pendingPerpCVDUpdates.lastRealTimeUpdate = 0;
        window.PS.pendingPerpCVDUpdates.lastTitleColor = null;
        window.PS.pendingPerpCVDUpdates.lastClearTime = Date.now();
      }
      
      // Call original function
      return originalSwitchPair.call(this, newPair);
    };
  }

  window.triggerPerpCVDRedraw = function () {
    if (pendingCVDUpdateArgs) {
      updateCVD(...pendingCVDUpdateArgs);
      pendingCVDUpdateArgs = null;
    }
  };

  function updateCVDBuffered(...args) {
    if (isTabVisible) {
      updateCVD(...args);
      pendingCVDUpdateArgs = null;
    } else {
      pendingCVDUpdateArgs = args;
    }
  }
  // --------------------------------------------------------------

  if (!window.utils) {
    setTimeout(() => {}, 500);
    return;
  }
  const { getIndicatorColor } = window.utils;
  const formatLargeNumber = window.commonUtils.formatLargeNumber;
  const { ema, stdev, clamp, lerp, weightedAverage } = window.mathUtils;

  const CVD_CONFIG = window.PS?.CVD_CONFIG ||
    window.CONFIG?.perpCvd || {
      volumeMAPeriod: 90,
      volumeAdjustment: {
        enabled: true,
        buyMultiplier: 1.0,
        sellMultiplier: 1.0,
        useWicks: true,
        useBodySize: true,
        useCloseRelative: true,
      },
      renderOnCandleCloseOnly: true,
      lookbackPeriod: (() => {
        const savedWindow = localStorage.getItem("normalizationWindow");
        return savedWindow ? parseInt(savedWindow) : 1440;
      })(),
      normalize: true,
      smoothing: true,
      sensitivityMultiplier: 1.2,
      normalizationBuffer: 0,
      minSmoothingPeriod: 5,
      maxSmoothingPeriod: 20,
      adaptiveSmoothingFactor: 0.5,
      volumeWeighting: {
        enabled: true,
        weightFactor: 0.5,
      },
    };

  window.PS = window.PS || {};
  window.PS.CVD_CONFIG = CVD_CONFIG;

  async function load6000Bybit5mBars(symbol) {
    if (!window.fetchBybitHistoricalData) {
      throw new Error("Enhanced Bybit historical data fetcher not available");
    }

    const bars = await window.fetchBybitHistoricalData(
      symbol,
      300,
      6000,
      (progressBars) => {},
    );

    if (!bars || bars.length === 0) {
      throw new Error("No bars returned from enhanced Bybit fetcher");
    }

    let invalidBars = 0;
    for (let i = 0; i < bars.length; i++) {
      const bar = bars[i];
      if (
        !bar.time ||
        bar.volume === undefined ||
        isNaN(bar.volume) ||
        isNaN(bar.open) ||
        isNaN(bar.high) ||
        isNaN(bar.low) ||
        isNaN(bar.close)
      ) {
        invalidBars++;
      }
    }

    if (invalidBars > 0) {
      let orderingIssues = 0;
      for (let i = 1; i < bars.length; i++) {
        if (bars[i].time <= bars[i - 1].time) {
          orderingIssues++;
          if (orderingIssues === 1) {
          }
        }
      }
      if (orderingIssues > 0) {
      }
    }
    return bars;
  }

  let historicalCVDData = [];
  let perpCvdNormalizationWindow = [];

  let lastRenderTime = 0;
  let renderRequestId = null;

  function cleanupCVD(cvdComponents) {
    if (renderRequestId) {
      try {
        cancelAnimationFrame(renderRequestId);
      } catch (e) {
        logger.debug("Error canceling animation frame:", e);
      }
      renderRequestId = null;
    }

    // CRITICAL FIX: Unsubscribe from data store updates
    if (cvdComponents?.unsubscribePerpCVD && typeof cvdComponents.unsubscribePerpCVD === 'function') {
      try {
        cvdComponents.unsubscribePerpCVD();
        logger.debug("PERP CVD: Unsubscribed from data store updates");
      } catch (e) {
        logger.warn("PERP CVD: Error unsubscribing from data store:", e);
      }
    }

    // Remove visibility event listener and clear buffer
    document.removeEventListener("visibilitychange", () => {});
    pendingCVDUpdateArgs = null;

    if (window.IndicatorChartUtils) {
      window.IndicatorChartUtils.cleanupIndicator(cvdComponents, [], {});
    }
  }

  function createCVDChart(container, priceChart) {
    let cvdPane;
    try {
      const panes = priceChart.panes();
      if (panes && panes.length > 1) {
        cvdPane = panes[1];
        cvdPane.applyOptions({ visible: true });
        if (typeof cvdPane.setHeight === "function") {
          cvdPane.setHeight(150);
        }
        cvdPane.applyOptions({
          rightPriceScale: {
            visible: true,
            borderColor: "#2A2A2A",
            scaleMargins: { top: 0.1, bottom: 0.1 },
            formatter: {
              format: (price) => formatLargeNumber(price),
            },
          },
        });
      }
    } catch (e) {}

    const cvdSeries = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: {
          type: "volume",
          formatter: (price) => formatLargeNumber(price),
        },
        color: "#FF6B35",
        lineWidth: 1.5,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "PERP CVD",
        titleColor: "#FF6B35",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const cvdMASeries = {
      update: () => {},
      setData: () => {},
      applyOptions: () => {},
      _internal_isDisposed: false,
    };

    const zeroLine = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "#444444",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const level1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.perpCvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const levelMinus1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: window.CONFIG.perpCvd.colors.neutral,
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const referenceLines = { level1: level1Line, levelMinus1: levelMinus1Line };

    priceChart.applyOptions({
      layout: {
        background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        panes: {
          separatorColor: "#2A2A2A",
          separatorHoverColor: "rgba(255, 0, 0, 0.1)",
          enableResize: true,
        },
      },
    });

    try {
      const chartContainer = container.querySelector(".tv-lightweight-charts");
      if (chartContainer) {
        chartContainer.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
      }
    } catch (e) {}

    return {
      chart: priceChart,
      pane: cvdPane,
      series: cvdSeries,
      zeroLine: zeroLine,
      referenceLines: referenceLines,
    };
  }

  function calculateAdjustedVolume(bar, prevBar) {
    if (!bar) return 0;
    const volume =
      bar.volume !== undefined && !isNaN(bar.volume) ? bar.volume : 0;
    if (volume === 0) return 0;
    let isBuyBar = true;
    if (
      window.PS.CVD_CONFIG.volumeAdjustment.useCloseRelative &&
      prevBar &&
      prevBar.close !== undefined &&
      !isNaN(prevBar.close)
    ) {
      isBuyBar = bar.close >= prevBar.close;
    } else {
      isBuyBar = bar.close >= bar.open;
    }
    let adjustmentFactor = 1.0;
    if (window.PS.CVD_CONFIG.volumeAdjustment.useBodySize) {
      const bodySize = Math.abs(bar.close - bar.open);
      const range = bar.high - bar.low;
      if (range > 0 && isFinite(bodySize) && isFinite(range)) {
        const bodySizePercent = bodySize / range;
        adjustmentFactor *= 0.7 + bodySizePercent * 0.6;
      }
    }
    if (window.PS.CVD_CONFIG.volumeAdjustment.useWicks) {
      const totalRange = bar.high - bar.low;
      if (totalRange > 0 && isFinite(totalRange)) {
        const upperWick = bar.high - Math.max(bar.open, bar.close);
        const lowerWick = Math.min(bar.open, bar.close) - bar.low;
        if (isFinite(upperWick) && isFinite(lowerWick)) {
          if (isBuyBar) {
            const lowerWickPercent = lowerWick / totalRange;
            adjustmentFactor *= 1 + lowerWickPercent * 0.8;
          } else {
            const upperWickPercent = upperWick / totalRange;
            adjustmentFactor *= 1 + upperWickPercent * 0.8;
          }
        }
      }
    }
    adjustmentFactor = Math.max(0.5, Math.min(2.0, adjustmentFactor));
    return isBuyBar
      ? volume *
          adjustmentFactor *
          window.PS.CVD_CONFIG.volumeAdjustment.buyMultiplier
      : -volume *
          adjustmentFactor *
          window.PS.CVD_CONFIG.volumeAdjustment.sellMultiplier;
  }

  const cvdDataCache = new WeakMap();
  function calculateCVDData(priceData) {
    if (cvdDataCache.has(priceData)) return cvdDataCache.get(priceData);

    if (!priceData || priceData.length === 0) {
      return [];
    }

    const cvdData = [];
    let cumulativeDelta = 0;
    let skippedBars = 0;

    for (let i = 0; i < priceData.length; i++) {
      const bar = priceData[i];
      const prevBar = i > 0 ? priceData[i - 1] : null;

      if (!bar || !bar.time || bar.volume === undefined || isNaN(bar.volume)) {
        skippedBars++;
        continue;
      }

      const barDelta = calculateAdjustedVolume(bar, prevBar);

      if (isNaN(barDelta)) {
        skippedBars++;
        continue;
      }

      cumulativeDelta += barDelta;
      cvdData.push({ time: bar.time, value: cumulativeDelta });
    }

    cvdDataCache.set(priceData, cvdData);
    return cvdData;
  }

  function getNormalizedColor(normalizedValue) {
    if (normalizedValue > 0.5) {
      return "rgba(255, 0, 0, 0.8)";
    } else if (normalizedValue < -0.5) {
      return "rgba(0, 255, 255, 0.8)";
    } else {
      return "rgba(170, 170, 170, 0.8)";
    }
  }

  function initializeCVDData(cvdComponents, priceData) {
    if (!priceData || priceData.length === 0) {
      return { cvdData: [], cvdMAData: [] };
    }

    try {
      // Ensure we only use closed bars - check current bar status
      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const currentBarTime = Math.floor(now / barInterval) * barInterval;
      const lastBarTime = priceData[priceData.length - 1]?.time;

      // Only use bars that are definitely closed
      const closedBars = priceData.filter((bar) => bar.time < currentBarTime);
      if (closedBars.length === 0) {
        console.error("PERP CVD: No valid closed bars available");
        return { cvdData: [], cvdMAData: [] };
      }

      const cvdData = calculateCVDData(closedBars);
      if (cvdData.length === 0) {
        return { cvdData: [], cvdMAData: [] };
      }

      const expectedDataPoints = closedBars.filter(
        (bar) =>
          bar && bar.time && bar.volume !== undefined && !isNaN(bar.volume),
      ).length;

      if (cvdData.length < expectedDataPoints * 0.9) {
      }

      const values = cvdData.map((d) => d.value).filter((v) => isFinite(v));
      if (values.length === 0) {
        return { cvdData: [], cvdMAData: [] };
      }

      // Store raw CVD values for rolling normalization
      perpCvdNormalizationWindow = values.slice(-1440);

      // Use mathUtils.rollingNormalize for efficient rolling normalization
      const normalizedValues = window.mathUtils.rollingNormalize(values, 1440);
      const normalizedCVDData = cvdData.map((point, i) => {
        const normalizedValue = normalizedValues[i];
        const color = getNormalizedColor(normalizedValue);
        return {
          time: point.time,
          value: isFinite(normalizedValue) ? normalizedValue : 0,
          color: color,
        };
      });

      // Store last historical bar time for websocket continuation
      const lastHistoricalBar = normalizedCVDData[normalizedCVDData.length - 1];

      if (!window.PS) window.PS = {};
      if (!window.PS.pendingPerpCVDUpdates) {
        window.PS.pendingPerpCVDUpdates = {
          lastBarTime: 0,
          pendingValue: 0,
          hasUpdate: false,
          lastCvdValue: 0,
          normalizedData: [],
          priceData: [],
          avgVolume: 0,
          maxHistoryLength: 1000,
          connectionErrors: 0,
          lastDataTime: 0,
          reconnectAttempts: 0,
          maxReconnectAttempts: 10,
          healthCheckInterval: null,
        };
      }
      const pendingCVDUpdates = window.PS.pendingPerpCVDUpdates;

      const emptyMAData = cvdData.map((point) => ({
        time: point.time,
        value: 0,
      }));
      const zeroLineData = [];

      if (closedBars.length > 0) {
        const firstTime = closedBars[0].time;
        const lastTime = closedBars[closedBars.length - 1].time;
        zeroLineData.push({ time: firstTime, value: 0 });
        zeroLineData.push({ time: lastTime, value: 0 });

        const lastDataPoint = normalizedCVDData[normalizedCVDData.length - 1];
        if (lastDataPoint) {
          pendingCVDUpdates.lastBarTime = lastDataPoint.time;
          pendingCVDUpdates.pendingValue = lastDataPoint.value;
          pendingCVDUpdates.pendingEmaValue = 0;
          pendingCVDUpdates.lastCvdValue = lastDataPoint.value; // This is already normalized
          pendingCVDUpdates.hasUpdate = false;
        }
      } else {
        const now = Math.floor(Date.now() / 1000);
        zeroLineData.push({ time: now - 86400, value: 0 });
        zeroLineData.push({ time: now, value: 0 });
      }

      try {
        cvdComponents.series.setData(normalizedCVDData);
        
        // Set initial title color based on the last data point
        if (normalizedCVDData.length > 0) {
          const lastPoint = normalizedCVDData[normalizedCVDData.length - 1];
          cvdComponents.series.applyOptions({
            titleColor: lastPoint.color
          });
        }
        
        cvdComponents.zeroLine.setData(zeroLineData);
        if (cvdComponents.referenceLines.level1)
          cvdComponents.referenceLines.level1.setData(
            zeroLineData.map((d) => ({ ...d, value: 1 })),
          );
        if (cvdComponents.referenceLines.levelMinus1)
          cvdComponents.referenceLines.levelMinus1.setData(
            zeroLineData.map((d) => ({ ...d, value: -1 })),
          );
      } catch (e) {
        logger.warn("PERP CVD: Error during chart update:", e);
      }

      // CRITICAL FIX: Subscribe to data store updates for real-time chart updates
      if (window.PS?.subscribePerpCVD && typeof window.PS.subscribePerpCVD === 'function') {
        // Store the unsubscribe function for cleanup
        cvdComponents.unsubscribePerpCVD = window.PS.subscribePerpCVD((newData) => {
          if (newData && cvdComponents.series && !cvdComponents.series._internal_isDisposed) {
            try {
              // Update the chart with new data
              const color = getNormalizedColor(newData.value);
              cvdComponents.series.applyOptions({
                titleColor: color
              });
              
              // Add the new data point to the series
              cvdComponents.series.update({
                time: typeof newData.time === 'object' && newData.time !== null && typeof newData.time.getTime === 'function'
                  ? Math.floor(newData.time.getTime() / 1000)
                  : Number(newData.time),
                value: newData.value,
                color: color
              });
              

            } catch (e) {
              // Handle "Cannot update oldest data" error gracefully
              if (e.message && e.message.includes('Cannot update oldest data')) {

                return;
              }
              logger.warn("PERP CVD: Error updating chart with new data:", e);
            }
          }
        });
        

      } else {
        logger.warn("PERP CVD: Data store subscription function not available");
      }

      // Mark historical data as loaded and ready for websocket continuation
      if (!window.PS) window.PS = {};
      window.PS.perpCvdHistoricalLoaded = true;

      return { cvdData: normalizedCVDData, cvdMAData: emptyMAData };
    } catch (e) {
      return { cvdData: [], cvdMAData: [] };
    }
  }

  function synchronizeCharts(cvdComponents, priceChart) {
    const syncHandle = window.IndicatorChartUtils
      ? window.IndicatorChartUtils.synchronizeCharts(
          cvdComponents,
          priceChart,
          {
            referenceLevels: {},
          },
        )
      : null;

    const updateIndicatorColor = () => {};

    updateIndicatorColor();

    return {
      unsubscribe: () => {
        try {
          syncHandle?.unsubscribe();
        } catch (e) {}
      },
      updateIndicatorColor: updateIndicatorColor,
    };
  }

  function normalizeCVDWithComponents(value, cvdComponents) {
    try {
      if (historicalCVDData.length === 0) {
        const currentData = cvdComponents.series.data();
        if (currentData && currentData.length > 0) {
          historicalCVDData = currentData.map((d) => ({
            time: d.time,
            value: d.value,
          }));
        }
      }
      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const currentBarTime = Math.floor(now / barInterval) * barInterval;
      if (
        historicalCVDData.length === 0 ||
        historicalCVDData[historicalCVDData.length - 1].time !== currentBarTime
      ) {
        historicalCVDData.push({ time: currentBarTime, value: value });
        if (historicalCVDData.length > CVD_CONFIG.lookbackPeriod * 2) {
          historicalCVDData = historicalCVDData.slice(
            -CVD_CONFIG.lookbackPeriod * 2,
          );
        }
      } else {
        historicalCVDData[historicalCVDData.length - 1].value = value;
      }
      const lookbackData = historicalCVDData.slice(-CVD_CONFIG.lookbackPeriod);
      if (lookbackData.length === 0) {
        return value >= 0 ? 0.5 : -0.5;
      }
      const min = Math.min(...lookbackData.map((d) => d.value));
      const max = Math.max(...lookbackData.map((d) => d.value));
      return normalizeCVD(value, min, max);
    } catch (e) {
      return value >= 0 ? 0.5 : -0.5;
    }
  }

  function normalizeCVD(value, min, max) {
    if (max === min) return value >= 0 ? 0.5 : -0.5;
    return ((value - min) / (max - min)) * 2 - 1;
  }

  function updateCVD() {
    // PerpCVD updates now handled entirely by subscription system
    return 0;
  }

  function resizeCVDChart(cvdComponents, _width, height) {
    try {
      if (cvdComponents && cvdComponents.chart) {
        if (cvdComponents.pane) {
          const cvdHeight = Math.max(150, Math.floor(height * 0.2));
          if (typeof cvdComponents.pane.setHeight === "function") {
            cvdComponents.pane.setHeight(cvdHeight);
          }
        } else {
          const chartContainer = document.querySelector(
            ".price-chart-container",
          );
          if (chartContainer) {
            const chartElement = chartContainer.querySelector(
              ".tv-lightweight-charts",
            );
            if (chartElement) {
              chartElement.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
              try {
                const panes = cvdComponents.chart.panes();
                if (panes && panes.length > 1) {
                  const cvdPane = panes[1];
                  cvdComponents.pane = cvdPane;
                  const cvdHeight = Math.max(150, Math.floor(height * 0.2));
                  if (typeof cvdPane.setHeight === "function") {
                    cvdPane.setHeight(cvdHeight);
                  }
                }
              } catch (paneError) {
                const paneElements = chartElement.querySelectorAll(
                  ".tv-lightweight-charts__pane",
                );
                if (paneElements && paneElements.length > 1) {
                  const cvdPaneElement = paneElements[1];
                  if (cvdPaneElement) {
                    cvdPaneElement.style.zIndex = "3";
                    cvdPaneElement.style.borderTop = "1px solid #2A2A2A";
                    cvdPaneElement.style.boxSizing = "border-box";
                  }
                }
              }
            }
          }
        }
      }
    } catch (e) {}
  }

  function renderPendingCVDUpdates(cvdComponents) {
    // Removed - updates happen directly in subscription
  }

  // WebSocket subscription setup removed - indicators now update via API polling

  function updateCustomCvdLegend(color) {
    let legend = document.getElementById("perp-cvd-legend");
    if (!legend) {
      legend = document.createElement("div");
      legend.id = "perp-cvd-legend";
      legend.style.position = "absolute";
      legend.style.top = "8px";
      legend.style.right = "16px";
      legend.style.zIndex = "10";
      legend.style.fontWeight = "bold";
      legend.style.fontSize = "13px";
      legend.style.pointerEvents = "none";
      legend.style.userSelect = "none";
      legend.style.padding = "2px 8px";
      legend.style.borderRadius = "4px";
      legend.style.background = "rgba(15,20,26,0.7)";
      legend.textContent = "PERP CVD";
      document.body.appendChild(legend);
    }
    legend.style.color = color;
  }

  try {
    window.perpCvdModule = window.perpCvdModule || {
      createCVDChart,
      calculateAdjustedVolume,
      calculateCVDData,
      getNormalizedColor,
      initializeCVDData,
      synchronizeCharts,
      normalizeCVDWithComponents,
      updateCVD,
      resizeCVDChart,
      cleanupCVD,
      renderPendingCVDUpdates,
      // WebSocket functions removed - indicators now update via API polling
      config: CVD_CONFIG,
    };
  } catch (e) {
    window.perpCvdModule = {
      createCVDChart: () => ({ series: null, syncResources: null }),
      calculateAdjustedVolume: () => {},
      calculateCVDData: () => [],
      getNormalizedColor: () => "#aaa",
      initializeCVDData: () => {},
      synchronizeCharts: () => ({}),
      normalizeCVDWithComponents: () => 0,
      updateCVD: () => {},
      resizeCVDChart: () => {},
      cleanupCVD: () => {},
      renderPendingCVDUpdates: () => {},
      // WebSocket functions removed
    };
  }

  document.addEventListener("DOMContentLoaded", async () => {
    if (!window.utils || !window.mathUtils) {
      return;
    }

    const configObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-config"
        ) {
          const newConfig = JSON.parse(
            mutation.target.getAttribute("data-config"),
          );
          if (newConfig.perpCvd) {
            Object.assign(CVD_CONFIG, newConfig.perpCvd);
          }
        }
      }
    });

    configObserver.observe(document.body, {
      attributes: true,
      childList: false,
      subtree: false,
    });

    let priceChartContainer;
    const waitForPriceChart = new Promise((resolve) => {
      const checkExist = setInterval(() => {
        priceChartContainer = document.querySelector(".price-chart-container");
        if (priceChartContainer) {
          clearInterval(checkExist);
          resolve(priceChartContainer);
        }
      }, 500);
    });

    try {
      await waitForPriceChart;

      // Ensure LightweightCharts is available
      if (!window.LightweightCharts) {
        console.error("PERP CVD: LightweightCharts library is not loaded.");
        return;
      }

      // Only create chart if not already present
      let priceChart = window.priceChart;
      if (!priceChart) {
        priceChart = window.LightweightCharts.createChart(priceChartContainer, {
          layout: {
            backgroundColor: "rgba(15, 20, 26, 1.0)",
            textColor: "#FFFFFF",
          },
          grid: {
            vertLines: {
              color: "#2A2A2A",
            },
            horzLines: {
              color: "#2A2A2A",
            },
          },
          crosshair: {
            mode: window.LightweightCharts.CrosshairMode.Normal,
          },
          timeScale: {
            borderColor: "#2A2A2A",
          },
        });
        window.priceChart = priceChart;
      }

      const cvdComponents = createCVDChart(priceChartContainer, priceChart);

      // WebSocket setup removed - indicators now update via API polling
      // setupCVDUpdateInterval(cvdComponents);

      // Only observe resize if container and chart exist
      if (priceChartContainer && priceChart) {
        const resizeObserver = new ResizeObserver(() => {
          const { width, height } = priceChartContainer.getBoundingClientRect();
          resizeCVDChart(cvdComponents, width, height);
        });
        resizeObserver.observe(priceChartContainer);
      }
    } catch (e) {
      console.error("PERP CVD: Initialization error:", e);
    }
  });

  // Remove global recovery utility functions - no longer needed
  // window.recoverIndicators = function() { ... };
  // window.checkIndicatorStatus = function() { ... };

  // Throttle rAF and interval-based updates to 30fps or less
  let lastCvdFrame = 0;
  const minCvdFrameInterval = 33; // ~30fps
  function throttledCvdAnimation(callback) {
    const now = Date.now();
    if (now - lastCvdFrame >= minCvdFrameInterval) {
      lastCvdFrame = now;
      callback();
    } else {
      setTimeout(() => requestAnimationFrame(() => callback()), minCvdFrameInterval - (now - lastCvdFrame));
    }
  }
  // Use throttledCvdAnimation for any rAF-based updates
  // Debounce listener notifications if present
  let notifyTimeout = null;
  function debouncedNotifyListeners(listeners, data) {
    if (notifyTimeout) return;
    notifyTimeout = setTimeout(() => {
      notifyTimeout = null;
      listeners.forEach(cb => cb(data));
    }, 100); // Notify at most every 100ms
  }

  // Enhanced data processing with transition state awareness
  function processPerpCVDData(newData) {
    // CRITICAL FIX: Check transition state before processing
    if (window.chartTransitionState && window.chartTransitionState.isTransitioning) {
      return; // Skip processing during chart transitions
    }
    
    try {
      if (!newData || !newData.time || !newData.value) {
        logger.warn('PERP CVD: Invalid data received:', newData);
        return;
      }
      
      // Enhanced data validation
      const timestamp = newData.time * 1000;
      const now = Date.now();
      
      // Skip data that's too old (more than 5 seconds)
      if (now - timestamp > 5000) {

        return;
      }
      
      // Process the data
      updatePerpCVDChart(newData);
      
    } catch (error) {
      logger.warn('PERP CVD: Error processing data:', error);
    }
  }
})();
