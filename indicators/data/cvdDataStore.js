// socket-profile5.0/indicators/data/cvdDataStore.js
// Global CVD data store with background updating and subscription API (classic script compatible)

// Use centralized throttle function from commonUtils

const cvdListeners = [];
let cvdData = null;
let isCVDUpdating = false;
let lastCVDUpdateTime = 0;
let cvdUpdateDebounceTimer = null;
let notifyTimeout = null;

/**
 * CVD Data Store
 * Manages Cumulative Volume Delta data and subscriptions
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("CVDDataStore")
    : console;

  // Configuration - use centralized config with fallback
  const CVD_CONFIG = window.CONFIG?.cvd || {
    updateIntervalMs: 1000,
    volumeMAPeriod: 90,
    volumeAdjustment: {
      enabled: true,
      buyMultiplier: 1.0,
      sellMultiplier: 1.0,
      useWicks: true,
      useBodySize: true,
      useCloseRelative: true,
    },
    renderOnCandleCloseOnly: true,
    lookbackPeriod: 1440,
    normalize: true,
    smoothing: true,
    sensitivityMultiplier: 1.2,
    normalizationBuffer: 0,
    minSmoothingPeriod: 5,
    maxSmoothingPeriod: 20,
    adaptiveSmoothingFactor: 0.5,
    volumeWeighting: {
      enabled: true,
      weightFactor: 0.5,
    },
    colors: {
      positive: "#00FF7F",
      negative: "#FF6B6B",
      neutral: "#888888",
    },
  };
  const cvdUpdateInterval = CVD_CONFIG.updateIntervalMs;

  // State
  let historicalCVDData = [];

  // You must provide priceData from your app's data source
  let cvdPriceData = []; // <-- You must update this externally for real data

  // CRITICAL FIX: Function to clear CVD data store when switching charts
  function clearCVDDataStore() {
    logger.debug("CVD DataStore: Clearing data store for chart switch");
    cvdPriceData = [];
    cvdData = null;
    historicalCVDData = [];
    isCVDUpdating = false;
    lastCVDUpdateTime = 0;
    if (cvdUpdateDebounceTimer) {
      clearTimeout(cvdUpdateDebounceTimer);
      cvdUpdateDebounceTimer = null;
    }
    // Clear tracking variables
    window.lastCVDCallTime = null;
    window.lastCVDDataHash = null;
  }

  window.PS = window.PS || {};
  window.PS.setCVDPriceData = function (newPriceData) {
    if (
      !newPriceData ||
      !Array.isArray(newPriceData) ||
      newPriceData.length === 0
    ) {
      return;
    }
    // Always update with the latest data for stability
    cvdPriceData = newPriceData;
    updateCVDData();
    // Log the update for debugging
    const now = Math.floor(Date.now() / 1000);
    const barInterval = 300;
    const currentBarTime = Math.floor(now / barInterval) * barInterval;
    const closedBars = newPriceData.filter((bar) => bar.time < currentBarTime);
    logger.debug(
      `CVD: Data store updated with ${newPriceData.length} total bars, ${closedBars.length} closed bars`,
    );
  };

  function calculateAdjustedVolume(bar, prevBar) {
    if (!bar) return 0;
    const volume =
      bar.volume !== undefined && !isNaN(bar.volume) ? bar.volume : 0;
    if (volume === 0) return 0;
    let isBuyBar = true;
    if (
      CVD_CONFIG.volumeAdjustment.useCloseRelative &&
      prevBar &&
      prevBar.close !== undefined &&
      !isNaN(prevBar.close)
    ) {
      isBuyBar = bar.close >= prevBar.close;
    } else {
      isBuyBar = bar.close >= bar.open;
    }
    let adjustmentFactor = 1.0;
    if (CVD_CONFIG.volumeAdjustment.useBodySize) {
      const bodySize = Math.abs(bar.close - bar.open);
      const range = bar.high - bar.low;
      if (range > 0 && isFinite(bodySize) && isFinite(range)) {
        const bodySizePercent = bodySize / range;
        adjustmentFactor *= 0.7 + bodySizePercent * 0.6;
      }
    }
    if (CVD_CONFIG.volumeAdjustment.useWicks) {
      const totalRange = bar.high - bar.low;
      if (totalRange > 0 && isFinite(totalRange)) {
        const upperWick = bar.high - Math.max(bar.open, bar.close);
        const lowerWick = Math.min(bar.open, bar.close) - bar.low;
        if (isFinite(upperWick) && isFinite(lowerWick)) {
          if (isBuyBar) {
            const lowerWickPercent = lowerWick / totalRange;
            adjustmentFactor *= 1 + lowerWickPercent * 0.8;
          } else {
            const upperWickPercent = upperWick / totalRange;
            adjustmentFactor *= 1 + upperWickPercent * 0.8;
          }
        }
      }
    }
    adjustmentFactor = Math.max(0.5, Math.min(2.0, adjustmentFactor));
    return isBuyBar
      ? volume * adjustmentFactor * CVD_CONFIG.volumeAdjustment.buyMultiplier
      : -volume * adjustmentFactor * CVD_CONFIG.volumeAdjustment.sellMultiplier;
  }

  function calculateCVDData(priceData) {
    if (!Array.isArray(priceData) || priceData.length === 0) {
      return [];
    }

    const cvdData = [];
    let cumulativeDelta = 0;

    try {
      for (let i = 0; i < priceData.length; i++) {
        const bar = priceData[i];
        const prevBar = i > 0 ? priceData[i - 1] : null;
        if (!bar || !bar.time || bar.volume === undefined) continue;
        const barDelta = calculateAdjustedVolume(bar, prevBar);
        cumulativeDelta += barDelta;
        cvdData.push({ time: bar.time, value: cumulativeDelta });
      }
    } catch (error) {
      window.errorHandler.handleError(
        error,
        "CVD DataStore: Error in calculateCVDData",
      );
    }

    return cvdData;
  }

  function normalizeCVD(value, min, max) {
    if (max === min) return 0;
    return ((value - min) / (max - min)) * 2 - 1;
  }

  function computeRollingMinMax(data, window, valueAccessor) {
    if (!Array.isArray(data) || data.length === 0) {
      return { minValues: [], maxValues: [] };
    }

    valueAccessor =
      valueAccessor ||
      function (d) {
        return d.value;
      };
    const minValues = [];
    const maxValues = [];

    try {
      for (let i = 0; i < data.length; i++) {
        const start = Math.max(0, i - window + 1);
        let min = Infinity,
          max = -Infinity;
        for (let j = start; j <= i; j++) {
          const v = valueAccessor(data[j]);
          if (v < min) min = v;
          if (v > max) max = v;
        }
        minValues.push(min);
        maxValues.push(max);
      }
    } catch (error) {
      window.errorHandler.handleError(
        error,
        "CVD DataStore: Error in computeRollingMinMax",
      );
    }

    return { minValues, maxValues };
  }

  function getCVDColor(value) {
    // Use the same color logic as the CVD module's getNormalizedColor function
    if (value > 0.5) {
      return "rgba(255, 0, 0, 0.8)"; // red for strong positive
    } else if (value < -0.5) {
      return "rgba(0, 255, 255, 0.8)"; // cyan for strong negative
    } else {
      return "rgba(170, 170, 170, 0.8)"; // gray for neutral
    }
  }

  function getLatestNormalizedCVD() {
    if (!cvdPriceData.length) return null;

    try {
      // Only use closed bars - filter out the current (open) bar
      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const currentBarTime = Math.floor(now / barInterval) * barInterval;
      const closedBars = cvdPriceData.filter(
        (bar) => bar.time < currentBarTime,
      );

      if (closedBars.length === 0) {
        return null;
      }

      const cvdDataArr = calculateCVDData(closedBars);
      historicalCVDData = cvdDataArr.slice();
      const { minValues, maxValues } = computeRollingMinMax(
        cvdDataArr,
        CVD_CONFIG.lookbackPeriod,
        function (p) {
          return p.value;
        },
      );
      const lastIdx = cvdDataArr.length - 1;
      if (lastIdx < 0) return null;
      const last = cvdDataArr[lastIdx];
      const min = minValues[lastIdx];
      const max = maxValues[lastIdx];
      const normalizedValue = normalizeCVD(last.value, min, max);
      return {
        time: last.time,
        value: normalizedValue,
        rawValue: last.value,
        color: getCVDColor(normalizedValue),
      };
    } catch (error) {
      window.errorHandler.handleError(
        error,
        "CVD DataStore: Error in getLatestNormalizedCVD",
      );
      return null;
    }
  }

  function fetchOrCalculateCVD() {
    // You must update cvdPriceData externally for this to reflect new data!
    return getLatestNormalizedCVD();
  }

  function updateCVDData() {
    if (isCVDUpdating) return;
    isCVDUpdating = true;

    try {
      const newData = fetchOrCalculateCVD();
      if (newData) {
        // Update the global cvdData variable
        cvdData = newData;

        // Check if this is a new bar or real-time update
        const lastBarTime =
          cvdPriceData.length > 0
            ? cvdPriceData[cvdPriceData.length - 1].time
            : 0;
        const isNewBar = newData.time > lastBarTime;

        if (isNewBar) {
          logger.debug(
            `CVD DataStore: New bar calculated - Time: ${newData.time}, Value: ${newData.value}`,
          );
          logger.debug(
            `CVD: Notifying ${cvdListeners.length} listeners with new data - Time: ${newData.time}, Value: ${newData.value}`,
          );
        }

        // Notify all listeners
        debouncedNotifyListeners();
      }
    } catch (error) {
      window.errorHandler.handleError(
        error,
        "CVD DataStore: Error in updateCVDData",
      );
    } finally {
      isCVDUpdating = false;
    }
  }

  // Start background updater
  // setInterval(updateCVDData, cvdUpdateInterval); // Removed: Update triggered by setCVDPriceData

  // Subscription API
  window.PS = window.PS || {};
  window.PS.getCurrentCVD = function () {
    return cvdData;
  };

  // Add subscribeCVD for real-time updates
  window.PS.subscribeCVD = function (cb) {
    if (typeof cb !== "function") {
      return function () {};
    }
    cvdListeners.push(cb);
    // Immediately send current data if available
    if (cvdData !== null) {
      try {
        cb(cvdData);
      } catch (e) {
        window.errorHandler.handleError(
          e,
          "[CVD DataStore] Error calling new listener immediately",
        );
      }
    }
    // Return unsubscribe function
    return function () {
      const idx = cvdListeners.indexOf(cb);
      if (idx !== -1) {
        cvdListeners.splice(idx, 1);
      }
    };
  };

  // CRITICAL FIX: Expose clear function for chart switching
  window.PS = window.PS || {};
  window.PS.clearCVDDataStore = clearCVDDataStore;

  // CRITICAL FIX: Listen for chart switching to clear data store
  document.addEventListener("pairChanged", (event) => {
    const newPair = event.detail?.pair;
    if (newPair) {
      logger.debug(
        `CVD DataStore: Chart switched to ${newPair}, clearing data store`,
      );
      clearCVDDataStore();
    }
  });

  function debouncedNotifyListeners() {
    if (notifyTimeout) return;
    notifyTimeout = setTimeout(() => {
      notifyTimeout = null;
      cvdListeners.forEach((cb) => cb());
    }, 100); // Notify at most every 100ms
  }
})(); // <-- Add missing closing IIFE bracket
