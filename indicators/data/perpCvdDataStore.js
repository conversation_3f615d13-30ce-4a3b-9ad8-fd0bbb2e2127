// socket-profile5.0/indicators/data/perpCvdDataStore.js
// Global PerpCVD data store with background updating and subscription API (classic script compatible)

// Use centralized throttle function from commonUtils

const perpCvdListeners = [];
let perpCvdData = null;
let isPerpCVDUpdating = false;

// STANDARDIZED: Add throttling variables to match other data stores
let lastPerpCVDUpdateTime = 0;
let perpCVDUpdateDebounceTimer = null;

// let perpCvdUpdateInterval = 1000; // ms // Will be derived from PERP_CVD_CONFIG

// Unique config object to avoid redeclaration
// Configuration - use centralized config with fallback
const PERP_CVD_CONFIG = window.CONFIG?.perpCvd || {
  updateIntervalMs: 1000,
  volumeMAPeriod: 90,
  volumeAdjustment: {
    enabled: true,
    buyMultiplier: 1.0,
    sellMultiplier: 1.0,
    useWicks: true,
    useBodySize: true,
    useCloseRelative: true,
  },
  renderOnCandleCloseOnly: true,
  lookbackPeriod: 1440,
  normalize: true,
  smoothing: true,
  sensitivityMultiplier: 1.2,
  normalizationBuffer: 0,
  minSmoothingPeriod: 5,
  maxSmoothingPeriod: 20,
  adaptiveSmoothingFactor: 0.5,
  volumeWeighting: {
    enabled: true,
    weightFactor: 0.5,
  },
  colors: {
    // Added default colors to local fallback
    positive: "#00BFFF",
    negative: "#FF4500",
    neutral: "#777777",
  },
};
const perpCvdUpdateInterval = PERP_CVD_CONFIG.updateIntervalMs;

let perpCvdHistoricalData = [];

// You must provide priceData from your app's data source
let perpCvdPriceData = [];

// CRITICAL FIX: Function to clear PERP CVD data store when switching charts
function clearPerpCVDDataStore() {

  perpCvdPriceData = [];
  perpCvdData = null;
  perpCvdHistoricalData = [];
  isPerpCVDUpdating = false;
  
  // Clear throttling variables
  lastPerpCVDUpdateTime = 0;
  if (perpCVDUpdateDebounceTimer) {
    clearTimeout(perpCVDUpdateDebounceTimer);
    perpCVDUpdateDebounceTimer = null;
  }
}

window.PS = window.PS || {};
window.PS.setPerpCVDPriceData = function (newPriceData) {
  if (!newPriceData || !Array.isArray(newPriceData) || newPriceData.length === 0) {
    return;
  }
  // Deduplicate bars by time and validate time is a number
  const seenTimes = new Set();
  const filteredData = newPriceData.filter(bar => {
    let t = typeof bar.time === 'object' && bar.time !== null && typeof bar.time.getTime === 'function'
      ? Math.floor(bar.time.getTime() / 1000)
      : Number(bar.time);
    if (!Number.isFinite(t)) {
      logger.warn('[perpCvdDataStore] Skipping bar with invalid time:', bar);
      return false;
    }
    if (seenTimes.has(t)) return false;
    seenTimes.add(t);
    bar.time = t;
    return true;
  });
  if (filteredData.length === 0) return;
  perpCvdPriceData = filteredData;
  // Throttle updates to perpCvdUpdateData
  if (window.PS._throttledPerpCvdUpdateData) {
    window.PS._throttledPerpCvdUpdateData();
  } else {
    perpCvdUpdateData();
  }
  
  // Log the update for debugging
  const now = Math.floor(Date.now() / 1000);
  const barInterval = 300;
  const currentBarTime = Math.floor(now / barInterval) * barInterval;
  const closedBars = newPriceData.filter(bar => bar.time < currentBarTime);

};

// Implementation for setPerpCVD: updates perpCvdData and notifies listeners
window.PS = window.PS || {};
window.PS.setPerpCVD = function (newCvdData) {
  try {
    perpCvdData = newCvdData;
    perpCvdListeners.forEach(function (cb) {
      try {
        cb(perpCvdData);
      } catch (e) {
        window.errorHandler.handleError(e, "PERP CVD DataStore: Error in listener callback");
      }
    });
  } catch (error) {
    window.errorHandler.handleError(error, "PERP CVD DataStore: Error in setPerpCVD");
  }
};

function perpCvdCalculateAdjustedVolume(bar, prevBar) {
  if (!bar) return 0;
  const volume = bar.volume !== undefined && !isNaN(bar.volume) ? bar.volume : 0;
  if (volume === 0) return 0;
  let isBuyBar = true;
  if (
    PERP_CVD_CONFIG.volumeAdjustment.useCloseRelative &&
    prevBar &&
    prevBar.close !== undefined &&
    !isNaN(prevBar.close)
  ) {
    isBuyBar = bar.close >= prevBar.close;
  } else {
    isBuyBar = bar.close >= bar.open;
  }
  let adjustmentFactor = 1.0;
  if (PERP_CVD_CONFIG.volumeAdjustment.useBodySize) {
    const bodySize = Math.abs(bar.close - bar.open);
    const range = bar.high - bar.low;
    if (range > 0 && isFinite(bodySize) && isFinite(range)) {
      const bodySizePercent = bodySize / range;
      adjustmentFactor *= 0.7 + bodySizePercent * 0.6;
    }
  }
  if (PERP_CVD_CONFIG.volumeAdjustment.useWicks) {
    const totalRange = bar.high - bar.low;
    if (totalRange > 0 && isFinite(totalRange)) {
      const upperWick = bar.high - Math.max(bar.open, bar.close);
      const lowerWick = Math.min(bar.open, bar.close) - bar.low;
      if (isFinite(upperWick) && isFinite(lowerWick)) {
        if (isBuyBar) {
          const lowerWickPercent = lowerWick / totalRange;
          adjustmentFactor *= 1 + lowerWickPercent * 0.8;
        } else {
          const upperWickPercent = upperWick / totalRange;
          adjustmentFactor *= 1 + upperWickPercent * 0.8;
        }
      }
    }
  }
  adjustmentFactor = Math.max(0.5, Math.min(2.0, adjustmentFactor));
  return isBuyBar
    ? volume * adjustmentFactor * PERP_CVD_CONFIG.volumeAdjustment.buyMultiplier
    : -volume * adjustmentFactor * PERP_CVD_CONFIG.volumeAdjustment.sellMultiplier;
}

function perpCvdCalculateCVDData(priceData) {
  if (!Array.isArray(priceData) || priceData.length === 0) {
    return [];
  }
  
  const cvdData = [];
  let cumulativeDelta = 0;
  
  try {
    for (let i = 0; i < priceData.length; i++) {
      const bar = priceData[i];
      if (!bar || !Number.isFinite(bar.time) || bar.volume === undefined) {
        if (bar && !Number.isFinite(bar.time)) {
          logger.warn('[perpCvdDataStore] Skipping bar with invalid time in perpCvdCalculateCVDData:', bar);
        }
        continue;
      }
      const prevBar = i > 0 ? priceData[i - 1] : null;
      const barDelta = perpCvdCalculateAdjustedVolume(bar, prevBar);
      cumulativeDelta += barDelta;
      cvdData.push({ time: bar.time, value: cumulativeDelta });
    }
  } catch (error) {
    window.errorHandler.handleError(error, "PERP CVD DataStore: Error in perpCvdCalculateCVDData");
  }
  
  return cvdData;
}

function perpCvdNormalize(value, min, max, opts) {
  return window.mathUtils?.normalize ? 
    window.mathUtils.normalize(value, min, max, opts || { range: [-1, 1] }) :
    (opts = opts || { range: [-1, 1] }, max === min ? 0 : opts.range[0] + ((value - min) * (opts.range[1] - opts.range[0])) / (max - min));
}

function perpCvdComputeRollingMinMax(data, window, accessor) {
  if (!Array.isArray(data) || data.length === 0) {
    logger.warn("perpCvdComputeRollingMinMax: Invalid or empty data array");
    return { minValues: [], maxValues: [] };
  }

  accessor =
    accessor ||
    function (d) {
      return d.value;
    };
  const minValues = [];
  const maxValues = [];

  try {
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - window + 1);
      const windowSlice = data.slice(start, i + 1).map(accessor);
      
      if (windowSlice.length > 0) {
        const min = Math.min(...windowSlice);
        const max = Math.max(...windowSlice);
        minValues.push(min);
        maxValues.push(max);
      } else {
        minValues.push(0);
        maxValues.push(0);
      }
    }
  } catch (error) {
    window.errorHandler.handleError(error, "PERP CVD DataStore: Error in perpCvdComputeRollingMinMax");
  }

  return { minValues, maxValues };
}

function perpCvdGetLatestNormalized() {
  if (!perpCvdPriceData.length) return null;
  
  // Only use closed bars - filter out the current (open) bar
  const now = Math.floor(Date.now() / 1000);
  const barInterval = 300;
  const currentBarTime = Math.floor(now / barInterval) * barInterval;
  const closedBars = perpCvdPriceData.filter(bar => Number.isFinite(bar.time) && bar.time < currentBarTime);
  
  if (closedBars.length === 0) {
    return null;
  }
  
  const cvdDataArr = perpCvdCalculateCVDData(closedBars);
  perpCvdHistoricalData = cvdDataArr.slice();

  const minMax = perpCvdComputeRollingMinMax(
    cvdDataArr,
    1440,
    function (p) {
      return p.value;
    },
  );

  const lastIdx = cvdDataArr.length - 1;
  if (lastIdx < 0) return null;
  const last = cvdDataArr[lastIdx];
  const min = minMax.minValues[lastIdx];
  const max = minMax.maxValues[lastIdx];
  const normalizedValue = perpCvdNormalize(last.value, min, max);
  return {
    time: last.time,
    value: normalizedValue,
    rawValue: last.value,
  };
}

function perpCvdFetchOrCalculate() {
  return perpCvdGetLatestNormalized();
}

function perpCvdUpdateData() {
  if (isPerpCVDUpdating) return;
  isPerpCVDUpdating = true;

  try {
    const newData = perpCvdFetchOrCalculate();
    if (newData) {
      // Check if this is a new bar or real-time update
      const isNewBar = !perpCvdData || newData.time > perpCvdData.time;
      const isRealTimeUpdate = perpCvdData && newData.time === perpCvdData.time;
      
      if (isNewBar) {

      } else if (isRealTimeUpdate) {
        // Real-time update within the same bar - log less frequently
        const shouldLog = Math.random() < 0.1; // Only log 10% of real-time updates

      }
      
      perpCvdData = newData;
      perpCvdListeners.forEach(function (cb, index) {
        try {
          cb(perpCvdData);
          if (isNewBar) {
            logger.debug(`PERP CVD DataStore: Successfully notified listener ${index + 1}`);
          }
        } catch (e) {
          window.errorHandler.handleError(e, `[PerpCVD DataStore] Error in listener ${index + 1}`);
        }
      });
    } else {
      logger.debug(`PERP CVD DataStore: No new data calculated`);
    }
  } catch (e) {
    window.errorHandler.handleError(e, "[PerpCVD DataStore] Error in perpCvdUpdateData");
  } finally {
    isPerpCVDUpdating = false;
  }
}

window.PS = window.PS || {};
window.PS.subscribePerpCVD = function (cb) {
  if (typeof cb !== "function") {
    return function () {};
  }
  perpCvdListeners.push(cb);

  // Immediately send current data if available
  if (perpCvdData !== null) {
    try {
      cb(perpCvdData);
    } catch (e) {
      window.errorHandler.handleError(e, "[PerpCVD DataStore] Error calling new listener immediately");
    }
  }
  // Return unsubscribe function
  return function () {
    const idx = perpCvdListeners.indexOf(cb);
    if (idx !== -1) {
      perpCvdListeners.splice(idx, 1);
    } else {
      // This warning can be useful for debugging if unsubscribe issues arise.
      // console.warn("[PerpCVD DataStore] Attempted to unsubscribe a listener that was not found.");
    }
  };
};

window.PS = window.PS || {};
window.PS.getCurrentPerpCVD = function () {
  return perpCvdData;
};

// CRITICAL FIX: Expose clear function for chart switching
window.PS = window.PS || {};
window.PS.clearPerpCVDDataStore = clearPerpCVDDataStore;

// CRITICAL FIX: Listen for chart switching to clear data store
document.addEventListener("pairChanged", (event) => {
  const newPair = event.detail?.pair;
  if (newPair) {
    logger.debug(`PERP CVD DataStore: Chart switched to ${newPair}, clearing data store`);
    clearPerpCVDDataStore();
  }
});

// Periodic updates disabled - data updates are event-driven only

// Periodic updates disabled - using original subscription function

// Throttle function to prevent excessive updates
const throttledSetPerpCVDPriceData = (window.commonUtils?.throttle || window.utils?.throttle)((priceData) => {
  setPerpCVDPriceData(priceData);
}, 1000); // Increased from 500ms to 1000ms for better performance

// Use a unique variable name for debounce timeout
let perpCvdNotifyTimeout = null;
function debouncedNotifyListeners() {
  if (perpCvdNotifyTimeout) return;
  perpCvdNotifyTimeout = setTimeout(() => {
    perpCvdNotifyTimeout = null;
    perpCvdListeners.forEach(cb => cb());
  }, 100); // Notify at most every 100ms
}

// Add a throttled version of perpCvdUpdateData
window.PS._throttledPerpCvdUpdateData = (window.commonUtils?.throttle || window.utils?.throttle)(perpCvdUpdateData, 100);
