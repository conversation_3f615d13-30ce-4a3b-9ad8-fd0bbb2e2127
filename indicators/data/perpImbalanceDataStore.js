/**
 * perpImbalanceDataStore.js
 * Event-driven data store for Perpetual Imbalance indicator.
 * Provides subscription, update, and notification APIs, matching the CVD data store pattern.
 */

/**
 * Perp Imbalance Data Store
 * Manages Perpetual Imbalance data and subscriptions
 * Uses centralized logger and error handler for all logging and error reporting
 */

(function () {
  const logger = window.logger
    ? window.logger.createLogger("PerpImbalanceDataStore")
    : console;

  // PERP Imbalance Data Store - Manages PERP Imbalance calculations and real-time updates
  // Uses throttling to prevent excessive updates and improve performance

  // Use centralized throttle function from commonUtils

  const perpImbalanceListeners = [];
  let perpImbalanceData = null;
  let isPerpImbalanceUpdating = false;

  // STANDARDIZED: Add throttling variables to match other data stores
  let lastPerpImbalanceUpdateTime = 0;
  let perpImbalanceUpdateDebounceTimer = null;

  // Configuration - use centralized config with fallback
  const PERP_IMBALANCE_CONFIG = window.CONFIG?.perpImbalance || {
    updateIntervalMs: 1000,
    lookbackPeriod: 1440,
    colors: {
      positive: "#4CAF50",
      negative: "#F44336",
      neutral: "#9E9E9E",
    },
  };

  let perpImbalancePriceData = []; // Should be updated externally with alignedBybit and alignedBitstamp bars

  // CRITICAL FIX: Function to clear PERP Imbalance data store when switching charts
  function clearPerpImbalanceDataStore() {
    logger.debug(
      "PERP Imbalance DataStore: Clearing data store for chart switch",
    );
    perpImbalancePriceData = [];
    perpImbalanceData = null;
    isPerpImbalanceUpdating = false;

    // Clear throttling variables
    lastPerpImbalanceUpdateTime = 0;
    if (perpImbalanceUpdateDebounceTimer) {
      clearTimeout(perpImbalanceUpdateDebounceTimer);
      perpImbalanceUpdateDebounceTimer = null;
    }
  }

  window.PS = window.PS || {};

  /**
   * Set new price data for perp imbalance calculation.
   * Expects an object: { bybitBars: [...], bitstampBars: [...] }
   */
  window.PS.setPerpImbalancePriceData = function (newPriceData) {
    if (!newPriceData || typeof newPriceData !== "object") {
      return;
    }
    function dedupeAndValidate(bars) {
      const seenTimes = new Set();
      return (Array.isArray(bars) ? bars : []).filter((bar) => {
        let t =
          typeof bar.time === "object" &&
          bar.time !== null &&
          typeof bar.time.getTime === "function"
            ? Math.floor(bar.time.getTime() / 1000)
            : Number(bar.time);
        if (!Number.isFinite(t)) {
          logger.warn(
            "[perpImbalanceDataStore] Skipping bar with invalid time:",
            bar,
          );
          return false;
        }
        if (seenTimes.has(t)) return false;
        seenTimes.add(t);
        bar.time = t;
        return true;
      });
    }
    newPriceData.bybitBars = dedupeAndValidate(newPriceData.bybitBars);
    newPriceData.bitstampBars = dedupeAndValidate(newPriceData.bitstampBars);
    if (!newPriceData.bybitBars.length || !newPriceData.bitstampBars.length)
      return;
    perpImbalancePriceData = newPriceData;
    // Throttle updates to updatePerpImbalanceData
    if (window.PS._throttledPerpImbalanceUpdateData) {
      window.PS._throttledPerpImbalanceUpdateData();
    } else {
      updatePerpImbalanceData();
    }

    // Log the update for debugging
    const now = Math.floor(Date.now() / 1000);
    const barInterval = 300;
    const currentBarTime = Math.floor(now / barInterval) * barInterval;
    const closedBybitBars = (newPriceData.bybitBars || []).filter(
      (bar) => bar.time < currentBarTime,
    );
    const closedBitstampBars = (newPriceData.bitstampBars || []).filter(
      (bar) => bar.time < currentBarTime,
    );
    logger.debug(
      `PERP Imbalance: Data store updated with ${newPriceData.bybitBars?.length || 0} total Bybit bars, ${newPriceData.bitstampBars?.length || 0} total Bitstamp bars, ${closedBybitBars.length} closed Bybit bars, ${closedBitstampBars.length} closed Bitstamp bars`,
    );
  };

  // Use a unique variable name for debounce timeout
  let perpImbalanceNotifyTimeout = null;
  function debouncedNotifyListeners() {
    if (perpImbalanceNotifyTimeout) return;
    perpImbalanceNotifyTimeout = setTimeout(() => {
      perpImbalanceNotifyTimeout = null;
      perpImbalanceListeners.forEach((cb) => cb(perpImbalanceData));
    }, 100); // Notify at most every 100ms
  }

  /**
   * Main update function: calculates perp imbalance and notifies listeners.
   * Uses the same normalization and calculation logic as the indicator module.
   */
  function updatePerpImbalanceData() {
    if (isPerpImbalanceUpdating) return;
    isPerpImbalanceUpdating = true;

    try {
      const newData = fetchOrCalculatePerpImbalance();
      if (newData) {
        perpImbalanceData = newData;
        debouncedNotifyListeners();
      }
    } catch (e) {
      window.errorHandler.handleError(
        "[PerpImbalance DataStore] Error in updatePerpImbalanceData:",
        e,
      );
    } finally {
      isPerpImbalanceUpdating = false;
    }
  }

  /**
   * Calculate perp imbalance value for the latest closed bar.
   * Returns { time, value, rawValue, normalizedValue }
   */
  function fetchOrCalculatePerpImbalance() {
    try {
      // Expect perpImbalancePriceData = { bybitBars: [...], bitstampBars: [...] }
      const bybitBars = Array.isArray(perpImbalancePriceData.bybitBars)
        ? perpImbalancePriceData.bybitBars
        : [];
      const bitstampBars = Array.isArray(perpImbalancePriceData.bitstampBars)
        ? perpImbalancePriceData.bitstampBars
        : [];
      if (!bybitBars.length || !bitstampBars.length) {
        return null;
      }

      // Defensive: filter out bars with invalid time
      const bybitAligned = bybitBars.filter((bar) => Number.isFinite(bar.time));
      const bitstampAligned = bitstampBars.filter((bar) =>
        Number.isFinite(bar.time),
      );

      // Align bars by time
      const minLen = Math.min(bybitAligned.length, bitstampAligned.length);
      const bybitAlignedFinal = bybitAligned.slice(-minLen);
      const bitstampAlignedFinal = bitstampAligned.slice(-minLen);

      // Only use closed bars - filter out the current (open) bar
      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const currentBarTime = Math.floor(now / barInterval) * barInterval;
      const closedBybit = bybitAlignedFinal.filter(
        (bar) => bar.time < currentBarTime,
      );
      const closedBitstamp = bitstampAlignedFinal.filter(
        (bar) => bar.time < currentBarTime,
      );
      if (!closedBybit.length || !closedBitstamp.length) {
        return null;
      }

      // Calculate price flow and cumulative delta
      function priceFlow(arr) {
        return arr.map((bar) => {
          if (
            !bar ||
            typeof bar.close !== "number" ||
            typeof bar.open !== "number" ||
            typeof bar.volume !== "number"
          ) {
            return 0;
          }
          const priceChange = bar.close - bar.open;
          return priceChange * bar.volume;
        });
      }

      function cumulativeDelta(arr) {
        let cumulative = 0;
        return arr.map((value) => {
          cumulative += value;
          return cumulative;
        });
      }

      function rollingNormalize(data, lookbackPeriod = 20) {
        if (!Array.isArray(data) || data.length === 0) {
          return [];
        }

        const normalized = [];
        for (let i = 0; i < data.length; i++) {
          const start = Math.max(0, i - lookbackPeriod + 1);
          const window = data.slice(start, i + 1);
          const min = Math.min(...window);
          const max = Math.max(...window);
          const value = data[i];

          if (max === min) {
            normalized.push(0);
          } else {
            normalized.push(((value - min) / (max - min)) * 2 - 1);
          }
        }
        return normalized;
      }

      // Calculate imbalance
      const bybitFlow = priceFlow(closedBybit);
      const bitstampFlow = priceFlow(closedBitstamp);
      const bybitCumulative = cumulativeDelta(bybitFlow);
      const bitstampCumulative = cumulativeDelta(bitstampFlow);

      // Calculate the difference
      const imbalance = bybitCumulative.map((bybit, i) => {
        const bitstamp = bitstampCumulative[i] || 0;
        return bybit - bitstamp;
      });

      // Normalize the imbalance
      const normalizedImbalance = rollingNormalize(
        imbalance,
        PERP_IMBALANCE_CONFIG.lookbackPeriod,
      );

      // Get the latest values
      const lastIndex = normalizedImbalance.length - 1;
      if (lastIndex < 0) return null;

      const latestTime = closedBybit[lastIndex]?.time;
      const latestValue = normalizedImbalance[lastIndex];
      const latestRawValue = imbalance[lastIndex];

      return {
        time: latestTime,
        value: latestValue,
        rawValue: latestRawValue,
        normalizedValue: latestValue,
      };
    } catch (error) {
      window.errorHandler.handleError(
        "PERP Imbalance DataStore: Error in fetchOrCalculatePerpImbalance:",
        error,
      );
      return null;
    }
  }

  // For debugging/testing
  window.PS._perpImbalanceDataStore = {
    getData: () => perpImbalanceData,
    getPriceData: () => perpImbalancePriceData,
    listeners: perpImbalanceListeners,
  };

  // CRITICAL FIX: Expose clear function for chart switching
  window.PS = window.PS || {};
  window.PS.clearPerpImbalanceDataStore = clearPerpImbalanceDataStore;

  // CRITICAL FIX: Listen for chart switching to clear data store
  document.addEventListener("pairChanged", (event) => {
    const newPair = event.detail?.pair;
    if (newPair) {
      logger.debug(
        `PERP Imbalance DataStore: Chart switched to ${newPair}, clearing data store`,
      );
      clearPerpImbalanceDataStore();
    }
  });

  // Throttle function to prevent excessive updates
  const throttledSetPerpImbalancePriceData = (window.commonUtils?.throttle || window.utils?.throttle)((priceData) => {
    setPerpImbalancePriceData(priceData);
  }, 1000); // Increased from 500ms to 1000ms for better performance

  // Add a throttled version of updatePerpImbalanceData
  window.PS._throttledPerpImbalanceUpdateData = (
    window.throttle ||
    function (func, limit) {
      let inThrottle;
      return function () {
        if (!inThrottle) {
          func();
          inThrottle = true;
          setTimeout(() => (inThrottle = false), 100);
        }
      };
    }
  )(updatePerpImbalanceData, 100);

  // CRITICAL FIX: Add subscription function for chart components
  window.PS = window.PS || {};
  window.PS.subscribePerpImbalance = function (cb) {
    if (typeof cb !== "function") {
      return function () {};
    }
    perpImbalanceListeners.push(cb);

    // Immediately send current data if available
    if (perpImbalanceData !== null) {
      try {
        cb(perpImbalanceData);
      } catch (e) {
        window.errorHandler.handleError(
          "[PerpImbalance DataStore] Error calling new listener immediately:",
          e,
        );
      }
    }
    // Return unsubscribe function
    return function () {
      const idx = perpImbalanceListeners.indexOf(cb);
      if (idx !== -1) {
        perpImbalanceListeners.splice(idx, 1);
      }
    };
  };

  window.PS = window.PS || {};
  window.PS.getCurrentPerpImbalance = function () {
    return perpImbalanceData;
  };
})();
