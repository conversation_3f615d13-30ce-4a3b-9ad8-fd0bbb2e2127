/**
 * PerpImbalance Indicator Module
 * Now updates via API polling - WebSocket recovery logic removed
 */

/**
 // perpimbalance.js

 // Listen for Bybit data from Service Worker and dispatch to imbalance logic
 window.addEventListener('bybit-data', (event) => {
   try {
     const msg = JSON.parse(event.detail);
     if (msg.topic && msg.topic.startsWith('publicTrade') && Array.isArray(msg.data)) {
       // Loop through each trade in the data array
       for (const trade of msg.data) {
         if (typeof updateImbalanceBuffered === 'function') {
           updateImbalanceBuffered(trade);
         }
       }
     }
   } catch (e) {
     // Ignore non-JSON or unrelated messages
   }
 });
 * Perpetual Imbalance Indicator Module
 * Refactored to match the modular structure and integration patterns of perpcvd.js
 */

(function () {
  "use strict";

  // --- Tab visibility tracking and buffered chart update logic ---
  let isTabVisible = !document.hidden;
  let pendingImbalanceUpdateArgs = null;

  // Use unified visibility system from ConnectionManager
  window.addEventListener("visibility-restore", () => {
    isTabVisible = true;
    if (typeof window.triggerPerpImbalanceRedraw === "function") {
      window.triggerPerpImbalanceRedraw();
    }
  });
  
  window.addEventListener("visibility-hide", () => {
    isTabVisible = false;
  });

  // Listen for chart switching to clear pending updates
  document.addEventListener("pairChanged", (event) => {
    const newPair = event.detail?.pair;
    if (newPair && window.PS?.pendingPerpImbalanceUpdates) {

      window.PS.pendingPerpImbalanceUpdates.lastBarTime = 0;
      window.PS.pendingPerpImbalanceUpdates.lastImbalanceValue = 0;
      window.PS.pendingPerpImbalanceUpdates.pendingValue = 0;
      window.PS.pendingPerpImbalanceUpdates.hasUpdate = false;
      window.PS.pendingPerpImbalanceUpdates.lastRealTimeUpdate = 0;
      window.PS.pendingPerpImbalanceUpdates.lastTitleColor = null;
      window.PS.pendingPerpImbalanceUpdates.lastClearTime = Date.now(); // Track when updates were cleared
    }
  });

  // CRITICAL FIX: Add proper chart switching cleanup
  const originalSwitchPair = window.switchPairInternal;
  if (originalSwitchPair) {
    window.switchPairInternal = function(newPair) {
      // Clear PerpImbalance data before switching
      if (window.PS?.setPerpImbalancePriceData) {

        window.PS.setPerpImbalancePriceData({ bybitBars: [], bitstampBars: [] });
      }
      
      // Clear pending updates
      if (window.PS?.pendingPerpImbalanceUpdates) {
        window.PS.pendingPerpImbalanceUpdates.lastBarTime = 0;
        window.PS.pendingPerpImbalanceUpdates.lastImbalanceValue = 0;
        window.PS.pendingPerpImbalanceUpdates.pendingValue = 0;
        window.PS.pendingPerpImbalanceUpdates.hasUpdate = false;
        window.PS.pendingPerpImbalanceUpdates.lastRealTimeUpdate = 0;
        window.PS.pendingPerpImbalanceUpdates.lastTitleColor = null;
        window.PS.pendingPerpImbalanceUpdates.lastClearTime = Date.now();
      }
      
      // Call original function
      return originalSwitchPair.call(this, newPair);
    };
  }

  window.triggerPerpImbalanceRedraw = function () {
    if (pendingImbalanceUpdateArgs) {
      updateImbalance(...pendingImbalanceUpdateArgs);
      pendingImbalanceUpdateArgs = null;
    }
  };

  function updateImbalanceBuffered(...args) {
    if (isTabVisible) {
      updateImbalance(...args);
      pendingImbalanceUpdateArgs = null;
    } else {
      pendingImbalanceUpdateArgs = args;
    }
  }
  // --------------------------------------------------------------

  // --- Config ---
  const IMBALANCE_CONFIG = window.PS?.IMBALANCE_CONFIG ||
    window.CONFIG?.perpImbalance || {
      lookbackPeriod: 1440,
      updateIntervalMs: 1000,
      colors: {
        positive: "#4CAF50",
        negative: "#F44336",
        neutral: "#9E9E9E",
      },
    };

  window.PS = window.PS || {};
  window.PS.IMBALANCE_CONFIG = IMBALANCE_CONFIG;

  // --- State ---
  let historicalImbalanceData = [];
  let perpImbalanceNormalizationWindow = [];
  let lastRenderTime = 0;
  let renderRequestId = null;

  // --- Utility Functions ---
  function getNormalizedColor(normalizedValue) {
    if (normalizedValue > 0.5) {
      return "rgba(255, 0, 0, 0.8)"; // red for strong positive
    } else if (normalizedValue < -0.5) {
      return "rgba(0, 255, 255, 0.8)"; // cyan for strong negative
    } else {
      return "rgba(170, 170, 170, 0.8)"; // gray for neutral
    }
  }

  function rollingNormalize(data, lookbackPeriod = 20) {
    if (!Array.isArray(data) || data.length === 0) return [];
    return data.map((value, index) => {
      const start = Math.max(0, index - lookbackPeriod + 1);
      const window = data.slice(start, index + 1);
      const min = Math.min(...window);
      const max = Math.max(...window);
      if (max === min) return 0;
      const normalized = 2 * ((value - min) / (max - min)) - 1;
      return isFinite(normalized) ? normalized : 0;
    });
  }

  function normalizeImbalance(value, min, max) {
    if (
      !isFinite(value) ||
      !isFinite(min) ||
      !isFinite(max) ||
      min === undefined ||
      max === undefined ||
      min === max
    ) {
      return 0;
    }
    const normalized = 2 * ((value - min) / (max - min)) - 1;
    return isFinite(normalized) ? normalized : 0;
  }

  // --- Chart Creation ---
  function createImbalanceChart(container, priceChart) {
    let pane;
    try {
      const panes = priceChart.panes && priceChart.panes();
      if (panes && panes.length > 1) {
        pane = panes[1];
        pane.applyOptions({ visible: true });
        if (typeof pane.setHeight === "function") {
          pane.setHeight(150);
        }
      }
    } catch (e) {}

    const imbalanceSeries = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: {
          type: "volume",
          precision: 2,
          minMove: 0.01,
          formatter: (price) => price?.toFixed(2),
        },
        color: "rgba(170, 170, 170, 0.8)",
        lineWidth: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "PERP IMB.",
        titleColor: "rgba(170, 170, 170, 0.8)",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
        autoscaleInfoProvider: () => ({
          priceRange: { minValue: -1.05, maxValue: 1.05 },
          margins: { above: 5, below: 5 },
        }),
      },
      1,
    );

    const zeroLine = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "#444444",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const level1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "rgba(170, 170, 170, 0.8)",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    const levelMinus1Line = priceChart.addSeries(
      LightweightCharts.LineSeries,
      {
        priceFormat: { type: "volume" },
        color: "rgba(170, 170, 170, 0.8)",
        lineWidth: 1,
        lineStyle: 2,
        lastValueVisible: false,
        priceLineVisible: false,
        title: "",
        pointsVisible: false,
        lastPriceAnimation: 0,
        crosshairMarkerVisible: false,
      },
      1,
    );

    priceChart.applyOptions({
      layout: {
        background: { color: "rgba(15, 20, 26, 1.0)", type: "solid" },
        panes: {
          separatorColor: "#2A2A2A",
          separatorHoverColor: "rgba(255, 0, 0, 0.1)",
          enableResize: true,
        },
      },
    });

    try {
      const chartContainer = container.querySelector(".tv-lightweight-charts");
      if (chartContainer) {
        chartContainer.style.backgroundColor = "rgba(15, 20, 26, 1.0)";
      }
    } catch (e) {}

    return {
      chart: priceChart,
      pane: pane,
      series: imbalanceSeries,
      zeroLine: zeroLine,
      referenceLines: { level1: level1Line, levelMinus1: levelMinus1Line },
    };
  }

  // --- Data Initialization ---
  function initializeImbalanceData(components, bybit5m, bitstamp5m) {
    if (!bybit5m?.length || !bitstamp5m?.length) {
      return { imbalanceData: [], imbalanceMAData: [] };
    }

    try {
      // Align bars by time and use only closed bars
      const now = Math.floor(Date.now() / 1000);
      const barInterval = 300;
      const currentBarTime = Math.floor(now / barInterval) * barInterval;

      // Align by time
      const minLen = Math.min(bybit5m.length, bitstamp5m.length);
      let bybitAligned = bybit5m.slice(-minLen);
      let bitstampAligned = bitstamp5m.slice(-minLen);

      // Only use bars that are definitely closed
      bybitAligned = bybitAligned.filter((bar) => bar.time < currentBarTime);
      bitstampAligned = bitstampAligned.filter(
        (bar) => bar.time < currentBarTime,
      );

      if (bybitAligned.length === 0 || bitstampAligned.length === 0) {
        // console.error("PERP IMBALANCE: No valid closed bars available");
        return { imbalanceData: [], imbalanceMAData: [] };
      }

      // Calculate price flow and cumulative delta
      function priceFlow(arr) {
        return arr.map((bar) => {
          if (
            !bar ||
            typeof bar.close !== "number" ||
            typeof bar.open !== "number" ||
            typeof bar.volume !== "number"
          ) {
            return 0;
          }
          return (bar.close - bar.open) * bar.volume;
        });
      }
      function cumulativeDelta(arr) {
        let sum = 0;
        return arr.map((v) => {
          if (isNaN(v)) v = 0;
          return (sum += v);
        });
      }

      const pfBybit = priceFlow(bybitAligned);
      const pfBitstamp = priceFlow(bitstampAligned);
      const cdBybit = cumulativeDelta(pfBybit);
      const cdBitstamp = cumulativeDelta(pfBitstamp);
      const diff = cdBybit.map((v, i) => v - cdBitstamp[i]);

      // Rolling normalization (match perpcvd.js)
      perpImbalanceNormalizationWindow = diff.slice(-1440);
      if (perpImbalanceNormalizationWindow.length > 0) {

      }

      const normalizedValues = window.mathUtils.rollingNormalize
        ? window.mathUtils.rollingNormalize(diff, 1440)
        : rollingNormalize(diff, 1440);

      const imbalanceData = bybitAligned.map((bar, i) => {
        const normalizedValue = normalizedValues[i];
        const color = getNormalizedColor(normalizedValue);
        return {
          time: bar.time,
          value: isFinite(normalizedValue) ? normalizedValue : 0,
          color: color,
        };
      });

      // Store last historical bar time for websocket continuation
      const lastHistoricalBar = imbalanceData[imbalanceData.length - 1];
      if (!window.PS) window.PS = {};
      if (!window.PS.pendingPerpImbalanceUpdates) {
        window.PS.pendingPerpImbalanceUpdates = {
          lastBarTime: 0,
          pendingValue: 0,
          hasUpdate: false,
          lastImbalanceValue: 0,
          normalizedData: [],
          priceData: [],
          avgImbalance: 0,
          maxHistoryLength: 1000,
        };
      }
      const pendingImbalanceUpdates = window.PS.pendingPerpImbalanceUpdates;

      const emptyMAData = imbalanceData.map((point) => ({
        time: point.time,
        value: 0,
      }));
      const zeroLineData = [];

      if (bybitAligned.length > 0) {
        const firstTime = bybitAligned[0].time;
        const lastTime = bybitAligned[bybitAligned.length - 1].time;
        zeroLineData.push({ time: firstTime, value: 0 });
        zeroLineData.push({ time: lastTime, value: 0 });

        const lastDataPoint = imbalanceData[imbalanceData.length - 1];
        if (lastDataPoint) {
          pendingImbalanceUpdates.lastBarTime = lastDataPoint.time;
          pendingImbalanceUpdates.pendingValue = lastDataPoint.value;
          pendingImbalanceUpdates.pendingEmaValue = 0;
          pendingImbalanceUpdates.lastImbalanceValue = lastDataPoint.value;
          pendingImbalanceUpdates.hasUpdate = false;

        }
      } else {
        const now = Math.floor(Date.now() / 1000);
        zeroLineData.push({ time: now - 86400, value: 0 });
        zeroLineData.push({ time: now, value: 0 });
      }

      try {
        components.series.setData(imbalanceData);
        
        // Set initial title color based on the last data point
        if (imbalanceData.length > 0) {
          const lastPoint = imbalanceData[imbalanceData.length - 1];
          components.series.applyOptions({
            titleColor: lastPoint.color
          });
        }
        
        components.zeroLine.setData(zeroLineData);
        if (components.referenceLines.level1)
          components.referenceLines.level1.setData(
            zeroLineData.map((d) => ({ ...d, value: 1 })),
          );
        if (components.referenceLines.levelMinus1)
          components.referenceLines.levelMinus1.setData(
            zeroLineData.map((d) => ({ ...d, value: -1 })),
          );
      } catch (e) {
        // console.warn("PERP IMBALANCE: Error during chart update:", e);
      }

      // CRITICAL FIX: Subscribe to data store updates for real-time chart updates
      if (window.PS?.subscribePerpImbalance && typeof window.PS.subscribePerpImbalance === 'function') {
        // Store the unsubscribe function for cleanup
        components.unsubscribePerpImbalance = window.PS.subscribePerpImbalance((newData) => {
          if (newData && components.series && !components.series._internal_isDisposed) {
            try {
              // Update the chart with new data
              const color = getNormalizedColor(newData.value);
              components.series.applyOptions({
                titleColor: color
              });
              
              // Add the new data point to the series
              components.series.update({
                time: typeof newData.time === 'object' && newData.time !== null && typeof newData.time.getTime === 'function'
                  ? Math.floor(newData.time.getTime() / 1000)
                  : Number(newData.time),
                value: newData.value,
                color: color
              });
              

            } catch (e) {
              // Handle "Cannot update oldest data" error gracefully
              if (e.message && e.message.includes('Cannot update oldest data')) {

                return;
              }
              console.warn("PERP IMBALANCE: Error updating chart with new data:", e);
            }
          }
        });
        

      } else {
        console.warn("PERP IMBALANCE: Data store subscription function not available");
      }

      // Mark historical data as loaded and ready for websocket continuation
      if (!window.PS) window.PS = {};
      window.PS.perpImbalanceHistoricalLoaded = true;


      return { imbalanceData: imbalanceData, imbalanceMAData: emptyMAData };
    } catch (e) {
      return { imbalanceData: [], imbalanceMAData: [] };
    }
  }

  // --- Chart Synchronization (stub for now) ---
  function synchronizeCharts(components, priceChart) {
    return {};
  }

  // --- Cleanup ---
  function cleanupImbalance(components) {
    if (renderRequestId) {
      try {
        cancelAnimationFrame(renderRequestId);
      } catch (e) {

      }
      renderRequestId = null;
    }

    // CRITICAL FIX: Unsubscribe from data store updates
    if (components?.unsubscribePerpImbalance && typeof components.unsubscribePerpImbalance === 'function') {
      try {
        components.unsubscribePerpImbalance();

      } catch (e) {
        console.warn("PERP IMBALANCE: Error unsubscribing from data store:", e);
      }
    }

    // Remove visibility event listener and clear buffer
    document.removeEventListener("visibilitychange", () => {});
    pendingImbalanceUpdateArgs = null;

    if (window.IndicatorChartUtils) {
      window.IndicatorChartUtils.cleanupIndicator(components, [], {});
    }
  }

  // --- Resize Handler (stub for now) ---
  function resizeImbalanceChart(components, w, h) {
    if (components?.chart && typeof components.chart.resize === "function") {
      components.chart.resize(w, h);
    }
  }

  // --- Render Pending Updates (stub for now) ---
  function renderPendingImbalanceUpdates(components) {
    // Removed - updates happen directly in subscription
  }

  // Enhanced data processing with transition state awareness
  function processPerpImbalanceData(newData) {
    // CRITICAL FIX: Check transition state before processing
    if (window.chartTransitionState && window.chartTransitionState.isTransitioning) {
      return; // Skip processing during chart transitions
    }
    
    try {
      if (!newData || !newData.time || !newData.value) {
        console.warn('PERP IMBALANCE: Invalid data received:', newData);
        return;
      }
      
      // Enhanced data validation
      const timestamp = newData.time * 1000;
      const now = Date.now();
      
      // Skip data that's too old (more than 5 seconds)
      if (now - timestamp > 5000) {

        return;
      }
      
      // Process the data
      updatePerpImbalanceChart(newData);
      
    } catch (error) {
      console.warn('PERP IMBALANCE: Error processing data:', error);
    }
  }

  // --- Module Exports ---
  window.perpImbalanceModule = {
    createImbalanceChart,
    initializeImbalanceData,
    synchronizeCharts,
    cleanupImbalance,
    resizeImbalanceChart,
    renderPendingImbalanceUpdates,
    // WebSocket functions removed - indicators now update via API polling
    getNormalizedColor,
    rollingNormalize,
    normalizeImbalance,
    // For debugging/testing:
    _state: {
      perpImbalanceNormalizationWindow,
      // perpImbalanceHistory removed
    },
    // Test function to manually trigger updates
    testUpdate: function() {
      console.log("PERP IMBALANCE: Manual test update triggered");
      if (window.PS && window.PS._perpImbalanceDataStore) {
        const store = window.PS._perpImbalanceDataStore;
        console.log("PERP IMBALANCE: Data store status:", {
          hasData: !!store.getData(),
          data: store.getData(),
          pending: store.getPending(),
          priceData: store.getPriceData(),
          listeners: store.listeners.length
        });
      }
      
      // Force a data store update
      if (window.PS && window.PS.setPerpImbalancePriceData) {
        const testData = {
          bybitBars: [{ time: Math.floor(Date.now() / 300) * 300, open: 50000, close: 50100, high: 50200, low: 49900, volume: 100 }],
          bitstampBars: [{ time: Math.floor(Date.now() / 300) * 300, open: 50000, close: 50050, high: 50100, low: 49950, volume: 100 }]
        };
        console.log("PERP IMBALANCE: Triggering test data update:", testData);
        window.PS.setPerpImbalancePriceData(testData);
      }
    },
    // WebSocket recovery functions removed - no longer needed
    // forceRecovery: function() { ... },
    // checkStatus: function() { ... },
    // testValidationLogic: function() { ... }
  };
})();
